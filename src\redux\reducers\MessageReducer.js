import Types from '../types';
const initState = {
    enableFlg: false,
    content: ''
}

const MessageReducer = (state = initState, action) => {
    const {type, payload} = action;
    
    switch (type) {
        case Types.WARNING_MESSAGE:
            return {
                ...state,
                enableFlg: payload.enableFlg,
                content: payload.content
            }
        default:
            return state;
    }
};

export default MessageReducer;
