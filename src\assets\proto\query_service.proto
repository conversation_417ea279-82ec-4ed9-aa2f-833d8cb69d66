syntax = "proto3";
import "trading_model.proto";
import "system_model.proto";
import "query_model.proto";

//Get Order List
message GetOrderRequest {
  string symbol_code = 1;
  int64 account_id = 2;
  Side side = 3;
}

message GetOrderResponse {
	MsgCode msg_code = 1;
	string msg_text = 2;
  	repeated Order order = 3;
  	int64 account_id = 4;
}

//Get Contract List
message GetContractRequest {
  string symbol_code = 1;
  int64 account_id = 2;
}

message GetContractResponse {
	MsgCode msg_code = 1;
	string msg_text	= 2;
	repeated Contract contract = 3;
	int64 account_id = 4;
}

//Get Position List
message GetPositionRequest {
  int64 account_id = 1;
  string symbol_code = 2;
}

message GetPositionResponse {
	MsgCode msg_code = 1;
	string msg_text	= 2;
	repeated Position contract = 3;
	string symbol_code = 4;
	int64 account_id = 5;
}

//Get Symbol List
message SymbolListRequest {
  int64 account_id = 1;
}

message SymbolListResponse {
	MsgCode msg_code = 1;
	string msg_text	= 2;
	repeated Symbol symbol = 3;
	int64 account_id = 4;
}

//Get Trade History
message GetTradeHistoryRequest {
	string symbol_code = 1;
	OrderType order_type = 2;
    int64 from_datetime = 3; // Timestamp in milliseconds
    int64 to_datetime = 4; // Timestamp in milliseconds
    int64 account_id = 5;
	Side side = 6;
}

message GetTradeHistoryResponse {
	MsgCode msg_code = 1;
	string msg_text = 2;
	repeated Trade trade = 3;
	int64 account_id =4;
}

//Get Order History
message GetOrderHistoryRequest {
	string symbol_code = 1;
	OrderType order_type = 2;
    int64 from_datetime = 3; // Timestamp in milliseconds
    int64 to_datetime = 4; // Timestamp in milliseconds
	OrderState order_state = 5;
	int64 account_id = 6;
	Side side = 7;
	string pspl_account_no = 8; //Send accountNo when request order history in remisier
}

message GetOrderHistoryResponse {
	MsgCode msg_code = 1;
	string msg_text = 2;
	repeated Order order = 3;
	int64 account_id =4;
}

// Update symbols info in trading core
message SymbolUpdateRequest {
	repeated Symbol symbols = 1;
}

message SymbolUpdateResponse {
	// TODO: Check response detail
	MsgCode msg_code = 1;
	string msg_text	= 2;
}

message GetOrderStatusRequest {
	int64 account_id = 1;
	string symbol_code = 2;
	string order_id = 3;
}

message GetOrderStatusResponse {
	MsgCode msg_code = 1;
	string msg_text = 2;
	Order order = 3;
}


