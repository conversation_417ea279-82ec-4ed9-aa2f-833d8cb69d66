import { memo, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { isEqual } from 'lodash';
import { Table, AutoSizer, CellMeasurerCache, CellMeasurer, Column, SortDirection } from 'react-virtualized';

import { wsService } from '../services/websocket-service';
import { SymbolStatus } from '../model/proto/query_model_pb'

import { ORDER_SIDE, DIGIT_DEFAULT, LocalStorageKey } from '../model/constant';
import {
    calcChange, calcPctChange, checkValue, getLastBidsElement,
    getLastAsksElement, getPriceAfterSpread, formatCurrencyDecimal, convertNumber, calcCeilFloor
} from '../model/utils';
import { DEFAULT_HEIGHT_ROW, DEFAULT_SCREEN_WIDTH, SCREEN_SIZE_WEB_MIN } from '../constants/general';

import './component.styles.css';

const renderIntervalTime = window.renderIntervalTime;

const MarketList = (props) => {
    const { handleFocusSymbol, symbols } = props;
    const { t } = useTranslation();
    const [lastQuotes, setLastQuotes] = useState([]);
    const [screenSize, setScreenSize] = useState(window.innerWidth);
    const [tickerCode, setTickerCode] = useState('');
    const [quoteMap, setQuoteMap] = useState(new Map());
    const [symbolMap, setSymbolMap] = useState(new Map());
    const [listData, setListData] = useState([]);

    const [isSortByName, setSortByName] = useState(false);
    const [isNameAsc, setNameAsc] = useState(false);

    // Default list data is sorted by Symbol but we don't show icon, only show when click
    const [isSortBySymbol, setSortBySymbol] = useState(false);
    const [isSymbolAsc, setSymbolAsc] = useState(true);

    const [isRunningInterval, setIsRunningInterval] = useState(false);

    const [sortBy, setSortBy] = useState('symbolCode');
    const [sortDirection, setSortDirection] = useState(SortDirection.ASC);
    
    const [rowNum, setRowNum] = useState(0);

    const theme = sessionStorage.getItem(LocalStorageKey.THEME);
    const iconByTheme = theme === "light" ? "" : "-fill text-white";

    const tableRef = useRef();
    const tableBodyRef = useRef();
    const renderIntervalRef = useRef();
    const tickerCodeRef = useRef();
    const cache = useRef(new CellMeasurerCache({
        fixedWidth: true,
        defaultHeight: DEFAULT_HEIGHT_ROW
    }))

    const rowClassName = ({ index }) => {
        if (index === -1) {
            return "p-2"
        }
        if (sortedData()[index]?.symbolCode === tickerCode) {
            return "p-2 bg_active"
        } else if(index % 2 === 1){
            return "p-2 row-even"
        }else{
            return "p-2"
        }
    }

    useEffect(() => {
        tickerCodeRef.current = tickerCode;
    },[tickerCode])

    useEffect(() => {
        const symbolListResponse = wsService.getSymbolListResponse().subscribe(symbols => {
            if (symbols && symbols.symbolList) {
                symbols.symbolList.forEach((symbol) => {
                    if (symbol.symbolStatus === SymbolStatus.SYMBOL_ACTIVE) {
                        symbolMap.set(symbol.symbolCode, symbol);
                    }
                });
            }
        });

        const lastQuoteResponse = wsService.getLastquoteResponse().subscribe(quotes => {
            if (quotes && quotes.quotesList) {
                setLastQuotes(quotes.quotesList);
            }
        });

        const getQuoteEventResponse = wsService.getQuoteEventResponse().subscribe(quotes => {
            if (quotes && quotes.quoteList) {
                processQuoteEvent(quotes.quoteList);
            }
        });

        window.addEventListener('resize', () => setScreenSize(window.innerWidth));

        return () => {
            lastQuoteResponse.unsubscribe();
            getQuoteEventResponse.unsubscribe();
            symbolListResponse.unsubscribe();
            window.removeEventListener('resize', () => setScreenSize(window.innerWidth));
        }
    }, [])

    useEffect(() => {
        processLastQuotes(lastQuotes);
    }, [lastQuotes, symbols])

    useEffect(() => {
        if (isRunningInterval) {
            renderIntervalRef.current = setInterval(() => {
                setUpDataDisplay();
            }, renderIntervalTime);
        } else {
            clearInterval(renderIntervalRef.current);
            renderIntervalRef.current = null;
        }
    }, [isRunningInterval]);

    const setUpDataDisplay = () => {
        if (quoteMap.size > 0) {
            setListData(Array.from(quoteMap.values()));
        }
    }

    const processQuoteEvent = (quotesEvent) => {
        if (quotesEvent && quotesEvent.length > 0) {
            setIsRunningInterval(true);
            quotesEvent.forEach(quote => {
                const lastBids = getLastBidsElement(quote);
                const lastAsks = getLastAsksElement(quote);
                let quoteUpdate = quoteMap.get(quote.symbolCode)
                if (quoteUpdate) {
                    const { ceiling, floor } = calcCeilFloor(convertNumber(quote.retailPrice), quoteUpdate, symbolMap);
                    const symbolDetail = symbolMap.get(quote.symbolCode)
                    quoteUpdate = {
                        ...quoteUpdate,
                        ceiling: ceiling,
                        floor: floor,
                        asksList: quote.asksList,
                        bidsList: quote.bidsList,
                        currentPrice: quote.retailPrice ? quote.retailPrice : quoteUpdate.currentPrice,
                        high: quote.retailHigh ? quote.retailHigh : quoteUpdate.high,
                        low: quote.retailLow ? quote.retailLow : quoteUpdate.low,
                        open: quote.retailOpen ? quote.retailOpen : quoteUpdate.open,
                        tickPerDay: checkValue(quoteUpdate.tickPerDay, quote.tickPerDay),
                        volumePerDay: checkValue(quoteUpdate.volumePerDay, quote.volumePerDay),
                        bidSize: lastBids ? formatCurrencyDecimal(lastBids.lpVolume) : '-',
                        bidPrice: lastBids ? formatCurrencyDecimal(getPriceAfterSpread(lastBids.price, ORDER_SIDE.BUY, symbolDetail), DIGIT_DEFAULT) : '-',
                        askSize: lastAsks ? formatCurrencyDecimal(lastAsks.lpVolume) : '-',
                        askPrice: lastAsks ? formatCurrencyDecimal(getPriceAfterSpread(lastAsks.price, ORDER_SIDE.SELL, symbolDetail), DIGIT_DEFAULT) : '-'
                    }
                    if (!isEqual(quoteMap.get(quote.symbolCode), quoteUpdate)) {
                        quoteMap.set(quote.symbolCode, quoteUpdate)
                    }
                } else {
                    const symbolDetail = symbolMap.get(quote.symbolCode)
                    if (symbolDetail) {
                        const quoteUpdate = {
                            symbolCode: symbolDetail.symbolCode,
                            symbolId: symbolDetail.symbolId,
                            symbolName: symbolDetail.symbolName,
                            prevClosePrice: symbolDetail.prevClosePrice,
                            ceiling: symbolDetail.ceiling,
                            floor: symbolDetail.floor,
                            productType: symbolDetail.productType,
                            asksList: [],
                            bidsList: [],
                            currentPrice: '0.00',
                            high: '0.00',
                            low: '0.00',
                            open: '0.00',
                            tickPerDay: '0',
                            volumePerDay: '0',
                            bidSize: '-',
                            bidPrice: '-',
                            askSize: '-',
                            askPrice: '-'
                        }
                        quoteMap.set(symbolDetail.symbolCode, quoteUpdate);
                    }
                }
            });
        }
    }

    const processLastQuotes = (lastQuote) => {
        if (lastQuote && lastQuote.length > 0) {
            lastQuote.forEach(quote => {
                const symbolDetail = symbolMap.get(quote.symbolCode);
                if (symbolDetail) {
                    const lastBids = getLastBidsElement(quote);
                    const lastAsks = getLastAsksElement(quote);
                    const { ceiling, floor } = calcCeilFloor(convertNumber(quote.retailPrice), symbolDetail, symbolMap);
                    const obj = {
                        symbolCode: symbolDetail.symbolCode,
                        symbolId: symbolDetail.symbolId,
                        symbolName: symbolDetail.symbolName,
                        prevClosePrice: symbolDetail.prevClosePrice,
                        ceiling: ceiling,
                        floor: floor,
                        productType: symbolDetail.productType,
                        asksList: quote.asksList,
                        bidsList: quote.bidsList,
                        currentPrice: quote.retailPrice,
                        high: quote.retailHigh,
                        low: quote.retailLow,
                        open: quote.retailOpen,
                        tickPerDay: quote.tickPerDay,
                        volumePerDay: quote.volumePerDay,
                        bidSize: lastBids ? formatCurrencyDecimal(lastBids.lpVolume) : '-',
                        bidPrice: lastBids ? formatCurrencyDecimal(getPriceAfterSpread(lastBids.price, ORDER_SIDE.BUY, symbolDetail), DIGIT_DEFAULT) : '-',
                        askSize: lastAsks ? formatCurrencyDecimal(lastAsks.lpVolume) : '-',
                        askPrice: lastAsks ? formatCurrencyDecimal(getPriceAfterSpread(lastAsks.price, ORDER_SIDE.SELL, symbolDetail), DIGIT_DEFAULT) : '-'
                    };
                    quoteMap.set(quote.symbolCode, obj);
                }
            })
        } else {
            if (symbols.length > 0) {
                symbols.forEach(symbol => {
                    const quote = {
                        symbolCode: symbol.symbolCode,
                        symbolId: symbol.symbolId,
                        symbolName: symbol.symbolName,
                        prevClosePrice: symbol.prevClosePrice,
                        ceiling: symbol.ceiling,
                        floor: symbol.floor,
                        productType: symbol.productType,
                        asksList: [],
                        bidsList: [],
                        currentPrice: '0.00',
                        high: '0.00',
                        low: '0.00',
                        open: '0.00',
                        tickPerDay: '0',
                        volumePerDay: '0',
                        bidSize: '-',
                        bidPrice: '-',
                        askSize: '-',
                        askPrice: '-'
                    };
                    quoteMap.set(symbol.symbolCode, quote);
                });
            }
        }
        const data = Array.from(quoteMap.values())
        const sortData = data.sort((a, b) => a?.symbolCode?.localeCompare(b?.symbolCode));
        if (sortData.length > 0) {
            setTickerCode(sortData[0].symbolCode);
            handleFocusSymbol(sortData[0]);
        }
        setUpDataDisplay()
    }

    const handleClickSymbol = (event) => {
        const data = event.rowData;
        setTickerCode(data.symbolCode);
        handleFocusSymbol(data);
        if (screenSize < SCREEN_SIZE_WEB_MIN) {
            window.scrollTo(0, document.body.scrollHeight || document.documentElement.scrollHeight);
        }
    }

    const sort = ({ sortBy, sortDirection }) => {
        if (sortBy === "symbolCode" || sortBy === "symbolName") {
            setSortBy(sortBy);
            setSortDirection(sortDirection);
        }
        if (sortBy === "symbolName") {
            setNameAsc(!isNameAsc);
            setSortByName(true);
            setSymbolAsc(false);
            setSortBySymbol(false);
        }
        if (sortBy === "symbolCode") {
            setSymbolAsc(!isSymbolAsc);
            setSortBySymbol(true);
            setNameAsc(false);
            setSortByName(false);
        }
    };

    const sortedData = () => {
        if (sortBy) {
            return listData.sort((a, b) => {
                if (a[sortBy] > b[sortBy])
                    return sortDirection === SortDirection.ASC ? 1 : -1;
                if (a[sortBy] < b[sortBy])
                    return sortDirection === SortDirection.ASC ? -1 : 1;
                return 0;
            });
        } else {
            return listData;
        }
    };

    const rowHeight = ({ index }) => {
        if (sortedData()[index].symbolName.length > 25) {
            return 52;
        }
        return DEFAULT_HEIGHT_ROW;
    };


    const _renderTableData = () => (
        <div className="max-height-80">
            <AutoSizer  onResize={() => {
                cache.current.clearAll();
                // In case there is no quote event (interval render not working), the UI does not rerender while resizing => break UI
                if (!isRunningInterval) {
                  setUpDataDisplay();
                }
            }}>{({ width, height }) => {
                let responseWidth = width
                // Fix responsive when resize, we set default width to able to see full column
                if (width < DEFAULT_SCREEN_WIDTH) {
                    responseWidth = DEFAULT_SCREEN_WIDTH
                }
                return <Table
                    ref={tableBodyRef}
                    width={responseWidth}
                    height={height}
                    headerHeight={DEFAULT_HEIGHT_ROW}
                    rowHeight={rowHeight}
                    rowCount={listData.length}
                    deferredMeasurementCache={cache.current}
                    rowGetter={({ index }) => sortedData()[index]}
                    rowClassName={rowClassName}
                    onRowClick={(event) => handleClickSymbol(event)}
                    scrollToIndex={sortedData().findIndex(e=> e.symbolCode === tickerCode)}
                    sort={sort}
                    sortBy={sortBy}
                    sortDirection={sortDirection}
                    overscanRowCount={rowNum !== 0 ? rowNum : 25}
                    onScroll={() => {
                        const index = sortedData().findIndex((element) => element.symbolCode === tickerCode);
                        if (index !== -1) {
                            setRowNum(index);
                        }
                    }}
                    className='table'
                >
                    <Column
                        dataKey="symbolName" minWidth={200} width={200}
                        headerRenderer={() =>
                            <div className="cursor-pointer-header fw-bold">
                                <span className={`text-one-line ${isSortByName && "text-orange"}`}>{t('market.name')}</span>
                                {isSortByName && <i className={`bi bi-caret-${isNameAsc ? 'up' : 'down'}${iconByTheme}`} />}
                            </div>
                        }
                        cellRenderer={({ cellData, dataKey, parent, rowIndex }) =>
                            {
                                return (<CellMeasurer
                                    cache={cache.current}
                                    key={dataKey}
                                    parent={parent}
                                    rowIndex={rowIndex}>
                                    <div><strong>{cellData}</strong></div>
                                </CellMeasurer>)
                            }
                        }
                    />
                    <Column
                        dataKey="symbolCode" minWidth={60} width={60}
                        headerRenderer={() =>
                            <div className="text-center cursor-pointer-header fw-bold">
                                <span className={`text-one-line ${isSortBySymbol && "text-orange"}`}>{t('market.symbol')}</span>
                                {isSortBySymbol && <i className={`bi bi-caret-${isSymbolAsc ? 'up' : 'down'}${iconByTheme}`} />}
                            </div>
                        }
                        cellRenderer={({ cellData, dataKey, parent, rowIndex }) =>
                            <CellMeasurer
                                cache={cache.current}
                                key={dataKey}
                                parent={parent}
                                rowIndex={rowIndex}>
                                <div className="text-center">{cellData}</div>
                            </CellMeasurer>
                        }
                    />
                    <Column
                        dataKey="currentPrice"
                        minWidth={70}
                        width={70}
                        className='overflow-unset'
                        headerRenderer={() =>
                            <div className="text-end fw-bold">{t('market.last')}</div>
                        }
                        cellRenderer={({ cellData, dataKey, parent, rowIndex }) =>
                            <CellMeasurer
                                cache={cache.current}
                                key={dataKey}
                                parent={parent}
                                rowIndex={rowIndex}>
                                <div className="text-end mx-2">{formatCurrencyDecimal(cellData, DIGIT_DEFAULT)}</div>
                            </CellMeasurer>
                        }
                        
                    />
                    <Column
                        dataKey="change" 
                        minWidth={60}
                        width={60}
                        className='overflow-unset'
                        headerRenderer={() =>
                            <div className="text-end fw-bold">{t('market.chg')}</div>
                        }
                        cellRenderer={({ rowData, dataKey, parent, rowIndex }) => {
                            const { prevClosePrice, currentPrice } = rowData;
                            return (
                                <CellMeasurer
                                    cache={cache.current}
                                    key={dataKey}
                                    parent={parent}
                                    rowIndex={rowIndex}>
                                        <div className="text-end mx-2">
                                            {convertNumber(currentPrice) !== 0 && <span className={calcChange(currentPrice, prevClosePrice) >= 0 ? "text-success" : "text-danger"}>
                                                {formatCurrencyDecimal(calcChange(currentPrice, prevClosePrice), DIGIT_DEFAULT)}
                                            </span>}
                                            {convertNumber(currentPrice) === 0 && <span>-</span>}
                                        </div>
                                </CellMeasurer>
                            )}
                        }
                    />
                    <Column
                        dataKey="changePercent"
                        minWidth={60}
                        width={60}
                        className='overflow-unset'
                        headerRenderer={() =>
                            <div className="text-end fw-bold">{t('market.chgPercent')}</div>
                        }
                        cellRenderer={({ rowData ,dataKey, parent, rowIndex }) => {
                            const { prevClosePrice, currentPrice } = rowData;
                            return (
                                <CellMeasurer
                                    cache={cache.current}
                                    key={dataKey}
                                    parent={parent}
                                    rowIndex={rowIndex}>
                                        <div className="text-end mx-2">
                                            {convertNumber(currentPrice) !== 0 && <span className={calcPctChange(currentPrice, prevClosePrice) >= 0 ? "text-success" : "text-danger"}>
                                                {formatCurrencyDecimal(calcPctChange(currentPrice, prevClosePrice), DIGIT_DEFAULT)}
                                            </span>}
                                            {convertNumber(currentPrice) === 0 && <span>-</span>}
                                        </div>
                                </CellMeasurer>
                            )}
                        }
                    />
                    <Column
                        dataKey="bidSize"
                        className='overflow-unset'
                        minWidth={70}
                        width={70}
                        headerRenderer={() =>
                            <div className="text-end fw-bold">{t('market.bvol')}</div>
                        }
                        cellRenderer={({ cellData, dataKey, parent, rowIndex }) =>
                            <CellMeasurer
                                cache={cache.current}
                                key={dataKey}
                                parent={parent}
                                rowIndex={rowIndex}>
                                <div className="text-end mx-2">{cellData}</div>
                            </CellMeasurer>
                        }
                    />
                    <Column
                        dataKey="bidPrice"  
                        className='overflow-unset'
                        minWidth={70}
                        width={70}
                        headerRenderer={() =>
                            <div className="text-end fw-bold">{t('market.bid')}</div>
                        }
                        cellRenderer={({ cellData, dataKey, parent, rowIndex }) =>
                            <CellMeasurer
                                cache={cache.current}
                                key={dataKey}
                                parent={parent}
                                rowIndex={rowIndex}>
                                <div className="text-end mx-2">{cellData}</div>
                            </CellMeasurer>
                        }
                    />
                    <Column
                        dataKey="askPrice" 
                        className='overflow-unset'
                        minWidth={70}
                        width={70}
                        headerRenderer={() =>
                            <div className="text-end fw-bold">{t('market.ask')}</div>
                        }
                        cellRenderer={({ cellData, dataKey, parent, rowIndex }) => {
                            return (
                                <CellMeasurer
                                    cache={cache.current}
                                    key={dataKey}
                                    parent={parent}
                                    rowIndex={rowIndex}>
                                   <div className="text-end mx-2">{cellData}</div>
                                </CellMeasurer>
                            )
                        }}
                    />
                    <Column
                        dataKey="askSize" 
                        className='overflow-unset'
                        width={70}
                        minWidth={70}
                        headerRenderer={() =>
                            <div className="text-one-line text-end fw-bold">{t('market.svol')}</div>
                        }
                        cellRenderer={({ cellData, dataKey, parent, rowIndex }) =>
                            <CellMeasurer
                                cache={cache.current}
                                key={dataKey}
                                parent={parent}
                                rowIndex={rowIndex}>
                                <div className="text-end mx-1">{cellData}</div>
                            </CellMeasurer>
                        }
                    />
                    <Column
                        dataKey="volumePerDay"  
                        className='overflow-unset'
                        width={80}
                        minWidth={80}
                        headerRenderer={() =>
                            <div className="text-end fw-bold">{t('market.vol')}</div>
                        }
                        cellRenderer={({ cellData, dataKey, parent, rowIndex }) => {
                            return (
                                <CellMeasurer
                                    cache={cache.current}
                                    key={dataKey}
                                    parent={parent}
                                    rowIndex={rowIndex}>
                                   <div className="text-end mx-1">{formatCurrencyDecimal(cellData, 0)}</div>
                                </CellMeasurer>
                            )
                        }}
                    />
                    <Column
                        dataKey="open" 
                        className='overflow-unset'
                        width={70}
                        minWidth={70}
                        headerRenderer={() =>
                            <div className="text-end fw-bold">{t('market.open')}</div>
                        }
                        cellRenderer={({ cellData, dataKey, parent, rowIndex }) => {
                            return (
                                <CellMeasurer
                                    cache={cache.current}
                                    key={dataKey}
                                    parent={parent}
                                    rowIndex={rowIndex}>
                                   <div className="text-end mx-2">{formatCurrencyDecimal(cellData, DIGIT_DEFAULT)}</div>
                                </CellMeasurer>
                            )
                        }}
                    />
                    <Column
                        dataKey="high" 
                        className='overflow-unset'
                        width={70}
                        minWidth={70}
                        headerRenderer={() =>
                            <div className="text-end fw-bold">{t('market.high')}</div>
                        }
                        cellRenderer={({ cellData, dataKey, parent, rowIndex }) => {
                            return (
                                <CellMeasurer
                                    cache={cache.current}
                                    key={dataKey}
                                    parent={parent}
                                    rowIndex={rowIndex}>
                                    <div className="text-end mx-2">{formatCurrencyDecimal(cellData, DIGIT_DEFAULT)}</div>
                                </CellMeasurer>
                            )
                        }}
                    />
                    <Column
                        dataKey="low" 
                        className='overflow-unset'
                        width={70}
                        minWidth={70}
                        headerRenderer={() =>
                            <div className="text-end fw-bold">{t('market.low')}</div>
                        }
                        cellRenderer={({ cellData, dataKey, parent, rowIndex }) => {
                            return (
                                <CellMeasurer
                                    cache={cache.current}
                                    key={dataKey}
                                    parent={parent}
                                    rowIndex={rowIndex}>
                                    <div className="text-end mx-2">{formatCurrencyDecimal(cellData, DIGIT_DEFAULT)}</div>
                                </CellMeasurer>
                            )
                        }}
                    />
                    <Column
                        dataKey="prevClosePrice" 
                        className='overflow-unset'
                        width={90}
                        minWidth={90}
                        headerRenderer={() =>
                            <div className="text-end fw-bold">{t('market.preClose')}</div>
                        }
                        cellRenderer={({ cellData, dataKey, parent, rowIndex }) => {
                            return (
                                <CellMeasurer
                                    cache={cache.current}
                                    key={dataKey}
                                    parent={parent}
                                    rowIndex={rowIndex}>
                                    <div className="text-end mx-2">{formatCurrencyDecimal(cellData, DIGIT_DEFAULT)}</div>
                                </CellMeasurer>
                            )
                        }}
                    />
                </Table>
            }}</AutoSizer>
        </div>
    )

    return (
        <>
            <div className="hidden-scroll" ref={tableRef}>
                {_renderTableData()}
            </div>
        </>
    )
}

export default memo(MarketList);