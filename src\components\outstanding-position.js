const OutstandingPosition = () => {
    return(
        <div className="card mb-0 border-0">
            <div className="card-header py-2 d-flex justify-content-between align-items-center">
                <h5 className="card-title mb-0">Outstanding Position</h5>
                <div className="small"><a href="#" className="text-warning p-0 me-2"><i className="bi bi-arrow-clockwise"></i></a>Last Updated: Feb 17 2022 05:10 PM </div>
            </div>
            <div className="card-body">
                <p className="text-danger">* Contracts that are Due + 2 market days are not allowed to be picked up and are subjected to forceselling </p>
                <div className="table-responsive mb-3">
                    <table className="table">
                        <thead>
                            <tr className="align-top text-uppercase">
                                <th width="30">&nbsp;</th>
                                <th>
                                    <div className="text-one-line">Contract</div>
                                    <div className="text-one-line">No</div>
                                </th>
                                <th>
                                    <div className="text-one-line">Security</div>
                                    <div>Name</div>
                                </th>
                                <th>
                                    <span className="text-one-line">IDN</span>
                                </th>
                                <th>
                                    <div className="text-one-line">Contract</div>
                                    <div>Date</div>
                                </th>
                                <th>
                                    <span className="text-one-line">Due Date</span>
                                </th>
                                <th>
                                    <span className="text-one-line">GRD</span>
                                </th>
                                <th>
                                    <span className="text-one-line">Price</span>
                                </th>
                                <th>
                                    <span className="text-one-line">Qty</span>
                                </th>
                                <th>
                                    <span className="text-one-line">Trd Cur</span>
                                </th>
                                <th>
                                    <span className="text-one-line">SS Amt</span>
                                </th>
                                <th>
                                    <span className="text-one-line">Set Cur</span>
                                </th>
                                <th>
                                    <span className="text-one-line">Frm Amt</span>
                                </th>
                                <th>
                                    <div className="text-one-line">Set Exec</div>
                                    <div>Rate</div>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr className="align-middle text-uppercase">
                                <td>
                                    <div className="form-check mb-0">
                                        <input className="form-check-input" type="checkbox" value="" id="select_id_1" checked />
                                        <label className="form-check-label" for="select_id_1"></label>
                                    </div>
                                </td>
                                <td>
                                    <a href="#" className="text-info-light">0501943</a>
                                </td>
                                <td>
                                    JMH
                                </td>
                                <td>
                                    &nbsp;
                                </td>
                                <td>
                                    16/02/2022
                                </td>
                                <td>
                                    16/02/2022
                                </td>
                                <td>
                                    S
                                </td>
                                <td>
                                    0.00000
                                </td>
                                <td>
                                    0
                                </td>
                                <td>
                                    S$
                                </td>
                                <td>
                                    S$146.50 CR
                                </td>
                                <td>
                                    US$
                                </td>
                                <td>
                                    41.65 CR
                                </td>
                                <td>
                                    0.000000
                                </td>
                            </tr>
                            <tr className="align-middle text-uppercase">
                                <td>
                                    <div className="form-check mb-0">
                                        <input className="form-check-input" type="checkbox" value="" id="select_id_x" />
                                        <label className="form-check-label" for="select_id_x"></label>
                                    </div>
                                </td>
                                <td>
                                    <a href="#">450580/400/02</a>
                                </td>
                                <td>
                                    UOB
                                </td>
                                <td>
                                    &nbsp;
                                </td>
                                <td>
                                    16/02/2022
                                </td>
                                <td>
                                    16/02/2022
                                </td>
                                <td>
                                    S
                                </td>
                                <td>
                                    S$24.229
                                </td>
                                <td>
                                    155
                                </td>
                                <td>
                                    S$
                                </td>
                                <td>
                                    <span className="text-dangdr">S$3,761.23 DR	</span>
                                </td>
                                <td>
                                    US$
                                </td>
                                <td>
                                    41.65 CR
                                </td>
                                <td>
                                    1.000000
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div className="d-flex">
                    <div className="me-3">NOTES:</div>
                    <div className="flex-grow-1">All trades done will be displayed on a best effort basis in the module on T+1. However, clients are advised to refer to the official contract notes for the settlement details. The Company does not accept any responsibility
                        or liability for any loss or inaccuracy of information displayed on its Electronic Broking Services per the Conditions Governing Phillip Securities Accounts.</div>
                </div>
                <hr/>
                <p className="text-danger">For enquiries, please contact your trading representative.Testing at Tel : ******** or email to : <EMAIL></p>
            </div>
        </div>
    )
}

export default OutstandingPosition;