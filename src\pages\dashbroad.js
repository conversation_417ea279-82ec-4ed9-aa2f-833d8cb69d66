import { memo, useCallback, useEffect, useState } from "react";
import moment from "moment";
import { useDispatch, useSelector } from "react-redux";

import { wsService } from "../services/websocket-service";
import rpc from "../model/proto/rpc_pb";
import { SymbolListRequest } from "../model/proto/query_service_pb";
import { SymbolStatus } from '../model/proto/query_model_pb'
import { GetLastQuotesRequest, SubscribeQuoteEventRequest, UnsubscribeQuoteEventRequest } from "../model/proto/pricing_service_pb";

import { LocalStorageKey, SocketKey } from "../model/constant";
import { convertNumber } from "../model/utils";
import { updateWarningMessage } from "../redux/actions/message";

import Header from "../components/header";
import MarketList from "../components/market-list";
import OrderPannel from "../components/order-pannel";
import LazyLoad from "../components/lazy-load";
import RiskStatement from "./riskStatement";

const wsResponseTimeout = window.wsResponseTimeout;

const DashBroad = () => {
    const [symbols, setSymbols] = useState([]);
    const [symbolInfo, setSymbolInfo] = useState({});
    const [symbolMap, setSymbolMap] = useState(new Map());
    const [isShowlazyLoad, setIsShowLazyLoad] = useState(false);

    const dispatch = useDispatch();
    const {enableFlg, content} = useSelector(state => state.message); 

    useEffect(() => {
        callSymbolListRequest()
    }, [])

    useEffect(() => {
        const wsConnect = wsService.getSocketSubject().subscribe(resp => {
            if (resp === SocketKey.SOCKET_CONNECTED || resp === SocketKey.SOCKET_RECONNECTED) {
                callSymbolListRequest();
            }
        });

        const symbolListResponse = wsService.getSymbolListResponse().subscribe(symbols => {
            setIsShowLazyLoad(false);
            if (symbols && symbols.symbolList) {
                const symbolListSorted = symbols.symbolList.sort((a, b) => a.symbolCode.localeCompare(b.symbolCode))
                const symbolActive = symbolListSorted.filter(item => item.symbolStatus === SymbolStatus.SYMBOL_ACTIVE);
                localStorage.setItem(LocalStorageKey.SYMBOL_LIST, JSON.stringify(symbolActive));
                setSymbols(symbolActive);
                callLastQuoteRequest(symbolActive);
                subscribeQuoteEventRequest(symbolActive);
                const _symbolMap = new Map();
                symbolListSorted.forEach(symbol => {
                    _symbolMap.set(symbol.symbolCode, symbol);
                });
                setSymbolMap(_symbolMap);
            }
        });

        const warningMessage = wsService.getWarningMessage().subscribe(res => {
            dispatch(updateWarningMessage({
                enableFlg: res.enableFlg,
                content: res.content
            }));
        })

        return () => {
            unsubscribeQuoteEventRequest();
            wsConnect.unsubscribe();
            symbolListResponse.unsubscribe();
            warningMessage.unsubscribe();
        }
    }, []);

    // NOTE: setTimeout if don't have symbolList response, lazyLoad auto hidden after config time
    useEffect(() => {
        const timer = setTimeout(() => {
            if (isShowlazyLoad) {
                setIsShowLazyLoad(false);
            }
        }, [convertNumber(wsResponseTimeout)])

        return () => {
            clearTimeout(timer);
        }
    }, [isShowlazyLoad])

    const callSymbolListRequest = () => {
        setIsShowLazyLoad(true);
        const accountId = sessionStorage.getItem(LocalStorageKey.ACCOUNT_ID);
        const wsConnect = wsService.getWsConnected();
        if (wsConnect) {
            const symbolListRequest = new SymbolListRequest();
            symbolListRequest.setAccountId(accountId);
            const rpcMsg = new rpc.RpcMessage();
            rpcMsg.setPayloadClass(rpc.RpcMessage.Payload.SYMBOL_LIST_REQ);
            rpcMsg.setPayloadData(symbolListRequest.serializeBinary());
            rpcMsg.setContextId(moment().valueOf());
            wsService.sendMessage(rpcMsg.serializeBinary());
        }
    }

    const subscribeQuoteEventRequest = (symbolList) => {
        const wsConnected = wsService.getWsConnected();
        if (wsConnected) {
            const subscribeQuotesReq = new SubscribeQuoteEventRequest();
            symbolList.forEach(item => {
                subscribeQuotesReq.addSymbolCode(item.symbolCode);
            });
            let rpcMsg = new rpc.RpcMessage();
            rpcMsg.setPayloadClass(rpc.RpcMessage.Payload.SUBSCRIBE_QUOTE_REQ);
            rpcMsg.setPayloadData(subscribeQuotesReq.serializeBinary());
            rpcMsg.setContextId(moment().valueOf());
            wsService.sendMessage(rpcMsg.serializeBinary());
        }
    }

    const unsubscribeQuoteEventRequest = () => {
        const wsConnected = wsService.getWsConnected();
        if (wsConnected) {
            let unsubscribeQuoteReq = new UnsubscribeQuoteEventRequest();
            symbols.forEach(item => {
                unsubscribeQuoteReq.addSymbolCode(item.symbolCode);
            });
            let rpcMsg = new rpc.RpcMessage();
            rpcMsg.setPayloadClass(rpc.RpcMessage.Payload.UNSUBSCRIBE_QUOTE_REQ);
            rpcMsg.setPayloadData(unsubscribeQuoteReq.serializeBinary());
            wsService.sendMessage(rpcMsg.serializeBinary());
        }
    }

    const callLastQuoteRequest = (symbolList) => {
        const wsConnect = wsService.getWsConnected();
        if (wsConnect) {
            const lastQuoteRequest = new GetLastQuotesRequest();
            symbolList.forEach(item => {
                lastQuoteRequest.addSymbolCode(item.symbolCode);
            });
            const rpcMsg = new rpc.RpcMessage();
            rpcMsg.setPayloadClass(rpc.RpcMessage.Payload.LAST_QUOTE_REQ);
            rpcMsg.setPayloadData(lastQuoteRequest.serializeBinary());
            rpcMsg.setContextId(moment().valueOf());
            wsService.sendMessage(rpcMsg.serializeBinary());
        }
    }

    const getSymbolInfo = (value) => {
      setSymbolInfo(value);
    };

    return (
        <div className="site">
            <Header page="dashboard" />
            <div className="site-main py-3">
                <div className="container">
                    <div className="row g-3">
                        {enableFlg && 
                            <div className="col-12 mt-0 mb-3 opacity-90 text-warning">
                                {content}
                            </div>
                        }
                        <div className="col-12 mt-0 mb-1 opacity-75">
                            PSPL acts as principal and is the contractual counterparty with investors in the trades.
                        </div>
                        <div className="col-lg-9 mt-0">
                            <MarketList handleFocusSymbol={getSymbolInfo} symbols={symbols} />
                            <div className="mt-0 opacity-75">Note: Prev. Close prices reflected in the US (Asian Hrs) refers to previous USPOST Market Close price.</div>
                            <RiskStatement/>
                        </div>
                        <div className="col-lg-3 d-flex mt-0">
                            <OrderPannel symbolInfo={symbolInfo} symbolMap={symbolMap} />
                        </div>
                    </div>
                </div>
            </div>
            {isShowlazyLoad && <LazyLoad />}
        </div>
    )
}

export default memo(DashBroad);