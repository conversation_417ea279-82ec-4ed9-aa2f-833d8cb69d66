syntax = "proto3";

message LoginInfo {
	string login		= 1;
	string mac_address	= 3;	//PC mac_address
	string local_ip		= 4;	//local ip
}

message Account {
	int64 account_id 	= 1;
	string name 				= 2;
	int64 group_id 				= 3;
	bool enable_flg 			= 4;
	TradingRights trading_rights 			= 5;
	bool api_flg				= 6;
	int64 registered_date 		= 7;
	string comment 				= 8;
	string api_key 				= 9;
	string password = 10;
	string secret_key			= 11;	//trading pin

	// Setting notifications
	BoolFlag recv_admin_news_flg = 12;
	BoolFlag recv_match_noti_flg = 13;

	string email = 14;
	string phone = 15;

	BoolFlag enable_secret_key_flg = 16; // Setting enable trading PIN

	int64 num_trades			= 17;
	int64 num_pending_orders	= 18;

	repeated string allowed_markets	= 19;

	// Have to use enum with NONE value for bool flag because:
	// There's no way of telling whether a field was explicitly set to
	// the default value (for example whether a boolean was set to false) or just not set at all
	enum BoolFlag {BOOL_FLAG_NONE = 0; BOOL_FLAG_ON = 1; BOOL_FLAG_OFF = 2;}
	enum TradingRights {TRADING_RIGHTS_NONE = 0; TRADING_OPEN_ONLY = 1; TRADING_CLOSE_ONLY = 2; TRADING_NORMAL = 3; }
}

message AdminSettings {
	int64 gross_buy_limit	= 1;
	int64 gross_sell_limit	= 2;
	repeated SessionSettings session_settings = 3;
	repeated Holiday holiday_settings = 4;
}

message GroupAccount {
	int64 group_id 				= 1;
	string group_name			= 2;
	GroupType group_type		= 3;
	int64 max_order_volume 		= 4;
	string min_order_value		= 5;
	string max_order_value		= 6;
}

enum GroupType {
	GT_NONE		= 0;
	GT_LP		= 1;
	GT_RETAIL	= 2;
}

message SessionSettings {
	int32 day_of_week		  = 1;  // SUNDAY ~ 0, MONDAY ~ 1, TUESDAY ~ 2, etc...
	int64 open_time			  = 2; // Minutes couting from 00:00 
	int64 close_time		  = 3; // Minutes couting from 00:00 
	int64 pre_order_time	  = 4; // Minutes couting from 00:00 
	int64 external_open_time = 5; // Start forwarding order to Blue Ocean - season session
	int64 external_preclose_time = 6; //Stop forwarding order to Blue Ocean - season session
	int64 external_close_time = 7; // Restart matching in USAH, Blue Ocean is closed and all unexecuted orders are canceled successfully
}

message Holiday {
	int64 holiday_id		= 1;
	HolidayStatus holiday_status		= 2;
	int64 timestamp_from		= 3; // Unix timestamp in milliseconds, e.g: *************
	int64 timestamp_to		= 4; // Unix timestamp in milliseconds, e.g: *************
}

enum HolidayStatus {
	HS_NONE 				= 0;
	HS_ACTIVE				= 1;
	HS_DEACTIVE				= 2;
}

message AccountBalance {
	int64 account_id 			= 1;
	string balance				= 2;
	string collateral			= 3;
	string realized_pl			= 4;
	string margin_required		= 5;
	string margin_maintain		= 6;
	string margin_deficit		= 7;
	string unrealized_pl = 8;
	string buying_power = 9;
	string equity 		= 10;
	string withdrawable_amount = 11;
	string currency_code				=	12;
	string password 					=	13; // login password
}

message AccountPortfolio {
	int64 account_id 			= 1;
	string symbol_code			= 2;
	
	int64 total_buy_volume = 3;
	string total_buy_amount = 4;

	int64 total_sell_volume = 5;
	string total_sell_amount = 6;
	
	string market_price			= 7;
	string invested_value		= 8;
	string current_value		= 9;
	string realized_pl			= 10;
	string unrealized_pl 		= 11;
	string currency_code		= 12;
	int64 owned_volume			= 13;
	string owned_amount			= 14;
	int64 total_volume			= 15;
}

enum DealAction {
	DEAL_NONE		= 0;
	DEAL_BALANCE	= 2;
	DEAL_CREDIT		= 3;
	DEAL_CHARGE		= 4;
	DEAL_BUY 		= 5;
	DEAL_SELL		= 6;
	DEAL_COLLATERAL	= 7;
}

enum SessionStatusCode {
    SESSION_NONE        = 0;
	SESSION_CLOSE       = 1;
	SESSION_OPEN        = 2;
	SESSION_REMOVE      = 3;
	SESSION_NOTFOUND    = 4;
}

message MarketDataSource {
	string	exchange_name	= 1;
	bool	enable_flg		= 2;
}

message MarketSettings {
	string market_code						= 1;
	MarketStatus md_status					= 2;
	MarketStatus trading_status				= 3;
	repeated SessionSettings md_time		= 4;
	repeated SessionSettings trading_time	= 5;
}

enum MarketStatus {
	MS_NONE 				= 0;
	MS_ACTIVE				= 1;
	MS_DEACTIVE				= 2;
}

//return code
enum MsgCode {
	MT_RET_OK_NONE					= 0;
	MT_RET_OK						= 1;

	MT_RET_ERROR					= 2;
	MT_RET_ERR_PARAMS				= 3;	//invalid parameter
	MT_RET_ERR_PERMISSIONS			= 8;
	MT_RET_ERR_TIMEOUT				= 9;
	MT_RET_ERR_NOTFOUND				= 13;
	MT_RET_FORWARD_EXT_SYSTEM			= 100; // Orders forwarded to external trading system 









	MT_RET_SUBCRIBE_OK				= 310;
	MT_RET_SUBCRIBE_ALREADY			= 311;
	MT_RET_SUBCRIBE_ERROR			= 312;

	MT_RET_UNSUBCRIBE_OK			= 320;
	MT_RET_UNSUBCRIBE_ERROR			= 322;

	MT_RET_AUTH_ACCOUNT_INVALID		= 1001;	//Invalid account
	MT_RET_AUTH_ACCOUNT_DISABLED	= 1002;	//Account is disabled

	MT_RET_REQUEST_INVALID_SYMBOL	= 10013;
	MT_RET_REQUEST_INVALID_VOLUME	= 10014;
	MT_RET_ERR_NOT_ENOUGH_MONEY = 10019;
	MT_RET_REQUEST_INVALID_FILL		= 10030;
	MT_RET_REQUEST_LIMIT_VOLUME		= 10034;
	MT_RET_REQUEST_INVALID_ORDER_TYPE	= 10035;
	MT_RET_REQUEST_INVALID_LIMIT_PRICE		= 10036;
	MT_RET_REQUEST_INVALID_TRIGGER_PRICE		= 10037;
	MT_RET_REQUEST_PROHIBITED_OPEN_ORDER = 10038;
	MT_RET_REQUEST_PROHIBITED_CLOSE_ORDER = 10039;
	MT_RET_MARKET_CLOSED			= 10040;
	MT_RET_INVALID_TICK_SIZE		= 10041;
	MT_RET_INVALID_PRICE_RANGE		= 10042;
	MT_RET_INVALID_MIN_LOT			= 10043; // Order qty is smaller than min lot
	MT_RET_INVALID_LOT_SIZE			= 10044;
	MT_RET_NEGATIVE_QTY				= 10045;
	MT_RET_EXCEED_MAX_ORDER_VOLUME	= 10046; // Order qty is out of range with max order volume
	MT_RET_NOT_ENOUGH_MIN_ORDER_VALUE			= 10047; // Order value is smaller than min order value
	MT_RET_INVALID_HOLIDAY_SESSION	= 10048; // Order in holiday session
	MT_RET_INVALID_PASSWORD			= 10049;
	MT_RET_TOKEN_EXPIRED			= 10050;
	MT_RET_CHANGE_PASSWORD_FAILED	= 10051;
	MT_RET_EXIST_LIMIT_ORDER_IN_QUEUE	= 10052; // There is an existing limit order in queue for retail
	MT_RET_EXCEED_MAX_ORDER_VALUE	= 10053; // Order value is out of range with max order value
	MT_RET_UNKNOWN_ORDER_ID			= 10054; // OrderNo was cancelled or filled before
	
	MT_RET_ERROR_FROM_BO            = 10055; // Error from Cowen
	MT_RET_ORDER_CLOSED				= 10056; // Order at closed state can not be modified or canceled
	MT_RET_REQUEST_MARKET_ACCESS_DENIED	= 10060; // Client doesn't have access rights to trade in the current market

// RMS risk check
	MT_RET_RMS_PROGRAM_ERROR		= 20001; // Program error
	MT_RET_RMS_INVALID_INPUT		= 20002; // Invalid input
	MT_RET_RMS_INVALID_SIDE			= 20003; // Order side should be Buy(1) / Sell(2) only
	MT_RET_RMS_INVALID_COMPANY		= 20004; // No valid company for input symbol and symbolsfx
	MT_RET_RMS_INVALID_CURRENCY		= 20005; // Currency Code for input symbol and symbolfx is not valid
	MT_RET_RMS_INVALID_LINKAGE		= 20006; // Order submitted Remisier is no linkage with the client
	MT_RET_RMS_NO_ACCESS		= 20007; // Account No don't have access to US Market trading
	MT_RET_RMS_PASSWORD_FAILED		= 20008; // E2EE password verification failed
	MT_RET_RMS_ERROR_COMPANY_INFO	= 20009; // Error: getting Company Info for input symbol and symbolfx
	MT_RET_RMS_PERMISSION_ACCESS	= 20010; // Error: getting US Trade Access for input Account No
	MT_RET_RMS_INVALID_ORDER_NO		= 20011; // OrderNo was submitted before
	MT_RET_RMS_INVALID_PRICE_LENGTH	= 20012; // Order price is out of field length
	MT_RET_RMS_STOP_PRICE_LENGTH	= 20013; // Stop price is out of field length
	MT_RET_RMS_INVALID_ORDER_TYPE	= 20014; // Order Type is not valid
	MT_RET_RMS_INVALID_CLIENT		= 20015; // Client of input order is not eligible to trade US Asian Hrs
	MT_RET_RMS_DB_ERROR				= 20016; // DB Error
	MT_RET_RMS_FAILED_RISK_CHECK	= 30001; // Show as comment that API return
}


