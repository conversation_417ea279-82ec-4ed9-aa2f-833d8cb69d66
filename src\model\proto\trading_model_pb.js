// source: trading_model.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = (function() {
  if (this) { return this; }
  if (typeof window !== 'undefined') { return window; }
  if (typeof global !== 'undefined') { return global; }
  if (typeof self !== 'undefined') { return self; }
  return Function('return this')();
}.call(null));

var system_model_pb = require('./system_model_pb.js');
goog.object.extend(proto, system_model_pb);
goog.exportSymbol('proto.CallPut', null, global);
goog.exportSymbol('proto.Contract', null, global);
goog.exportSymbol('proto.ExchangeCode', null, global);
goog.exportSymbol('proto.ExecutionMode', null, global);
goog.exportSymbol('proto.ExternalState', null, global);
goog.exportSymbol('proto.ModifyType', null, global);
goog.exportSymbol('proto.Order', null, global);
goog.exportSymbol('proto.OrderEntry', null, global);
goog.exportSymbol('proto.OrderFilling', null, global);
goog.exportSymbol('proto.OrderMode', null, global);
goog.exportSymbol('proto.OrderReason', null, global);
goog.exportSymbol('proto.OrderRoute', null, global);
goog.exportSymbol('proto.OrderState', null, global);
goog.exportSymbol('proto.OrderTime', null, global);
goog.exportSymbol('proto.OrderType', null, global);
goog.exportSymbol('proto.Position', null, global);
goog.exportSymbol('proto.Side', null, global);
goog.exportSymbol('proto.Trade', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.Order = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.Order, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.Order.displayName = 'proto.Order';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.Trade = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.Trade, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.Trade.displayName = 'proto.Trade';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.Contract = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.Contract, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.Contract.displayName = 'proto.Contract';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.Position = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.Position, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.Position.displayName = 'proto.Position';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.Order.prototype.toObject = function(opt_includeInstance) {
  return proto.Order.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.Order} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Order.toObject = function(includeInstance, msg) {
  var f, obj = {
    orderId: jspb.Message.getFieldWithDefault(msg, 1, ""),
    symbolCode: jspb.Message.getFieldWithDefault(msg, 2, ""),
    orderType: jspb.Message.getFieldWithDefault(msg, 3, 0),
    state: jspb.Message.getFieldWithDefault(msg, 4, 0),
    amount: jspb.Message.getFieldWithDefault(msg, 5, ""),
    price: jspb.Message.getFieldWithDefault(msg, 6, ""),
    slippage: jspb.Message.getFieldWithDefault(msg, 7, ""),
    orderFilling: jspb.Message.getFieldWithDefault(msg, 8, 0),
    executeMode: jspb.Message.getFieldWithDefault(msg, 9, 0),
    reason: jspb.Message.getFieldWithDefault(msg, 10, 0),
    orderTime: jspb.Message.getFieldWithDefault(msg, 11, 0),
    expireTime: jspb.Message.getFieldWithDefault(msg, 12, 0),
    tp: jspb.Message.getFieldWithDefault(msg, 13, ""),
    sl: jspb.Message.getFieldWithDefault(msg, 14, ""),
    pl: jspb.Message.getFieldWithDefault(msg, 15, ""),
    swap: jspb.Message.getFieldWithDefault(msg, 16, ""),
    fee: jspb.Message.getFieldWithDefault(msg, 17, ""),
    time: jspb.Message.getFieldWithDefault(msg, 18, 0),
    note: jspb.Message.getFieldWithDefault(msg, 19, ""),
    entry: jspb.Message.getFieldWithDefault(msg, 20, 0),
    route: jspb.Message.getFieldWithDefault(msg, 21, 0),
    orderMode: jspb.Message.getFieldWithDefault(msg, 22, 0),
    uid: jspb.Message.getFieldWithDefault(msg, 23, 0),
    averagePrice: jspb.Message.getFieldWithDefault(msg, 24, ""),
    filledAmount: jspb.Message.getFieldWithDefault(msg, 25, ""),
    lastPrice: jspb.Message.getFieldWithDefault(msg, 26, ""),
    executedDatetime: jspb.Message.getFieldWithDefault(msg, 27, ""),
    side: jspb.Message.getFieldWithDefault(msg, 28, 0),
    externalOrderId: jspb.Message.getFieldWithDefault(msg, 29, ""),
    currencyCode: jspb.Message.getFieldWithDefault(msg, 30, ""),
    totalFilledAmount: jspb.Message.getFieldWithDefault(msg, 31, ""),
    comment: jspb.Message.getFieldWithDefault(msg, 32, ""),
    orderRef: jspb.Message.getFieldWithDefault(msg, 33, ""),
    withdrawAmount: jspb.Message.getFieldWithDefault(msg, 34, ""),
    submittedId: jspb.Message.getFieldWithDefault(msg, 35, ""),
    msgCode: jspb.Message.getFieldWithDefault(msg, 36, 0),
    groupType: jspb.Message.getFieldWithDefault(msg, 37, 0),
    externalState: jspb.Message.getFieldWithDefault(msg, 38, 0),
    allowMatching: jspb.Message.getBooleanFieldWithDefault(msg, 39, false),
    existedInBo: jspb.Message.getBooleanFieldWithDefault(msg, 40, false),
    orderMarketCode: jspb.Message.getFieldWithDefault(msg, 41, ""),
    executedMarketCode: jspb.Message.getFieldWithDefault(msg, 42, ""),
    cqB2bAccountNo: jspb.Message.getFieldWithDefault(msg, 43, ""),
    cqClSetCurrency: jspb.Message.getFieldWithDefault(msg, 44, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.Order}
 */
proto.Order.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.Order;
  return proto.Order.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.Order} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.Order}
 */
proto.Order.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setOrderId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSymbolCode(value);
      break;
    case 3:
      var value = /** @type {!proto.OrderType} */ (reader.readEnum());
      msg.setOrderType(value);
      break;
    case 4:
      var value = /** @type {!proto.OrderState} */ (reader.readEnum());
      msg.setState(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setAmount(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setPrice(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setSlippage(value);
      break;
    case 8:
      var value = /** @type {!proto.OrderFilling} */ (reader.readEnum());
      msg.setOrderFilling(value);
      break;
    case 9:
      var value = /** @type {!proto.ExecutionMode} */ (reader.readEnum());
      msg.setExecuteMode(value);
      break;
    case 10:
      var value = /** @type {!proto.OrderReason} */ (reader.readEnum());
      msg.setReason(value);
      break;
    case 11:
      var value = /** @type {!proto.OrderTime} */ (reader.readEnum());
      msg.setOrderTime(value);
      break;
    case 12:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setExpireTime(value);
      break;
    case 13:
      var value = /** @type {string} */ (reader.readString());
      msg.setTp(value);
      break;
    case 14:
      var value = /** @type {string} */ (reader.readString());
      msg.setSl(value);
      break;
    case 15:
      var value = /** @type {string} */ (reader.readString());
      msg.setPl(value);
      break;
    case 16:
      var value = /** @type {string} */ (reader.readString());
      msg.setSwap(value);
      break;
    case 17:
      var value = /** @type {string} */ (reader.readString());
      msg.setFee(value);
      break;
    case 18:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setTime(value);
      break;
    case 19:
      var value = /** @type {string} */ (reader.readString());
      msg.setNote(value);
      break;
    case 20:
      var value = /** @type {!proto.OrderEntry} */ (reader.readEnum());
      msg.setEntry(value);
      break;
    case 21:
      var value = /** @type {!proto.OrderRoute} */ (reader.readEnum());
      msg.setRoute(value);
      break;
    case 22:
      var value = /** @type {!proto.OrderMode} */ (reader.readEnum());
      msg.setOrderMode(value);
      break;
    case 23:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setUid(value);
      break;
    case 24:
      var value = /** @type {string} */ (reader.readString());
      msg.setAveragePrice(value);
      break;
    case 25:
      var value = /** @type {string} */ (reader.readString());
      msg.setFilledAmount(value);
      break;
    case 26:
      var value = /** @type {string} */ (reader.readString());
      msg.setLastPrice(value);
      break;
    case 27:
      var value = /** @type {string} */ (reader.readString());
      msg.setExecutedDatetime(value);
      break;
    case 28:
      var value = /** @type {!proto.Side} */ (reader.readEnum());
      msg.setSide(value);
      break;
    case 29:
      var value = /** @type {string} */ (reader.readString());
      msg.setExternalOrderId(value);
      break;
    case 30:
      var value = /** @type {string} */ (reader.readString());
      msg.setCurrencyCode(value);
      break;
    case 31:
      var value = /** @type {string} */ (reader.readString());
      msg.setTotalFilledAmount(value);
      break;
    case 32:
      var value = /** @type {string} */ (reader.readString());
      msg.setComment(value);
      break;
    case 33:
      var value = /** @type {string} */ (reader.readString());
      msg.setOrderRef(value);
      break;
    case 34:
      var value = /** @type {string} */ (reader.readString());
      msg.setWithdrawAmount(value);
      break;
    case 35:
      var value = /** @type {string} */ (reader.readString());
      msg.setSubmittedId(value);
      break;
    case 36:
      var value = /** @type {!proto.MsgCode} */ (reader.readEnum());
      msg.setMsgCode(value);
      break;
    case 37:
      var value = /** @type {!proto.GroupType} */ (reader.readEnum());
      msg.setGroupType(value);
      break;
    case 38:
      var value = /** @type {!proto.ExternalState} */ (reader.readEnum());
      msg.setExternalState(value);
      break;
    case 39:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setAllowMatching(value);
      break;
    case 40:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setExistedInBo(value);
      break;
    case 41:
      var value = /** @type {string} */ (reader.readString());
      msg.setOrderMarketCode(value);
      break;
    case 42:
      var value = /** @type {string} */ (reader.readString());
      msg.setExecutedMarketCode(value);
      break;
    case 43:
      var value = /** @type {string} */ (reader.readString());
      msg.setCqB2bAccountNo(value);
      break;
    case 44:
      var value = /** @type {string} */ (reader.readString());
      msg.setCqClSetCurrency(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.Order.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.Order.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.Order} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Order.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOrderId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSymbolCode();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getOrderType();
  if (f !== 0.0) {
    writer.writeEnum(
      3,
      f
    );
  }
  f = message.getState();
  if (f !== 0.0) {
    writer.writeEnum(
      4,
      f
    );
  }
  f = message.getAmount();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getPrice();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getSlippage();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getOrderFilling();
  if (f !== 0.0) {
    writer.writeEnum(
      8,
      f
    );
  }
  f = message.getExecuteMode();
  if (f !== 0.0) {
    writer.writeEnum(
      9,
      f
    );
  }
  f = message.getReason();
  if (f !== 0.0) {
    writer.writeEnum(
      10,
      f
    );
  }
  f = message.getOrderTime();
  if (f !== 0.0) {
    writer.writeEnum(
      11,
      f
    );
  }
  f = message.getExpireTime();
  if (f !== 0) {
    writer.writeInt64(
      12,
      f
    );
  }
  f = message.getTp();
  if (f.length > 0) {
    writer.writeString(
      13,
      f
    );
  }
  f = message.getSl();
  if (f.length > 0) {
    writer.writeString(
      14,
      f
    );
  }
  f = message.getPl();
  if (f.length > 0) {
    writer.writeString(
      15,
      f
    );
  }
  f = message.getSwap();
  if (f.length > 0) {
    writer.writeString(
      16,
      f
    );
  }
  f = message.getFee();
  if (f.length > 0) {
    writer.writeString(
      17,
      f
    );
  }
  f = message.getTime();
  if (f !== 0) {
    writer.writeInt64(
      18,
      f
    );
  }
  f = message.getNote();
  if (f.length > 0) {
    writer.writeString(
      19,
      f
    );
  }
  f = message.getEntry();
  if (f !== 0.0) {
    writer.writeEnum(
      20,
      f
    );
  }
  f = message.getRoute();
  if (f !== 0.0) {
    writer.writeEnum(
      21,
      f
    );
  }
  f = message.getOrderMode();
  if (f !== 0.0) {
    writer.writeEnum(
      22,
      f
    );
  }
  f = message.getUid();
  if (f !== 0) {
    writer.writeInt64(
      23,
      f
    );
  }
  f = message.getAveragePrice();
  if (f.length > 0) {
    writer.writeString(
      24,
      f
    );
  }
  f = message.getFilledAmount();
  if (f.length > 0) {
    writer.writeString(
      25,
      f
    );
  }
  f = message.getLastPrice();
  if (f.length > 0) {
    writer.writeString(
      26,
      f
    );
  }
  f = message.getExecutedDatetime();
  if (f.length > 0) {
    writer.writeString(
      27,
      f
    );
  }
  f = message.getSide();
  if (f !== 0.0) {
    writer.writeEnum(
      28,
      f
    );
  }
  f = message.getExternalOrderId();
  if (f.length > 0) {
    writer.writeString(
      29,
      f
    );
  }
  f = message.getCurrencyCode();
  if (f.length > 0) {
    writer.writeString(
      30,
      f
    );
  }
  f = message.getTotalFilledAmount();
  if (f.length > 0) {
    writer.writeString(
      31,
      f
    );
  }
  f = message.getComment();
  if (f.length > 0) {
    writer.writeString(
      32,
      f
    );
  }
  f = message.getOrderRef();
  if (f.length > 0) {
    writer.writeString(
      33,
      f
    );
  }
  f = message.getWithdrawAmount();
  if (f.length > 0) {
    writer.writeString(
      34,
      f
    );
  }
  f = message.getSubmittedId();
  if (f.length > 0) {
    writer.writeString(
      35,
      f
    );
  }
  f = message.getMsgCode();
  if (f !== 0.0) {
    writer.writeEnum(
      36,
      f
    );
  }
  f = message.getGroupType();
  if (f !== 0.0) {
    writer.writeEnum(
      37,
      f
    );
  }
  f = message.getExternalState();
  if (f !== 0.0) {
    writer.writeEnum(
      38,
      f
    );
  }
  f = message.getAllowMatching();
  if (f) {
    writer.writeBool(
      39,
      f
    );
  }
  f = message.getExistedInBo();
  if (f) {
    writer.writeBool(
      40,
      f
    );
  }
  f = message.getOrderMarketCode();
  if (f.length > 0) {
    writer.writeString(
      41,
      f
    );
  }
  f = message.getExecutedMarketCode();
  if (f.length > 0) {
    writer.writeString(
      42,
      f
    );
  }
  f = message.getCqB2bAccountNo();
  if (f.length > 0) {
    writer.writeString(
      43,
      f
    );
  }
  f = message.getCqClSetCurrency();
  if (f.length > 0) {
    writer.writeString(
      44,
      f
    );
  }
};


/**
 * optional string order_id = 1;
 * @return {string}
 */
proto.Order.prototype.getOrderId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setOrderId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string symbol_code = 2;
 * @return {string}
 */
proto.Order.prototype.getSymbolCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setSymbolCode = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional OrderType order_type = 3;
 * @return {!proto.OrderType}
 */
proto.Order.prototype.getOrderType = function() {
  return /** @type {!proto.OrderType} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {!proto.OrderType} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setOrderType = function(value) {
  return jspb.Message.setProto3EnumField(this, 3, value);
};


/**
 * optional OrderState state = 4;
 * @return {!proto.OrderState}
 */
proto.Order.prototype.getState = function() {
  return /** @type {!proto.OrderState} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {!proto.OrderState} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setState = function(value) {
  return jspb.Message.setProto3EnumField(this, 4, value);
};


/**
 * optional string amount = 5;
 * @return {string}
 */
proto.Order.prototype.getAmount = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setAmount = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string price = 6;
 * @return {string}
 */
proto.Order.prototype.getPrice = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setPrice = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string slippage = 7;
 * @return {string}
 */
proto.Order.prototype.getSlippage = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setSlippage = function(value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional OrderFilling order_filling = 8;
 * @return {!proto.OrderFilling}
 */
proto.Order.prototype.getOrderFilling = function() {
  return /** @type {!proto.OrderFilling} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/**
 * @param {!proto.OrderFilling} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setOrderFilling = function(value) {
  return jspb.Message.setProto3EnumField(this, 8, value);
};


/**
 * optional ExecutionMode execute_mode = 9;
 * @return {!proto.ExecutionMode}
 */
proto.Order.prototype.getExecuteMode = function() {
  return /** @type {!proto.ExecutionMode} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/**
 * @param {!proto.ExecutionMode} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setExecuteMode = function(value) {
  return jspb.Message.setProto3EnumField(this, 9, value);
};


/**
 * optional OrderReason reason = 10;
 * @return {!proto.OrderReason}
 */
proto.Order.prototype.getReason = function() {
  return /** @type {!proto.OrderReason} */ (jspb.Message.getFieldWithDefault(this, 10, 0));
};


/**
 * @param {!proto.OrderReason} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setReason = function(value) {
  return jspb.Message.setProto3EnumField(this, 10, value);
};


/**
 * optional OrderTime order_time = 11;
 * @return {!proto.OrderTime}
 */
proto.Order.prototype.getOrderTime = function() {
  return /** @type {!proto.OrderTime} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/**
 * @param {!proto.OrderTime} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setOrderTime = function(value) {
  return jspb.Message.setProto3EnumField(this, 11, value);
};


/**
 * optional int64 expire_time = 12;
 * @return {number}
 */
proto.Order.prototype.getExpireTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 12, 0));
};


/**
 * @param {number} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setExpireTime = function(value) {
  return jspb.Message.setProto3IntField(this, 12, value);
};


/**
 * optional string tp = 13;
 * @return {string}
 */
proto.Order.prototype.getTp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 13, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setTp = function(value) {
  return jspb.Message.setProto3StringField(this, 13, value);
};


/**
 * optional string sl = 14;
 * @return {string}
 */
proto.Order.prototype.getSl = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 14, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setSl = function(value) {
  return jspb.Message.setProto3StringField(this, 14, value);
};


/**
 * optional string pl = 15;
 * @return {string}
 */
proto.Order.prototype.getPl = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 15, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setPl = function(value) {
  return jspb.Message.setProto3StringField(this, 15, value);
};


/**
 * optional string swap = 16;
 * @return {string}
 */
proto.Order.prototype.getSwap = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 16, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setSwap = function(value) {
  return jspb.Message.setProto3StringField(this, 16, value);
};


/**
 * optional string fee = 17;
 * @return {string}
 */
proto.Order.prototype.getFee = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 17, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setFee = function(value) {
  return jspb.Message.setProto3StringField(this, 17, value);
};


/**
 * optional int64 time = 18;
 * @return {number}
 */
proto.Order.prototype.getTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 18, 0));
};


/**
 * @param {number} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setTime = function(value) {
  return jspb.Message.setProto3IntField(this, 18, value);
};


/**
 * optional string note = 19;
 * @return {string}
 */
proto.Order.prototype.getNote = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 19, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setNote = function(value) {
  return jspb.Message.setProto3StringField(this, 19, value);
};


/**
 * optional OrderEntry entry = 20;
 * @return {!proto.OrderEntry}
 */
proto.Order.prototype.getEntry = function() {
  return /** @type {!proto.OrderEntry} */ (jspb.Message.getFieldWithDefault(this, 20, 0));
};


/**
 * @param {!proto.OrderEntry} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setEntry = function(value) {
  return jspb.Message.setProto3EnumField(this, 20, value);
};


/**
 * optional OrderRoute route = 21;
 * @return {!proto.OrderRoute}
 */
proto.Order.prototype.getRoute = function() {
  return /** @type {!proto.OrderRoute} */ (jspb.Message.getFieldWithDefault(this, 21, 0));
};


/**
 * @param {!proto.OrderRoute} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setRoute = function(value) {
  return jspb.Message.setProto3EnumField(this, 21, value);
};


/**
 * optional OrderMode order_mode = 22;
 * @return {!proto.OrderMode}
 */
proto.Order.prototype.getOrderMode = function() {
  return /** @type {!proto.OrderMode} */ (jspb.Message.getFieldWithDefault(this, 22, 0));
};


/**
 * @param {!proto.OrderMode} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setOrderMode = function(value) {
  return jspb.Message.setProto3EnumField(this, 22, value);
};


/**
 * optional int64 uid = 23;
 * @return {number}
 */
proto.Order.prototype.getUid = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 23, 0));
};


/**
 * @param {number} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setUid = function(value) {
  return jspb.Message.setProto3IntField(this, 23, value);
};


/**
 * optional string average_price = 24;
 * @return {string}
 */
proto.Order.prototype.getAveragePrice = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 24, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setAveragePrice = function(value) {
  return jspb.Message.setProto3StringField(this, 24, value);
};


/**
 * optional string filled_amount = 25;
 * @return {string}
 */
proto.Order.prototype.getFilledAmount = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 25, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setFilledAmount = function(value) {
  return jspb.Message.setProto3StringField(this, 25, value);
};


/**
 * optional string last_price = 26;
 * @return {string}
 */
proto.Order.prototype.getLastPrice = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 26, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setLastPrice = function(value) {
  return jspb.Message.setProto3StringField(this, 26, value);
};


/**
 * optional string executed_datetime = 27;
 * @return {string}
 */
proto.Order.prototype.getExecutedDatetime = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 27, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setExecutedDatetime = function(value) {
  return jspb.Message.setProto3StringField(this, 27, value);
};


/**
 * optional Side side = 28;
 * @return {!proto.Side}
 */
proto.Order.prototype.getSide = function() {
  return /** @type {!proto.Side} */ (jspb.Message.getFieldWithDefault(this, 28, 0));
};


/**
 * @param {!proto.Side} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setSide = function(value) {
  return jspb.Message.setProto3EnumField(this, 28, value);
};


/**
 * optional string external_order_id = 29;
 * @return {string}
 */
proto.Order.prototype.getExternalOrderId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 29, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setExternalOrderId = function(value) {
  return jspb.Message.setProto3StringField(this, 29, value);
};


/**
 * optional string currency_code = 30;
 * @return {string}
 */
proto.Order.prototype.getCurrencyCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 30, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setCurrencyCode = function(value) {
  return jspb.Message.setProto3StringField(this, 30, value);
};


/**
 * optional string total_filled_amount = 31;
 * @return {string}
 */
proto.Order.prototype.getTotalFilledAmount = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 31, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setTotalFilledAmount = function(value) {
  return jspb.Message.setProto3StringField(this, 31, value);
};


/**
 * optional string comment = 32;
 * @return {string}
 */
proto.Order.prototype.getComment = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 32, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setComment = function(value) {
  return jspb.Message.setProto3StringField(this, 32, value);
};


/**
 * optional string order_ref = 33;
 * @return {string}
 */
proto.Order.prototype.getOrderRef = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 33, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setOrderRef = function(value) {
  return jspb.Message.setProto3StringField(this, 33, value);
};


/**
 * optional string withdraw_amount = 34;
 * @return {string}
 */
proto.Order.prototype.getWithdrawAmount = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 34, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setWithdrawAmount = function(value) {
  return jspb.Message.setProto3StringField(this, 34, value);
};


/**
 * optional string submitted_id = 35;
 * @return {string}
 */
proto.Order.prototype.getSubmittedId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 35, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setSubmittedId = function(value) {
  return jspb.Message.setProto3StringField(this, 35, value);
};


/**
 * optional MsgCode msg_code = 36;
 * @return {!proto.MsgCode}
 */
proto.Order.prototype.getMsgCode = function() {
  return /** @type {!proto.MsgCode} */ (jspb.Message.getFieldWithDefault(this, 36, 0));
};


/**
 * @param {!proto.MsgCode} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setMsgCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 36, value);
};


/**
 * optional GroupType group_type = 37;
 * @return {!proto.GroupType}
 */
proto.Order.prototype.getGroupType = function() {
  return /** @type {!proto.GroupType} */ (jspb.Message.getFieldWithDefault(this, 37, 0));
};


/**
 * @param {!proto.GroupType} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setGroupType = function(value) {
  return jspb.Message.setProto3EnumField(this, 37, value);
};


/**
 * optional ExternalState external_state = 38;
 * @return {!proto.ExternalState}
 */
proto.Order.prototype.getExternalState = function() {
  return /** @type {!proto.ExternalState} */ (jspb.Message.getFieldWithDefault(this, 38, 0));
};


/**
 * @param {!proto.ExternalState} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setExternalState = function(value) {
  return jspb.Message.setProto3EnumField(this, 38, value);
};


/**
 * optional bool allow_matching = 39;
 * @return {boolean}
 */
proto.Order.prototype.getAllowMatching = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 39, false));
};


/**
 * @param {boolean} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setAllowMatching = function(value) {
  return jspb.Message.setProto3BooleanField(this, 39, value);
};


/**
 * optional bool existed_in_bo = 40;
 * @return {boolean}
 */
proto.Order.prototype.getExistedInBo = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 40, false));
};


/**
 * @param {boolean} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setExistedInBo = function(value) {
  return jspb.Message.setProto3BooleanField(this, 40, value);
};


/**
 * optional string order_market_code = 41;
 * @return {string}
 */
proto.Order.prototype.getOrderMarketCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 41, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setOrderMarketCode = function(value) {
  return jspb.Message.setProto3StringField(this, 41, value);
};


/**
 * optional string executed_market_code = 42;
 * @return {string}
 */
proto.Order.prototype.getExecutedMarketCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 42, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setExecutedMarketCode = function(value) {
  return jspb.Message.setProto3StringField(this, 42, value);
};


/**
 * optional string cq_b2b_account_no = 43;
 * @return {string}
 */
proto.Order.prototype.getCqB2bAccountNo = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 43, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setCqB2bAccountNo = function(value) {
  return jspb.Message.setProto3StringField(this, 43, value);
};


/**
 * optional string cq_cl_set_currency = 44;
 * @return {string}
 */
proto.Order.prototype.getCqClSetCurrency = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 44, ""));
};


/**
 * @param {string} value
 * @return {!proto.Order} returns this
 */
proto.Order.prototype.setCqClSetCurrency = function(value) {
  return jspb.Message.setProto3StringField(this, 44, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.Trade.prototype.toObject = function(opt_includeInstance) {
  return proto.Trade.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.Trade} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Trade.toObject = function(includeInstance, msg) {
  var f, obj = {
    orderId: jspb.Message.getFieldWithDefault(msg, 1, ""),
    tickerCode: jspb.Message.getFieldWithDefault(msg, 2, ""),
    tickerName: jspb.Message.getFieldWithDefault(msg, 3, ""),
    orderType: jspb.Message.getFieldWithDefault(msg, 4, 0),
    amount: jspb.Message.getFieldWithDefault(msg, 5, ""),
    price: jspb.Message.getFieldWithDefault(msg, 6, ""),
    executedVolume: jspb.Message.getFieldWithDefault(msg, 7, ""),
    executedPrice: jspb.Message.getFieldWithDefault(msg, 8, ""),
    matchedValue: jspb.Message.getFieldWithDefault(msg, 9, ""),
    executedDatetime: jspb.Message.getFieldWithDefault(msg, 10, ""),
    side: jspb.Message.getFieldWithDefault(msg, 11, 0),
    externalOrderId: jspb.Message.getFieldWithDefault(msg, 12, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.Trade}
 */
proto.Trade.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.Trade;
  return proto.Trade.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.Trade} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.Trade}
 */
proto.Trade.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setOrderId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setTickerCode(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setTickerName(value);
      break;
    case 4:
      var value = /** @type {!proto.OrderType} */ (reader.readEnum());
      msg.setOrderType(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setAmount(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setPrice(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setExecutedVolume(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setExecutedPrice(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setMatchedValue(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setExecutedDatetime(value);
      break;
    case 11:
      var value = /** @type {!proto.Side} */ (reader.readEnum());
      msg.setSide(value);
      break;
    case 12:
      var value = /** @type {string} */ (reader.readString());
      msg.setExternalOrderId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.Trade.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.Trade.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.Trade} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Trade.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOrderId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getTickerCode();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getTickerName();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getOrderType();
  if (f !== 0.0) {
    writer.writeEnum(
      4,
      f
    );
  }
  f = message.getAmount();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getPrice();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getExecutedVolume();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getExecutedPrice();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getMatchedValue();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getExecutedDatetime();
  if (f.length > 0) {
    writer.writeString(
      10,
      f
    );
  }
  f = message.getSide();
  if (f !== 0.0) {
    writer.writeEnum(
      11,
      f
    );
  }
  f = message.getExternalOrderId();
  if (f.length > 0) {
    writer.writeString(
      12,
      f
    );
  }
};


/**
 * optional string order_id = 1;
 * @return {string}
 */
proto.Trade.prototype.getOrderId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.Trade} returns this
 */
proto.Trade.prototype.setOrderId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string ticker_code = 2;
 * @return {string}
 */
proto.Trade.prototype.getTickerCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.Trade} returns this
 */
proto.Trade.prototype.setTickerCode = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string ticker_name = 3;
 * @return {string}
 */
proto.Trade.prototype.getTickerName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.Trade} returns this
 */
proto.Trade.prototype.setTickerName = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional OrderType order_type = 4;
 * @return {!proto.OrderType}
 */
proto.Trade.prototype.getOrderType = function() {
  return /** @type {!proto.OrderType} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {!proto.OrderType} value
 * @return {!proto.Trade} returns this
 */
proto.Trade.prototype.setOrderType = function(value) {
  return jspb.Message.setProto3EnumField(this, 4, value);
};


/**
 * optional string amount = 5;
 * @return {string}
 */
proto.Trade.prototype.getAmount = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.Trade} returns this
 */
proto.Trade.prototype.setAmount = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string price = 6;
 * @return {string}
 */
proto.Trade.prototype.getPrice = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.Trade} returns this
 */
proto.Trade.prototype.setPrice = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string executed_volume = 7;
 * @return {string}
 */
proto.Trade.prototype.getExecutedVolume = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.Trade} returns this
 */
proto.Trade.prototype.setExecutedVolume = function(value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string executed_price = 8;
 * @return {string}
 */
proto.Trade.prototype.getExecutedPrice = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/**
 * @param {string} value
 * @return {!proto.Trade} returns this
 */
proto.Trade.prototype.setExecutedPrice = function(value) {
  return jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional string matched_value = 9;
 * @return {string}
 */
proto.Trade.prototype.getMatchedValue = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/**
 * @param {string} value
 * @return {!proto.Trade} returns this
 */
proto.Trade.prototype.setMatchedValue = function(value) {
  return jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional string executed_datetime = 10;
 * @return {string}
 */
proto.Trade.prototype.getExecutedDatetime = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/**
 * @param {string} value
 * @return {!proto.Trade} returns this
 */
proto.Trade.prototype.setExecutedDatetime = function(value) {
  return jspb.Message.setProto3StringField(this, 10, value);
};


/**
 * optional Side side = 11;
 * @return {!proto.Side}
 */
proto.Trade.prototype.getSide = function() {
  return /** @type {!proto.Side} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/**
 * @param {!proto.Side} value
 * @return {!proto.Trade} returns this
 */
proto.Trade.prototype.setSide = function(value) {
  return jspb.Message.setProto3EnumField(this, 11, value);
};


/**
 * optional string external_order_id = 12;
 * @return {string}
 */
proto.Trade.prototype.getExternalOrderId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 12, ""));
};


/**
 * @param {string} value
 * @return {!proto.Trade} returns this
 */
proto.Trade.prototype.setExternalOrderId = function(value) {
  return jspb.Message.setProto3StringField(this, 12, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.Contract.prototype.toObject = function(opt_includeInstance) {
  return proto.Contract.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.Contract} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Contract.toObject = function(includeInstance, msg) {
  var f, obj = {
    orderId: jspb.Message.getFieldWithDefault(msg, 1, ""),
    symbolCode: jspb.Message.getFieldWithDefault(msg, 2, ""),
    exchangeCode: jspb.Message.getFieldWithDefault(msg, 3, 0),
    orderType: jspb.Message.getFieldWithDefault(msg, 4, 0),
    state: jspb.Message.getFieldWithDefault(msg, 5, 0),
    qty: jspb.Message.getFieldWithDefault(msg, 6, ""),
    settleQty: jspb.Message.getFieldWithDefault(msg, 7, ""),
    orderMode: jspb.Message.getFieldWithDefault(msg, 8, 0),
    cp: jspb.Message.getFieldWithDefault(msg, 9, 0),
    strikePrice: jspb.Message.getFieldWithDefault(msg, 10, ""),
    limitPrice: jspb.Message.getFieldWithDefault(msg, 11, ""),
    settlePrice: jspb.Message.getFieldWithDefault(msg, 12, ""),
    positionPrice: jspb.Message.getFieldWithDefault(msg, 13, ""),
    expireTime: jspb.Message.getFieldWithDefault(msg, 14, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.Contract}
 */
proto.Contract.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.Contract;
  return proto.Contract.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.Contract} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.Contract}
 */
proto.Contract.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setOrderId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSymbolCode(value);
      break;
    case 3:
      var value = /** @type {!proto.ExchangeCode} */ (reader.readEnum());
      msg.setExchangeCode(value);
      break;
    case 4:
      var value = /** @type {!proto.OrderType} */ (reader.readEnum());
      msg.setOrderType(value);
      break;
    case 5:
      var value = /** @type {!proto.OrderState} */ (reader.readEnum());
      msg.setState(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setQty(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setSettleQty(value);
      break;
    case 8:
      var value = /** @type {!proto.OrderMode} */ (reader.readEnum());
      msg.setOrderMode(value);
      break;
    case 9:
      var value = /** @type {!proto.CallPut} */ (reader.readEnum());
      msg.setCp(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setStrikePrice(value);
      break;
    case 11:
      var value = /** @type {string} */ (reader.readString());
      msg.setLimitPrice(value);
      break;
    case 12:
      var value = /** @type {string} */ (reader.readString());
      msg.setSettlePrice(value);
      break;
    case 13:
      var value = /** @type {string} */ (reader.readString());
      msg.setPositionPrice(value);
      break;
    case 14:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setExpireTime(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.Contract.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.Contract.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.Contract} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Contract.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOrderId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSymbolCode();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getExchangeCode();
  if (f !== 0.0) {
    writer.writeEnum(
      3,
      f
    );
  }
  f = message.getOrderType();
  if (f !== 0.0) {
    writer.writeEnum(
      4,
      f
    );
  }
  f = message.getState();
  if (f !== 0.0) {
    writer.writeEnum(
      5,
      f
    );
  }
  f = message.getQty();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getSettleQty();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getOrderMode();
  if (f !== 0.0) {
    writer.writeEnum(
      8,
      f
    );
  }
  f = message.getCp();
  if (f !== 0.0) {
    writer.writeEnum(
      9,
      f
    );
  }
  f = message.getStrikePrice();
  if (f.length > 0) {
    writer.writeString(
      10,
      f
    );
  }
  f = message.getLimitPrice();
  if (f.length > 0) {
    writer.writeString(
      11,
      f
    );
  }
  f = message.getSettlePrice();
  if (f.length > 0) {
    writer.writeString(
      12,
      f
    );
  }
  f = message.getPositionPrice();
  if (f.length > 0) {
    writer.writeString(
      13,
      f
    );
  }
  f = message.getExpireTime();
  if (f !== 0) {
    writer.writeInt64(
      14,
      f
    );
  }
};


/**
 * optional string order_id = 1;
 * @return {string}
 */
proto.Contract.prototype.getOrderId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.Contract} returns this
 */
proto.Contract.prototype.setOrderId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string symbol_code = 2;
 * @return {string}
 */
proto.Contract.prototype.getSymbolCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.Contract} returns this
 */
proto.Contract.prototype.setSymbolCode = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional ExchangeCode exchange_code = 3;
 * @return {!proto.ExchangeCode}
 */
proto.Contract.prototype.getExchangeCode = function() {
  return /** @type {!proto.ExchangeCode} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {!proto.ExchangeCode} value
 * @return {!proto.Contract} returns this
 */
proto.Contract.prototype.setExchangeCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 3, value);
};


/**
 * optional OrderType order_type = 4;
 * @return {!proto.OrderType}
 */
proto.Contract.prototype.getOrderType = function() {
  return /** @type {!proto.OrderType} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {!proto.OrderType} value
 * @return {!proto.Contract} returns this
 */
proto.Contract.prototype.setOrderType = function(value) {
  return jspb.Message.setProto3EnumField(this, 4, value);
};


/**
 * optional OrderState state = 5;
 * @return {!proto.OrderState}
 */
proto.Contract.prototype.getState = function() {
  return /** @type {!proto.OrderState} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {!proto.OrderState} value
 * @return {!proto.Contract} returns this
 */
proto.Contract.prototype.setState = function(value) {
  return jspb.Message.setProto3EnumField(this, 5, value);
};


/**
 * optional string qty = 6;
 * @return {string}
 */
proto.Contract.prototype.getQty = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.Contract} returns this
 */
proto.Contract.prototype.setQty = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string settle_qty = 7;
 * @return {string}
 */
proto.Contract.prototype.getSettleQty = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.Contract} returns this
 */
proto.Contract.prototype.setSettleQty = function(value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional OrderMode order_mode = 8;
 * @return {!proto.OrderMode}
 */
proto.Contract.prototype.getOrderMode = function() {
  return /** @type {!proto.OrderMode} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/**
 * @param {!proto.OrderMode} value
 * @return {!proto.Contract} returns this
 */
proto.Contract.prototype.setOrderMode = function(value) {
  return jspb.Message.setProto3EnumField(this, 8, value);
};


/**
 * optional CallPut cp = 9;
 * @return {!proto.CallPut}
 */
proto.Contract.prototype.getCp = function() {
  return /** @type {!proto.CallPut} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/**
 * @param {!proto.CallPut} value
 * @return {!proto.Contract} returns this
 */
proto.Contract.prototype.setCp = function(value) {
  return jspb.Message.setProto3EnumField(this, 9, value);
};


/**
 * optional string strike_price = 10;
 * @return {string}
 */
proto.Contract.prototype.getStrikePrice = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/**
 * @param {string} value
 * @return {!proto.Contract} returns this
 */
proto.Contract.prototype.setStrikePrice = function(value) {
  return jspb.Message.setProto3StringField(this, 10, value);
};


/**
 * optional string limit_price = 11;
 * @return {string}
 */
proto.Contract.prototype.getLimitPrice = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};


/**
 * @param {string} value
 * @return {!proto.Contract} returns this
 */
proto.Contract.prototype.setLimitPrice = function(value) {
  return jspb.Message.setProto3StringField(this, 11, value);
};


/**
 * optional string settle_price = 12;
 * @return {string}
 */
proto.Contract.prototype.getSettlePrice = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 12, ""));
};


/**
 * @param {string} value
 * @return {!proto.Contract} returns this
 */
proto.Contract.prototype.setSettlePrice = function(value) {
  return jspb.Message.setProto3StringField(this, 12, value);
};


/**
 * optional string position_price = 13;
 * @return {string}
 */
proto.Contract.prototype.getPositionPrice = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 13, ""));
};


/**
 * @param {string} value
 * @return {!proto.Contract} returns this
 */
proto.Contract.prototype.setPositionPrice = function(value) {
  return jspb.Message.setProto3StringField(this, 13, value);
};


/**
 * optional int64 expire_time = 14;
 * @return {number}
 */
proto.Contract.prototype.getExpireTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 14, 0));
};


/**
 * @param {number} value
 * @return {!proto.Contract} returns this
 */
proto.Contract.prototype.setExpireTime = function(value) {
  return jspb.Message.setProto3IntField(this, 14, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.Position.prototype.toObject = function(opt_includeInstance) {
  return proto.Position.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.Position} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Position.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, ""),
    symbol: jspb.Message.getFieldWithDefault(msg, 2, ""),
    vwapPrice: jspb.Message.getFieldWithDefault(msg, 3, ""),
    amount: jspb.Message.getFieldWithDefault(msg, 4, ""),
    marginRequire: jspb.Message.getFieldWithDefault(msg, 5, ""),
    swapPl: jspb.Message.getFieldWithDefault(msg, 6, ""),
    mamCode: jspb.Message.getFieldWithDefault(msg, 7, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.Position}
 */
proto.Position.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.Position;
  return proto.Position.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.Position} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.Position}
 */
proto.Position.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSymbol(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setVwapPrice(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setAmount(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setMarginRequire(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setSwapPl(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setMamCode(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.Position.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.Position.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.Position} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Position.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSymbol();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getVwapPrice();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getAmount();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getMarginRequire();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getSwapPl();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getMamCode();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.Position.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.Position} returns this
 */
proto.Position.prototype.setId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string symbol = 2;
 * @return {string}
 */
proto.Position.prototype.getSymbol = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.Position} returns this
 */
proto.Position.prototype.setSymbol = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string vwap_price = 3;
 * @return {string}
 */
proto.Position.prototype.getVwapPrice = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.Position} returns this
 */
proto.Position.prototype.setVwapPrice = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string amount = 4;
 * @return {string}
 */
proto.Position.prototype.getAmount = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.Position} returns this
 */
proto.Position.prototype.setAmount = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string margin_require = 5;
 * @return {string}
 */
proto.Position.prototype.getMarginRequire = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.Position} returns this
 */
proto.Position.prototype.setMarginRequire = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string swap_pl = 6;
 * @return {string}
 */
proto.Position.prototype.getSwapPl = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.Position} returns this
 */
proto.Position.prototype.setSwapPl = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string mam_code = 7;
 * @return {string}
 */
proto.Position.prototype.getMamCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.Position} returns this
 */
proto.Position.prototype.setMamCode = function(value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * @enum {number}
 */
proto.ExchangeCode = {
  EXCHANGE_CODE_NONE: 0,
  EXCHANGE_TSE: 1,
  EXCHANGE_NSE: 3,
  EXCHANGE_FSE: 6,
  EXCHANGE_SSE: 8,
  EXCHANGE_OSE: 10,
  EXCHANGE_TOCOM: 11
};

/**
 * @enum {number}
 */
proto.OrderType = {
  OP_NONE: 0,
  OP_LIMIT: 1,
  OP_STOP: 2,
  OP_MARKET: 3,
  OP_STOP_LIMIT: 4,
  OP_MTL: 5,
  OP_STM: 6
};

/**
 * @enum {number}
 */
proto.ExecutionMode = {
  EXECUTION_MODE_NONE: 0,
  MARKET: 1,
  INSTANT: 2
};

/**
 * @enum {number}
 */
proto.OrderMode = {
  ORDER_MODE_NONE: 0,
  OCO: 10,
  IFD: 20,
  IFO: 30,
  REGULAR: 40
};

/**
 * @enum {number}
 */
proto.OrderFilling = {
  ORDER_FILL_NONE: 0,
  ORDER_FILL_FAK: 5,
  ORDER_FILL_FAS: 6,
  ORDER_FILL_FOK: 7
};

/**
 * @enum {number}
 */
proto.OrderTime = {
  ORDER_TIME_NONE: 0,
  ORDER_TIME_DAY: 1,
  ORDER_TIME_SPECIFIED: 2,
  ORDER_TIME_SPECIFIED_DAY: 3,
  ORDER_TIME_GTC: 11
};

/**
 * @enum {number}
 */
proto.OrderState = {
  ORDER_STATE_NONE: 0,
  ORDER_STATE_PLACED: 1,
  ORDER_STATE_CANCELED: 2,
  ORDER_STATE_PARTIAL: 3,
  ORDER_STATE_FILLED: 4,
  ORDER_STATE_REJECTED: 5,
  ORDER_STATE_EXPIRED: 6,
  ORDER_STATE_MODIFIED: 7,
  ORDER_STATE_MATCHED: 10,
  ORDER_STATE_STARTED: 11
};

/**
 * @enum {number}
 */
proto.OrderEntry = {
  ENTRY_NONE: 0,
  ENTRY_OUT: 1,
  ENTRY_IN: 2
};

/**
 * @enum {number}
 */
proto.Side = {
  NONE: 0,
  BUY: 1,
  SELL: 2
};

/**
 * @enum {number}
 */
proto.OrderReason = {
  REASON_NONE: 0,
  REASON_API: 1,
  REASON_DEALER: 2,
  REASON_SL: 3,
  REASON_TP: 4,
  REASON_SO: 5,
  REASON_ROLLOVER: 6,
  REASON_CLIENT: 10
};

/**
 * @enum {number}
 */
proto.OrderRoute = {
  ROUTE_NONE: 0,
  ROUTE_IOS: 1,
  ROUTE_ANDROID: 2,
  ROUTE_BACK: 3,
  ROUTE_API: 4,
  ROUTE_WEB: 5,
  ROUTE_FIX: 6,
  ROUTE_CQ: 7
};

/**
 * @enum {number}
 */
proto.CallPut = {
  CALL_PUT_NONE: 0,
  PUT: 1,
  CALL: 2
};

/**
 * @enum {number}
 */
proto.ModifyType = {
  MODIFY_TYPE_NONE: 0,
  CANCEL: 2,
  UPDATE: 3
};

/**
 * @enum {number}
 */
proto.ExternalState = {
  EXTERNAL_STATE_NONE: 0,
  EXTERNAL_STATE_WAITING: 1,
  EXTERNAL_STATE_RECEIVED: 2,
  EXTERNAL_STATE_NEW: 3,
  EXTERNAL_STATE_CANCELED: 4,
  EXTERNAL_STATE_PARTIAL: 5,
  EXTERNAL_STATE_FILLED: 6,
  EXTERNAL_STATE_REJECTED: 7,
  EXTERNAL_STATE_EXPIRED: 8,
  EXTERNAL_STATE_MODIFIED: 9,
  EXTERNAL_STATE_UNLOCK: 10,
  EXTERNAL_STATE_CANCEL_REJECT: 11
};

goog.object.extend(exports, proto);
