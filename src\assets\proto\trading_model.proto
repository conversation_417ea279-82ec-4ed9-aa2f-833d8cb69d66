syntax = "proto3";
import "system_model.proto";

message Order {
  string order_id 		= 1;
  string symbol_code 			= 2;
  OrderType order_type 			= 3;
  OrderState state 				= 4;
  string amount 				= 5;
  string price 		= 6;
  string slippage 		= 7;
  OrderFilling order_filling 	= 8;
  ExecutionMode execute_mode 	= 9;
  OrderReason reason 			= 10;
  OrderTime order_time 			= 11;
  int64 expire_time 	= 12; // Timestamp in milliseconds
  string tp 			= 13;
  string sl 			= 14;
  string pl 			= 15;
  string swap 			= 16;
  string fee 			= 17;
  int64 time 			= 18; // Timestamp in milliseconds
  string note 			= 19;
  OrderEntry entry 				= 20;
  OrderRoute route 				= 21;
  OrderMode	order_mode			= 22;
  int64 uid = 23;
  string average_price   = 24;
  string filled_amount = 25;
  string last_price = 26;
  string executed_datetime = 27;
  Side side					= 28;
  string external_order_id			= 29;
  string currency_code				= 30;
  string total_filled_amount		= 31;
  string comment					= 32;
  // Order Reference ID, E.g: place new order, id = 1 => order_ref = 1
  // Modify order id = 1 => new order after modified, id = 2, ref = 1
  string order_ref = 33; 
  string withdraw_amount = 34; // Withdraw able amount or Withdrew amount
  string submitted_id = 35;
  MsgCode msg_code = 36;
  GroupType group_type = 37;
  ExternalState external_state = 38; // External system result state
  bool allow_matching = 39; // True -> Allow matching in USAH, False -> Not allow matching in USAH
  bool existed_in_bo = 40; // True -> Order is existing in BO, False -> Order isn't existing in BO
  string order_market_code = 41; // For us24: market where order has been placed
  string executed_market_code = 42; // For us24: market where order has been matched
  string cq_b2b_account_no = 43; // For CQ: Account Holder of CQ B2B Client
  string cq_cl_set_currency = 44; // For CQ: Currency client set for process settlement
}

message Trade {
	string order_id				= 1;
	string ticker_code			= 2;
	string ticker_name			= 3;
	OrderType order_type 		= 4;
	string amount				= 5;
	string price 				= 6;
	string executed_volume 		= 7;
	string executed_price		= 8;
	string matched_value 		= 9;
	string executed_datetime	= 10;
	Side side					= 11;
	string external_order_id			= 12;
}

message Contract {
  string order_id 		= 1;
  string symbol_code 			= 2;	//stock code
  ExchangeCode exchange_code 	= 3;
  OrderType order_type 			= 4;
  OrderState state 				= 5;
  string qty	 				= 6;
  string settle_qty 			= 7;
  OrderMode	order_mode			= 8;
  CallPut cp					= 9;
  string strike_price  = 10;
  string limit_price  	= 11;
  string settle_price  = 12;
  string position_price= 13;
  int64 expire_time 	= 14; // Timestamp in milliseconds

}

message Position {
  string id = 1;
  string symbol = 2;
  string vwap_price = 3;
  string amount = 4;
  string margin_require = 5;
  string swap_pl = 6;
  string mam_code = 7;
}

enum ExchangeCode {
	EXCHANGE_CODE_NONE = 0;
	EXCHANGE_TSE  = 1; 		//Tokyo Stock Exchange
	EXCHANGE_NSE  = 3; 		//Nagoya Stock Exchange
	EXCHANGE_FSE  = 6; 		//Fukuoka Stock Exchange
	EXCHANGE_SSE  = 8; 		//Fukuoka Stock Exchange
	EXCHANGE_OSE  = 10; 	//Osaka Stock Exchange
	EXCHANGE_TOCOM  = 11; 	//Tokyo Commodity Exchange
}

enum OrderType {
	OP_NONE 	= 0;
	OP_LIMIT 		= 1;
	OP_STOP 		= 2;
	OP_MARKET 		= 3;
	OP_STOP_LIMIT 		= 4;
   	OP_MTL 			= 5;		//Market to Limit
  	OP_STM 			= 6;
}
enum ExecutionMode {
 	EXECUTION_MODE_NONE = 0;
 	MARKET = 1;
	INSTANT = 2;
}
 enum OrderMode {
	ORDER_MODE_NONE= 0;
 	OCO = 10;
 	IFD = 20;
 	IFO = 30;
	REGULAR = 40;
 }
 enum OrderFilling {
	ORDER_FILL_NONE = 0;
 	ORDER_FILL_FAK = 5;
 	ORDER_FILL_FAS = 6;
	ORDER_FILL_FOK = 7;
 }
 enum OrderTime {
 	ORDER_TIME_NONE = 0;
 	ORDER_TIME_DAY = 1;
 	ORDER_TIME_SPECIFIED = 2;
 	ORDER_TIME_SPECIFIED_DAY = 3;
	ORDER_TIME_GTC = 11;
 }
 enum OrderState {
 	ORDER_STATE_NONE = 0;
 	ORDER_STATE_PLACED = 1; 	//Placed — for pending orders, this state means that the order has been accepted and placed.
 	ORDER_STATE_CANCELED = 2;	//Canceled — the order has been canceled by the client.
 	ORDER_STATE_PARTIAL = 3;	//Partially filled — the order has been partially filled, so the market operation has been executed for part of the initially requested volume.
 	ORDER_STATE_FILLED = 4;		//Filled — the order has been filled in full.
 	ORDER_STATE_REJECTED = 5;	//Rejected — the order has been rejected by the broker.
 	ORDER_STATE_EXPIRED = 6;	//Expired — the order has been canceled upon expiration.
	ORDER_STATE_MODIFIED = 7;	//Modified — the order has been modified
	ORDER_STATE_MATCHED = 10;	//Matched by ME system
	ORDER_STATE_STARTED = 11; 	//Started — the order correctness has been checked, and now the order is awaiting processing.
 }

 enum OrderEntry {
	ENTRY_NONE = 0;
 	ENTRY_OUT = 1;	//Close Order
	ENTRY_IN = 2;	//New order
 }

 enum Side {
	NONE = 0;
	BUY = 1;
	SELL = 2;
}

 enum OrderReason {
 	REASON_NONE = 0;
 	REASON_API = 1;
 	REASON_DEALER = 2;
 	REASON_SL = 3;
 	REASON_TP = 4;
 	REASON_SO = 5;
 	REASON_ROLLOVER = 6;
	REASON_CLIENT = 10;
 }

enum OrderRoute {
	ROUTE_NONE = 0;
	ROUTE_IOS = 1;
	ROUTE_ANDROID = 2;
	ROUTE_BACK = 3;
	ROUTE_API = 4;
	ROUTE_WEB = 5;
	ROUTE_FIX = 6; // Come from FIX ENGINE
	ROUTE_CQ = 7; // Come from CQ
}

enum CallPut {
	CALL_PUT_NONE = 0;
	PUT = 1;
	CALL = 2;
}

enum ModifyType {
	MODIFY_TYPE_NONE = 0;
 	CANCEL = 2;
	UPDATE = 3;
}

enum ExternalState {
	EXTERNAL_STATE_NONE = 0;
	EXTERNAL_STATE_WAITING = 1; // Wating for received external trading system result for an order
	EXTERNAL_STATE_RECEIVED = 2; // Already received result for an order
  EXTERNAL_STATE_NEW = 3;   // Cowen accepted the order
  EXTERNAL_STATE_CANCELED = 4; // Cowen canceled the order
  EXTERNAL_STATE_PARTIAL = 5;  // Partial Filled
  EXTERNAL_STATE_FILLED = 6;   // Full Filled
  EXTERNAL_STATE_REJECTED = 7; // Cowen rejected the order
  EXTERNAL_STATE_EXPIRED = 8;  // Expired — the order has been canceled upon expiration.
  EXTERNAL_STATE_MODIFIED = 9; // Order has been modified success (REPLACED ER)
  EXTERNAL_STATE_UNLOCK = 10; // Use this enum to unlock order from USAH core, show in orderbook
  EXTERNAL_STATE_CANCEL_REJECT = 11; // Send cancel request to BO but received ER = CANCEL_REJECT
}