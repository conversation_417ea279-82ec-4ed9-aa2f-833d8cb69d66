{"name": "poem-square-retail", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/material": "^5.15.15", "@testing-library/jest-dom": "^5.16.2", "@testing-library/react": "^12.1.3", "@testing-library/user-event": "^13.5.0", "axios": "^1.8.4", "bootstrap": "^5.1.3", "bootstrap-icons": "^1.8.1", "cookies-next": "^2.0.4", "decimal.js": "^10.3.1", "file-saver": "^2.0.5", "google-protobuf": "^3.20.0-rc.1", "html-react-parser": "^1.4.8", "i18next": "^21.6.13", "i18next-http-backend": "^1.3.2", "jquery": "^3.6.0", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "moment": "^2.29.1", "query-string": "^7.1.1", "react": "^17.0.2", "react-bootstrap": "^2.2.0", "react-dom": "^17.0.2", "react-helmet": "^6.1.0", "react-i18next": "^11.15.5", "react-js-pagination": "^3.0.3", "react-number-format": "^4.9.1", "react-redux": "^8.0.1", "react-router-dom": "^6.2.2", "react-scripts": "5.0.0", "react-toastify": "^8.2.0", "react-virtualized": "^9.22.5", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-saga": "^1.1.3", "redux-thunk": "^2.4.1", "rxjs": "^7.5.4", "web-vitals": "^2.1.4", "xlsx": "^0.18.3"}, "scripts": {"start": "react-app-rewired start", "build": "react-app-rewired build && (rd /s /q build-retail && move build build-retail) || move build build-retail", "test": "react-app-rewired test", "eject": "react-app-rewired eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "homepage": "/retail", "devDependencies": {"react-app-rewired": "^2.2.1", "typescript-plugin-css-modules": "^3.4.0"}}