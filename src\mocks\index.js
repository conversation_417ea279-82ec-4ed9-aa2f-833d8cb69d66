import { CURRENCY } from "../model/constant"

export const mockListSymbol = [
    {
        name: "Apple Inc.",
        symbol: "APH",
        lastPrice: 0.210,
        chg: -0.01,
        chgPercent: -0.955,
        bvol: 0.297,
        bid: 0.987,
        ask: 0.983,
        svol: 0.443,
        vol: 0.452,
        open: 0.434,
        high: 0.432,
        low: 0.345,
        preClose: 0.432
    },
    {
        name: "Facebook Inc.",
        symbol: "FBK",
        lastPrice: 0.210,
        chg: 1.01,
        chgPercent: 1.955,
        bvol: 0.297,
        bid: 0.987,
        ask: 0.983,
        svol: 0.443,
        vol: 0.452,
        open: 0.434,
        high: 0.432,
        low: 0.345,
        preClose: 0.432
    }
]

export const mockOrderBook = [
    {
        bid: 113.434,
        ask: 114.232,
        bvol: 15,
        svol: 11,
        buyPercent: 34,
        sellPercent: 45
    },
    {
        bid: 114.434,
        ask: 115.232,
        bvol: 17,
        svol: 23,
        buyPercent: 54,
        sellPercent: 100
    }
]

export const mockOrderList = [
    {
        orderId: "113342",
        name: "Apple Inc",
        symbol: "AAPL",
        status: "Received",
        price: 3818,
        qty: 11,
        side: "Buy",
        msg: "Order Received",
        time: "Feb 14 2022 09:30AM",
        remaining: 1
    },
    {
        orderId: "113343",
        name: "Tesla",
        symbol: "TLSA",
        status: "Withdrawn",
        price: 3818,
        qty: 11,
        side: "Sell",
        msg: "Order Received",
        time: "Feb 14 2022 09:30AM",
        remaining: 1
    },
    {
        orderId: "113344",
        name: "Apple Inc",
        symbol: "AAPL",
        status: "Done",
        price: 3818,
        qty: 11,
        side: "Buy",
        msg: "Order Received",
        time: "Feb 14 2022 09:30AM",
        remaining: 1
    },
    {
        orderId: "113345",
        name: "Apple Inc",
        symbol: "AAPL",
        status: "Rejected",
        price: 3818,
        qty: 11,
        side: "Buy",
        msg: "Order Received",
        time: "Feb 14 2022 09:30AM",
        remaining: 1
    },
    {
        orderId: "113346",
        name: "Apple Inc",
        symbol: "AAPL",
        status: "Received",
        price: 3818,
        qty: 11,
        side: "Buy",
        msg: "Order Received",
        time: "Feb 14 2022 09:30AM",
        remaining: 1
    }
]

export const mockNotificationList = [
    {
        type: "PI-X News",
        title:"Dear valued Customer, PI-X will update the system during 8:00 - 10:00",
        content: "Dear valued Customer, PI-X will update the system during 8:00 - 10:00 on platea lacus mus aliquam potenti ad eu turpis vitae quisque gravida eleifend.",
        time: "25/20/2020 12:40:22"
    },
    {
        type: "PI-X News",
        title: "They shoot at anyone who tries to leave.' Ukrainians describe terror",
        content: "A volley of machine-gun fire erupted just as Andriy Abba's family raised a toast to celebrate his 30th birthday in Kherson. Wine glass in hand, he rushed with his parents and younger brother to the basement.",
        time: "22/20/2020 12:40:22"
    },
    {
        type: "PI-X News",
        title: "Kolykhaiev said that the Russian forces had settled in to the city, and showed no signs of leaving.",
        content: "Kherson, a key port city on the Black Sea, in southern Ukraine, was overrun by Russian forces in the early hours of Wednesday, after days of heavy bombardment and shelling. The Ukrainian flag was still hoisted on government buildings, and the mayor of the city, Ihor Kolykhaiev, remained in his post.",
        time: "22/20/2020 12:40:22"
    },
    {
        type: "PI-X News",
        title: "People living in Kherson under Russian occupation describe days of terror confined to their apartments and houses",
        content: "Checkpoints manned by Russian troops pepper the city's streets, five Kherson residents told CNN in recent phone calls. The roads are virtually empty because inhabitants have either fled the fighting, or are staying indoors for fear of encountering Russian soldiers. Grocery stores have been emptied and medicine is running out, residents and officials said.",
        time: "22/20/2020 12:40:22"
    }
]

export const rateDefault = 1.37;

export const paramOrderDefault = {
    side: "buy",
    quantity: 0,
    ccy: CURRENCY.USD,
    cashBalance: 0,
    maxPurchase: 0,
    sellLimit: 0,
    grossValue: 0
}

export const clientInfoDataDefault = {
    acctType: "",
    buyLimit: 0,
    cashBalance: 0,
    counterGrade: "",
    rate: 0,
    sellLimit: 0
}

export const CounterGradeValueM = {
    A: 3.33,
    B: 2,
    C: 1,
    E: 1.42,
    S: 3.33
}

export const CounterGradeValueV = {
    A: 3.33,
    B: 2,
    C: 1,
    E: 1,
    S: 5
}

export const DEFAULT_SORT_DASHBOARD = {
    symbolName: false,
    symbolCode: false,
    lastPrice: false,
    change: false,
    percentChange: false,
    bVol: false,
    bid: false,
    ask: false,
    sVol: false,
    vol: false,
    open: false,
    high: false,
    low: false,
    preClose: false
}

export const DEFAULT_SORT_ORDER_STATUS = {
    product: false,
    orderNo: false,
    name: false,
    symbol: false,
    orderStatus: false,
    submitted: false,
    action: false,
    message: false,
    submittedDate: false,
    vol: false,
    dateTime: false,
    currency: false,
}