import { useState,useEffect } from "react";
import Header from "../components/header";
import { Pages } from "../model/constant";
import OutstandingPosition from "../components/outstanding-position";
import AccountDetail from "../components/account-detail";
import ScriptPosition from "../components/script-position";
import MonthTransaction from "../components/month-transaction";
import TransactionHistory from "../components/transaction-history";
import SettledPosition from "../components/settled-position";

const AccountManagement = () => {
    const [page, setPage] = useState(Pages.OUTSTANDING_POSITION);

    const _renderContentBody = () => {
        switch(page){
            case Pages.OUTSTANDING_POSITION:
                return <OutstandingPosition/>
            case Pages.ACCOUNT_DETAIL:
                return <AccountDetail/>
            case Pages.SCRIPT_POSITION:
                return <ScriptPosition/>
            case Pages.MONTH_TRANSACTION:
                return <MonthTransaction/>
            case Pages.TRANSCTION_HISTORY:
                return <TransactionHistory/>
            case Pages.SETTLED_POSTION:
                return <SettledPosition/>
            default:
                return <OutstandingPosition/>
        }
    }

    return(
        <div className="site">
            <Header page="account-management"/>

            <div className="site-main pt-1 pb-3">
                <div className="container">
                    <div className="row g-0 align-items-stretch">
                        <div className="col-md-3 col-lg-2">
                            <div className="card mb-3">
                                <div className="card-header py-2 text-center">
                                    <h5 className="card-title mb-0 text-uppercase">Category</h5>
                                </div>
                                <div className="card-body p-0">
                                    <ul className="nav flex-column">
                                        <li className="nav-item">
                                            <a href="#" className="nav-link" onClick={() => setPage(Pages.OUTSTANDING_POSITION)}>Outstanding Position</a>
                                        </li>
                                        <li className="nav-item">
                                            <a href="#" className="nav-link" onClick={() => setPage(Pages.ACCOUNT_DETAIL)}>Account Details</a>
                                        </li>
                                        <li className="nav-item">
                                            <a href="#" className="nav-link" onClick={() => setPage(Pages.SCRIPT_POSITION)}>Script Positions</a>
                                        </li>
                                        <li className="nav-item">
                                            <a href="#" className="nav-link" onClick={() => setPage(Pages.MONTH_TRANSACTION)}>This Month Transactions</a>
                                        </li>
                                        <li className="nav-item">
                                            <a href="#" className="nav-link" onClick={() => setPage(Pages.TRANSCTION_HISTORY)}>Transactions History</a>
                                        </li>
                                        <li className="nav-item">
                                            <a href="#" className="nav-link" onClick={() => setPage(Pages.SETTLED_POSTION)}>Settled Positions</a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div className="col-md-9 col-lg-10">
                            {_renderContentBody()}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default AccountManagement;