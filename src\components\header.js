import axios from 'axios';
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { API_GET_TOTAL_UNREAD } from '../constants/api.constant';
import { LocalStorageKey } from '../model/constant';
import { convertNumber, defindConfigPost } from '../model/utils';
import { useSelector } from 'react-redux';

const Header = (props) => {
    const { page } = props;
    const { t } = useTranslation();
    const [numberNewsUnread, setNumberNewsUnread] = useState(0);

    const isUpdateNews = useSelector(state => state.news.isUpdateNews);

    const apiUrl = window.apiUrl;
    const urlGetTotalUnread = `${apiUrl}${API_GET_TOTAL_UNREAD}`;

    useEffect(() => {
        countNumberNewsUnread();
    }, [isUpdateNews])

    const getAccountnumber = () => {
        const poemId = sessionStorage.getItem(LocalStorageKey.POEM_ID) || '********';
        const maskedPoemId = '****' + poemId.slice(4);
        return maskedPoemId;
    }

    const countNumberNewsUnread = () => {
        axios.get(urlGetTotalUnread, defindConfigPost()).then(resp => {
            if (resp && resp.data) {
                setNumberNewsUnread(convertNumber(resp.data.data?.num_unread_news));
            }
        })
    }

    return (
        <>
            <div className="site-header">
                <div className="container position-relative d-flex flex-column-reverse">
                    <div className="header-inner d-flex justify-content-between align-items-center align-items-md-end">
                        <button className="btn bg-transparent btn-offcanvas-toggle d-md-none" data-bs-toggle="offcanvas" href="#offcanvasNav" role="button" aria-controls=""><i className="bi bi-list"></i></button>
                        <div className="site-brand">
                        </div>
                        <ul className="nav nav-tabs header-nav d-none d-md-flex" >
                            <li className="nav-item item-dashboard">
                                <Link to="/dashboard" className={page === "dashboard" ? "nav-link active" : "nav-link"}>{t('header.dashbroad')}</Link>
                            </li>
                            <li className="nav-item item-order-status dropdown">
                                <Link to="/order-status" className={page === "order-status" ? "nav-link active" : "nav-link"}>{t('header.orderStatus')}</Link>
                            </li>
                            <li className="nav-item item-announcement">
                                <Link to="/announcement" className={page === "announcement" ? "nav-link active" : "nav-link"}>{t('header.announcement')}
                                    {numberNewsUnread > 0 && <sub className='count'>{numberNewsUnread}</sub>}
                                </Link>
                            </li>
                        </ul>
                        <ul className="nav topbar-nav">
                            <li className="nav-item text-end text-md-left">
                                <Link to=" " className="nav-link d-flex theme-text">
                                    <span className="d-block d-md-inline-block">{t('header.accountNo')}</span> &nbsp; <span className="d-block d-md-inline-block">{getAccountnumber()}</span>
                                </Link>
                            </li>
                        </ul>
                    </div>

                </div>
            </div>

            <div className="offcanvas offcanvas-start" tabIndex="-1" id="offcanvasNav" aria-labelledby="offcanvasMenuLabel">
                <div className="offcanvas-header">
                    <h5 className="offcanvas-title" id="offcanvasMenuLabel">Menu</h5>
                    <button type="button" className="btn-close text-reset btn-close-white" data-bs-dismiss="offcanvas" aria-label="Close"></button>
                </div>
                <div>
                    <ul className="nav flex-column">
                        <li className="nav-item item-dashboard"><Link to="/dashboard" className={page === "dashboard" ? "nav-link active_mobile" : "nav-link"}>{t('header.dashbroad')}</Link></li>
                        <li className="nav-item item-order-staus"><Link to="/order-status" className={page === "order-status" ? "nav-link active_mobile" : "nav-link"}>{t('header.orderStatus')}</Link></li>
                        <li className="nav-item item-announcement menu-accounce-mobile" >
                            <div className={`w-100 link_announcement ${page === "announcement" ? "active_mobile" : ""}`}>
                                <Link to="/announcement" className={page === "announcement" ? "nav-link active_mobile" : "nav-link"}>{t('header.announcement')}</Link>
                                {numberNewsUnread > 0 && <sub className='count count-mobile'>{numberNewsUnread}</sub>}
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </>

    )
}

export default Header;