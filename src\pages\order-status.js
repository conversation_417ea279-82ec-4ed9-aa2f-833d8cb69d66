import React, { useState, useEffect, useCallback } from "react";
import Header from "../components/header";
import Pagination from "react-js-pagination";
import { Modal } from 'react-bootstrap';
import {
    convertNumber,
    getClassState, formatCurrency, formatCurrencyDecimal, checkMessageError, defindConfigPost, checkVolumeLotSize
} from "../model/utils";
import * as tmpb from "../model/proto/trading_model_pb";
import * as tspb from "../model/proto/trading_service_pb";
import { wsService } from '../services/websocket-service';
import { SymbolListRequest } from '../model/proto/query_service_pb';
import { OrderState, OrderType } from '../model/proto/trading_model_pb';
import { ProductType } from '../model/proto/query_model_pb';
import {
    LocalStorageKey, SocketKey, TimeFrameOrderStatus,
    FORMAT_DATE_TIME_YEAR, FORMAT_DATE_TIME, FORMAT_DATE_TIME_SECOND, PASSWORD_REQ_SUBMIT, MAX_LENGTH_VOLUME, HANDLE_MODIFY_REQUEST,
    DEFAULT_PAGE_INDEX,
    DEFAULT_PAGE_SIZE,
    TOAST_MESSAGE_SELECT_AMEND
} from "../model/constant";
import rpc from '../model/proto/rpc_pb';
import moment from "moment";
import { useTranslation } from 'react-i18next';
import { LIST_ORDERS_VIA, SCREEN_SIZE_WEB_MIN } from "../constants/general";
import { MESSAGE_ERROR, DIGIT_DEFAULT, PRODUCT_TYPE, TOAST_MESSAGE_CANCEL_REQUEST } from "../model/constant";
import * as systemModel from '../model/proto/system_model_pb';
import NumberFormat from "react-number-format";
import { toast, ToastContainer } from "react-toastify";
import PopupTimeout from "../components/popup-timeout";
import { API_E2EE_PRESESSION, API_ORDER_HISTORY } from "../constants/api.constant";
import axios from "axios";
import $ from 'jquery';
import Decimal from "decimal.js";
import LazyLoad from "../components/lazy-load";
import { Autocomplete, Paper, TextField } from "@mui/material";
import { success } from "../constants/announcement";

const _fn = require("../assets/scripts/E2EE/fn");
const _rsa = require("../assets/scripts/E2EE/rsa_jso");

const urlGetE2EEPresession = `${window.apiUrl}${API_E2EE_PRESESSION}`;
const urlGetOrderHistory = `${window.apiUrl}${API_ORDER_HISTORY}`;
const flagMsgCode = window.flagMsgCode;

const OrderStatus = (props) => {
    const { i18n } = useTranslation();
    const tradingModelPb = tmpb;
    const tradingServicePb = tspb;
    const today = moment().format(FORMAT_DATE_TIME_YEAR);
    const { t } = useTranslation();
    const [paging, setPaging] = useState({
        pageIndex: DEFAULT_PAGE_INDEX,
        pageSize: DEFAULT_PAGE_SIZE,
        totalRecord: 0
    });

    const [password, setPassword] = useState('');
    const [order, setOrder] = useState(null);
    const [showDetailModal, setShowDetailModal] = useState(false);
    const [showMultiOrderCancelModel, setShowMultiOrderCancelModel] = useState(false);
    const [showAmendModal, setShowAmendModal] = useState(false);
    const [showWithDrawModal, setShowWithDrawModal] = useState(false);
    const [showAmendResultModal, setShowAmendResultModal] = useState(false);
    const [showWithdrawResultModal, setShowWithdrawResultModal] = useState(false);
    const [orderTime, setOrderTime] = useState(TimeFrameOrderStatus.today);
    const [isPassTime, setIsPassTime] = useState(false);
    const [timeFrameOrder, setTimeFrameOrder] = useState(today);
    const [symbol, setSymbol] = useState('');
    const [orderStatus, setOrderStatus] = useState(OrderState.ORDER_STATE_NONE);
    const [listOrders, setListOrders] = useState([]);
    const [sizeScreen, setSizeScreen] = useState(window.innerWidth);
    const [orderType, setOrderType] = useState(OrderType.OP_NONE);
    const [orderVia, setOrderVia] = useState('');
    const [isAllowed, setIsAllow] = useState(false);

    const [isExceedQty, setIsExceedQty] = useState(false);

    const pwdReqAmend = sessionStorage.getItem(LocalStorageKey.PWD_REQ_AMEND) || PASSWORD_REQ_SUBMIT.OFF;
    const pwdReqWithdraw = sessionStorage.getItem(LocalStorageKey.PWD_REQ_WITHDRAW) || PASSWORD_REQ_SUBMIT.OFF;

    const [remainQty, setRemainQty] = useState(0);
    const [remainQtyDisplay, setRemainQtyDisplay] = useState(0);
    const [showSuccessModal, setShowSuccessModal] = useState(false);
    const [isShowError, setIsShowError] = useState(false);
    const [titleError, setTitleError] = useState('');
    const [selectedRecord, setSelectedRecord] = useState([]);
    const [listTickerSearch, setListTickerSearch] = useState([]);
    const [orderEventList, setOrderEventList] = useState([]);
    const [symbolMap, setSymbolMap] = useState(new Map());
    const [pinR, setPinR] = useState('');
    const [pinROnly8, setPinROnly8] = useState('');
    const [preSessionInfo, setPreSessionInfo] = useState();
    const [isShowPassword, setIsShowPassword] = useState(false);

    const [showMessageCancelSuccess, setShowMessageCancelSuccess] = useState(false);
    const [isInvalidMinValue, setIsInvalidMinValue] = useState(false);
    const [isInvalidQty, setIsInvalidQty] = useState(false);
    const [isShowLazyLoad, setIsShowLazyLoad] = useState(false);
    const [isRefresh, setIsRefresh] = useState(false);
    const [symbolCodeWithCompanyName, setSymbolCodeWithCompanyName] = useState('');
    let rsa = null;

    const accountId = sessionStorage.getItem(LocalStorageKey.ACCOUNT_ID);
    const minOrderValue = localStorage.getItem(LocalStorageKey.MIN_ORDER_VALUE) || '';
    const symbolList = JSON.parse(localStorage.getItem(LocalStorageKey.SYMBOL_LIST) || '[]');

    useEffect(() => {
        callSymbolListRequest();
        window.addEventListener('resize', () => setSizeScreen(window.innerWidth));
        return () => {
            window.removeEventListener('resize', () => setSizeScreen(window.innerWidth));
        };
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])

    useEffect(() => {
        const wsConnect = wsService.getSocketSubject().subscribe(resp => {
            if (resp === SocketKey.SOCKET_CONNECTED) {
                callSymbolListRequest();
            }
        });

        const orderEvent = wsService.getOrderEventResponse().subscribe(res => {
            if (res && res?.orderList) {
                setOrderEventList(res?.orderList);
            }
        });

        const modifyRes = wsService.getModifySubject().subscribe(resp => {
            localStorage.setItem(LocalStorageKey.LAZY_LOAD_STATUS, false)
            const respCode = resp?.msgCode;
            const respText = resp?.msgText;
            setShowAmendModal(false);
            setPassword("");
            if (respCode === systemModel.MsgCode.MT_RET_OK) {
                toast.success("Amendment request has been sent")
            } else if (respCode === systemModel.MsgCode.MT_RET_FORWARD_EXT_SYSTEM) {
                toast.success(HANDLE_MODIFY_REQUEST);
            } else {
                const messageDisplay = checkMessageError(respText, respCode);
                setTitleError(messageDisplay ? messageDisplay : i18n.t('message.orderFail'));
                setIsShowError(true);
            }
            setSelectedRecord([]);
        })

        const symbolListResponse = wsService.getSymbolListResponse().subscribe(symbols => {
            setIsShowLazyLoad(false)
            if (symbols && symbols.symbolList) {
                const _symbolMap = new Map();
                symbols.symbolList.forEach(symbol => {
                    _symbolMap.set(symbol.symbolCode, symbol);
                });
                setSymbolMap(_symbolMap);

                // get list ticker search
                const listOptions = symbols.symbolList
                  .sort((a, b) => a?.symbolCode.localeCompare(b?.symbolCode))
                  .map((item) => {
                    return `${item?.symbolCode} - ${item?.symbolName}`;
                  });
                setListTickerSearch(listOptions);
            }
        });

        return () => {
            wsConnect.unsubscribe();
            modifyRes.unsubscribe();
            orderEvent.unsubscribe();
            symbolListResponse.unsubscribe();
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])

    // NOTE: setTimeout if don't have symbolList response, lazyLoad auto hidden after config time
    setTimeout(() => {
        if (isShowLazyLoad) {
            setIsShowLazyLoad(false);
        }
    }, [convertNumber(window.wsResponseTimeout)])

    useEffect(() => {
        if (orderEventList && orderEventList?.length > 0 && isOrderExistInList(orderEventList[0])) {
            processOrderEvent(orderEventList);
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [orderEventList])

    useEffect(() => {
        const cancelRes = wsService.getCancelSubject().subscribe(resp => {
            console.log(`Received cancel order response at: ${moment().format('YYYY-MM-DD HH:mm:ss')}.${moment().millisecond()}`)
            const respCode = resp?.msgCode;
            const respText = resp?.msgText;
            setShowWithDrawModal(false);
            setPassword("");
            if (resp?.orderList?.length > 1) {
                if (respCode === systemModel.MsgCode.MT_RET_OK) {
                    if (showMessageCancelSuccess) {
                        toast.success("Withdrawal request has been sent");
                        setShowMessageCancelSuccess(false);
                    }
                } else if (respCode === systemModel.MsgCode.MT_RET_FORWARD_EXT_SYSTEM) {
                    if (showMessageCancelSuccess) {
                        toast.success(TOAST_MESSAGE_CANCEL_REQUEST);
                        setShowMessageCancelSuccess(false);
                    }
                    setSelectedRecord([]);
                    console.log(`Process finished cancel order response at: ${moment().format('YYYY-MM-DD HH:mm:ss')}.${moment().millisecond()}`)
                    return;
                } else {
                    const messageDisplay = checkMessageError(respText, respCode);
                    setTitleError(messageDisplay ? messageDisplay : i18n.t('message.orderFail'));
                    setIsShowError(true);
                }
            } else if (resp?.orderList?.length === 1) {
                const order = resp?.orderList[0];
                if (order?.msgCode === systemModel.MsgCode.MT_RET_OK) {
                    if (showMessageCancelSuccess) {
                        toast.success("Withdrawal request has been sent");
                        setShowMessageCancelSuccess(false);
                    }
                } else if (order?.msgCode === systemModel.MsgCode.MT_RET_FORWARD_EXT_SYSTEM) {
                    if (showMessageCancelSuccess) {
                        toast.success(TOAST_MESSAGE_CANCEL_REQUEST);
                        setShowMessageCancelSuccess(false);
                    }
                    setSelectedRecord([]);
                    console.log(`Process finished cancel order response at: ${moment().format('YYYY-MM-DD HH:mm:ss')}.${moment().millisecond()}`)
                    return;
                } else {
                    const messageDisplay = checkMessageError(order?.note, order?.msgCode);
                    setTitleError(messageDisplay ? messageDisplay : i18n.t('message.orderFail'));
                    setIsShowError(true);
                }
            } else {
                const messageDisplay = checkMessageError(respText, respCode);
                setTitleError(messageDisplay ? messageDisplay : i18n.t('message.orderFail'));
                setIsShowError(true);
            }
            setSelectedRecord([]);
            console.log(`Process finished cancel order response at: ${moment().format('YYYY-MM-DD HH:mm:ss')}.${moment().millisecond()}`)
        })
        return () => cancelRes.unsubscribe();
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [showMessageCancelSuccess])

    useEffect(() => {
        const remain = convertNumber(order?.amount) - convertNumber(order?.filledAmount);
        setRemainQty(remain);
    }, [order])

    useEffect(() => {
        getOrderHistory();
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [symbol,
        timeFrameOrder,
        orderStatus,
        orderType,
        paging.pageIndex,
        paging.pageSize,
        orderVia,
        isRefresh])

    const getOrderHistory = () => {
        setIsShowLazyLoad(true);
        const param = {
            symbolCode: symbol,
            orderStatus: orderStatus !== tradingModelPb.OrderState.ORDER_STATE_NONE ? orderStatus.toString() : '',
            orderType: orderType !== tradingModelPb.OrderType.OP_NONE ? orderType.toString() : '',
            page: paging.pageIndex,
            pageSize: paging.pageSize,
            from: timeFrameOrder === today ? moment.utc().startOf('day').valueOf() : moment.utc(timeFrameOrder).startOf('day').valueOf(),
            to: timeFrameOrder === today ? moment.utc().endOf('day').valueOf() : moment.utc(timeFrameOrder).endOf('day').valueOf(),
            orderVia: orderVia
        }
        axios.post(urlGetOrderHistory, param, defindConfigPost()).then(resp => {
            const data = resp?.data?.data?.results;
            setPaging({
                ...paging,
                totalRecord: resp?.data?.data?.count
            })
            const dataConverted = data?.map(item => ({
                ...item,
                side: convertNumber(item.orderSide),
                state: convertNumber(item.orderStatus),
                orderType: convertNumber(item.orderType),
                amount: item.volume,
                lastPrice: item.execPrice,
                averagePrice: item.averagePrice,
                filledAmount: item.execVolume,
                time: item.orderTime,
                executedDatetime: item.execTime,
                msgCode : convertNumber(item.msgCode)
            }))
            if (dataConverted) {
                setListOrders(dataConverted);
            } else {
                setListOrders([]);
            }
        }).catch(err => {
            console.error("FAILED to call API OrderHistory", err);
        }).finally(() => {
            setIsShowLazyLoad(false);
        })
    }

    const callSymbolListRequest = () => {
        setIsShowLazyLoad(true);
        const accountId = sessionStorage.getItem(LocalStorageKey.ACCOUNT_ID);
        const wsConnect = wsService.getWsConnected();
        if (wsConnect) {
            const symbolListRequest = new SymbolListRequest();
            symbolListRequest.setAccountId(accountId);
            const rpcMsg = new rpc.RpcMessage();
            rpcMsg.setPayloadClass(rpc.RpcMessage.Payload.SYMBOL_LIST_REQ);
            rpcMsg.setPayloadData(symbolListRequest.serializeBinary());
            rpcMsg.setContextId(moment().valueOf());
            wsService.sendMessage(rpcMsg.serializeBinary());
        }
    }

    const isOrderExistInList = (order) => {
        //check every received orderEvent if orderEvent is in the OrderList => update real-time for order
        const index = listOrders.findIndex(o => o?.externalOrderId === order?.externalOrderId);
        return index > -1 ? true : false;
    }

    const processOrderEvent = (orderList) => {
       orderList.forEach(order => {
        switch (order?.state) {
            case tradingModelPb.OrderState.ORDER_STATE_PLACED: {
                processPlaceOrder(order);
                break;
            }
            case tradingModelPb.OrderState.ORDER_STATE_CANCELED: {
                processCancelOrder(order);
                break;
            }
            case tradingModelPb.OrderState.ORDER_STATE_PARTIAL: {
                processPartialAndFilledOrder(order);
                break;
            }
            case tradingModelPb.OrderState.ORDER_STATE_FILLED: {
                processPartialAndFilledOrder(order);
                break;
            }
            case tradingModelPb.OrderState.ORDER_STATE_REJECTED: {
                processRejectedOrder(order);
                break;
            }
            case tradingModelPb.OrderState.ORDER_STATE_MODIFIED: {
                processModifyOrder(order);
                break;
            }
            case tradingModelPb.OrderState.ORDER_STATE_MATCHED: {
                processPartialAndFilledOrder(order);
                break;
            }
            default: {
                break;
            }
        }
       })
    }

    const processPlaceOrder = (order) => {
        const tempList = [...listOrders];
        const idx = tempList.findIndex(o => o?.externalOrderId === order?.externalOrderId);
        if (idx < 0) {
            tempList.unshift(order);
            setListOrders(tempList);
            return;
        }
        tempList[idx] = {
            ...tempList[idx],
            state: order?.state,
            time: order?.time,
            executedDatetime: order?.executedDatetime,
            price: order?.price
        }
        setListOrders(tempList);
    }

    const processCancelOrder = (order) => {
        const tempList = [...listOrders];
        const idx = tempList.findIndex(o => o?.externalOrderId === order?.externalOrderId && o?.state !== tradingModelPb.OrderState.ORDER_STATE_MODIFIED);
        if (idx < 0) {
            return;
        }
        tempList[idx] = {
            ...tempList[idx],
            state: order?.state,
            executedDatetime: order?.executedDatetime,
        }
        setListOrders(tempList);
    }

    const processPartialAndFilledOrder = (order) => {
        const tempList = [...listOrders];
        const idx = tempList.findIndex(o => o?.externalOrderId === order?.externalOrderId && o?.state !== tradingModelPb.OrderState.ORDER_STATE_MODIFIED);
        if (idx < 0) {
            return;
        }
        const orderState = order?.state === tradingModelPb.OrderState.ORDER_STATE_PARTIAL ? tradingModelPb.OrderState.ORDER_STATE_MATCHED : order?.state;
        tempList[idx] = {
            ...tempList[idx],
            state: orderState,
            executedDatetime: order?.executedDatetime,
            execPrice: order?.lastPrice,
            lastPrice: order?.lastPrice,
            averagePrice: formatCurrencyDecimal(order?.averagePrice, 5),
            filledAmount: order?.totalFilledAmount
        }
        setListOrders(tempList);
    }

    const processRejectedOrder = (order) => {
        const tempList = [...listOrders];

        // partial filled and reject remain
        const idx = tempList.findIndex(o => o?.externalOrderId === order?.externalOrderId && o?.state === tradingModelPb.OrderState.ORDER_STATE_MATCHED);
        if (idx >= 0) {
            tempList[idx] = {
                ...tempList[idx],
                state: tradingModelPb.OrderState.ORDER_STATE_PARTIAL,
            };
            const amountReject = convertNumber(order?.amount) - convertNumber(order?.totalFilledAmount);
            tempList.push({
                ...order,
                time: tempList[idx]?.time,
                lastPrice: '0',
                averagePrice: '0',
                filledAmount: amountReject.toString()
            });
            setListOrders(tempList);
            return;
        }

        // reject when session close
        const idxReject = tempList.findIndex(o => o?.externalOrderId === order?.externalOrderId && (o?.state === tradingModelPb.OrderState.ORDER_STATE_PARTIAL || o?.state === tradingModelPb.OrderState.ORDER_STATE_PLACED));
        if (idxReject >= 0) {
            tempList[idxReject] = {
                ...tempList[idxReject],
                lastPrice: '0',
                executedDatetime: order?.executedDatetime,
                state: order?.state,
                msgCode: order?.msgCode,
                comment: order?.comment
            };
            setListOrders(tempList);
            return;
        }

        tempList.push({
            ...order,
            lastPrice: '0',
            time: order?.executedDatetime
        });
        setListOrders(tempList);
    }

    const processModifyOrder = (order) => {
        const tempList = [...listOrders];
        const idx = tempList.findIndex(o => o?.externalOrderId === order?.externalOrderId && o?.state !== tradingModelPb.OrderState.ORDER_STATE_MODIFIED);
        if (idx < 0) {
            return;
        }
        tempList[idx] = {
            ...tempList[idx],
            state: order?.state,
            executedDatetime: order?.executedDatetime,
            withdrawAmount: order?.filledAmount
        }

        // add record place after modify success
        if (order?.msgCode === systemModel.MsgCode.MT_RET_OK) {
            tempList.unshift({
                ...order,
                amount: order?.filledAmount,
                filledAmount: '0',
                lastPrice: '0',
                execPrice: '0',
                averagePrice: '0',
                state: tradingModelPb.OrderState.ORDER_STATE_PLACED,
                time: order?.executedDatetime
            })
        }
        setListOrders(tempList);
    }

    const getE2EEPresessionInfo = async () => {
        const param = {
            appType: 1
        };
        try {
            const resp = await axios.post(urlGetE2EEPresession, param, defindConfigPost());
            if (resp?.data?.meta?.code === success) {
                const dataResp = resp?.data?.data;
                if (dataResp) {
                    setPreSessionInfo(dataResp);
                    encryptPasswordConfirm(dataResp);
                }
            }
        } catch (error) {
            setShowMultiOrderCancelModel(false);
            setShowWithDrawModal(false);
            setShowAmendModal(false);
            setTitleError(i18n.t('message.handleFail'));
            setIsShowError(true);
            setPassword('');
        }
    }

    const encryptPasswordConfirm = (preSession) => {
        rsa = new _rsa.RSAEngine();
        $("#E2EE_PUBLIC_KEY").val(preSession.publicKey);
        $("#E2EE_SESSION_ID").val(preSession.sessionId);
        $("#E2EE_RANDOM_NUMBER").val(preSession.randomNumber);

        let _publicKey = $("#E2EE_PUBLIC_KEY").val();
        let _sessionID = $("#E2EE_SESSION_ID").val();
        let _randomNumber = $("#E2EE_RANDOM_NUMBER").val();
        rsa.init(_publicKey, _sessionID, _randomNumber);

        let _txtPassword = $('#txtPassword')[0];

        let pwdOnly8 = _txtPassword.value.trim().substring(0, 8);
        let pwd = _txtPassword.value.trim().substring(0, 50);

        // encrypt password PinR
        _txtPassword.value = pwd;
        const pinR = _fn.fnGetRPIN(_txtPassword, rsa);
        setPinR(pinR)
        
        // encrypt password PinR_only8
        _txtPassword.value = pwdOnly8;
        const pinROnly8 = _fn.fnGetRPIN(_txtPassword, rsa);
        setPinROnly8(pinROnly8);
        if (showAmendModal) {
            requestAmendOrder(preSession, pinR, pinROnly8);
        }

        if (showWithDrawModal) {
            requestWithDrawOrder(order, preSession, pinR, pinROnly8);
        }

        if (showMultiOrderCancelModel) {
            selectedRecord.forEach(order => {
                requestWithDrawOrder(order, preSession, pinR, pinROnly8);
            });
            setShowMultiOrderCancelModel(false);
            setSelectedRecord([]);
        }
    }

    const onPageChange = (event) => {
        setPaging({
            ...paging,
            pageIndex: event
        });
        setSelectedRecord([]);
    }

    const onChangePageSize = (event) => {
        let pageSize = parseInt(event.target.value);
        setPaging({
            ...paging,
            pageSize: pageSize,
            pageIndex: DEFAULT_PAGE_INDEX
        });
        setSelectedRecord([]);
    }

    const handleSelectSymbol = (event) => {
      const symbolCode = event.target.innerText?.split("-")[0]?.trim();

      if (symbolCode) {
        const tickerSearch = symbolList.find((item) => item.symbolCode === symbolCode.toUpperCase());
        if (tickerSearch) {
          setSymbolCodeWithCompanyName(event.target.innerText);
          setSymbol(tickerSearch.symbolCode);
        }
      } else {
        setSymbol("");
      }
    };

    const _renderSuccessModal = () => {
        return <Modal show={showSuccessModal} size="sm" centered="true">
            <div className="modal-header text-end pb-0">
                <button type="button" className="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close" onClick={() => setShowSuccessModal(false)}></button>
            </div>
            <div className="modal-body">
                <div className="mb-3 text-center">
                    <p><img src={`${process.env.PUBLIC_URL}/img/icon-confirm-success.svg`} alt="Confirm Success" /></p>
                    <p className="text-uppercase mb-0">{t('order.placed')}</p>
                    <h4 className="text-success text-uppercase mb-0"><strong>{t('order.successfully')}</strong></h4>
                </div>
                <p className="text-center opacity-75">{t('order.visitPage')}</p>
                {orderType === OrderType.OP_LIMIT && <p className='text-center opacity-75'>{t('order.noteLimitOrder')}</p>}
            </div>
        </Modal>
    }

    const handleChangeTime = (event) => {
        const time = event.target.value;
        setPaging({
            ...paging,
            pageIndex: DEFAULT_PAGE_INDEX
        })
        setOrderTime(time);
        setIsPassTime(time === TimeFrameOrderStatus.pass);
        if (time === TimeFrameOrderStatus.pass) {
            setTimeFrameOrder(renderListPassDays()[0]?.value);
        } else {
            setTimeFrameOrder(today);
        }
    }

    const renderListPassDays = () => {
        const listDateTime = [];
        let currentDate = moment.utc(); // Start with current UTC date
        // TODO: filter date enough 7 days in the past don't have Saturday and Sunday
        while (listDateTime.length < 7) {
            currentDate = currentDate.subtract(1, 'day');
            const isSaturday = currentDate.day() === 6;
            const isSunday = currentDate.day() === 0;
            const element = {
                value: currentDate.format(FORMAT_DATE_TIME_YEAR),
                title: currentDate.format(FORMAT_DATE_TIME)
            }
            if (!isSaturday && !isSunday) {
                listDateTime.push(element);
            }
        }
        return listDateTime;
    }

    const renderTimeFramePassOrder = () => (
        renderListPassDays().map((item, index) => (
            <option key={index} value={item.value}>{item.title}</option>
        ))
    )

    const handleChangePassDay = (event) => {
        setPaging({ ...paging, pageIndex: DEFAULT_PAGE_INDEX });
        setTimeFrameOrder(event.target.value);
    }

    const handleChangeOrderStatus = (event) => {
        setPaging({
            ...paging,
            pageIndex: DEFAULT_PAGE_INDEX
        })
        setOrderStatus(event.target.value);
    }

    const handleChangeOrderType = (event) => {
        setPaging({
            ...paging,
            pageIndex: DEFAULT_PAGE_INDEX
        });
        setOrderType(convertNumber(event.target.value));
    }

    const handleChangeOrderVia = (event) => {
        setPaging({
            ...paging,
            pageIndex: DEFAULT_PAGE_INDEX
        });
        setOrderVia(event.target.value);
    }

    const enableCheckBox = (state, msgCode) => {
        return state === OrderState.ORDER_STATE_PLACED || (state === OrderState.ORDER_STATE_MATCHED && msgCode === systemModel.MsgCode.MT_RET_OK);
    }

    const handleAmend = () => {
        if (selectedRecord.length > 1) {
            setTitleError(TOAST_MESSAGE_SELECT_AMEND);
            setIsShowError(true);
        } else {
            const amount = convertNumber(selectedRecord[0]?.amount);
            const filledAmount = convertNumber(selectedRecord[0]?.filledAmount);
            setShowAmendModal(true);
            setOrder(selectedRecord[0]);
            setRemainQty(amount - filledAmount);
            setRemainQtyDisplay(amount - filledAmount);
        }
    }

    const _renderSearchBoxMobile = () => (
        <div className="card-body bg-dark-light pb-0 form-line">
            <div className="row g-3 justify-content-between align-items-end mb-20">
                <div className="col-6 selectdiv">
                    <label htmlFor="currentDate" className="opacity-50 mb-1">Order History</label>
                    <select className="form-select" id="currentDate" value={orderTime} onChange={handleChangeTime}>
                        <option value={TimeFrameOrderStatus.today}>{`Today's Orders`}</option>
                        <option value={TimeFrameOrderStatus.pass}>Past Orders</option>
                    </select>
                </div>
                {
                    isPassTime && <div className="col-6 selectdiv">
                        <label className="opacity-50 mb-1"></label>
                        <select className="form-select" value={timeFrameOrder} onChange={handleChangePassDay} >
                            {renderTimeFramePassOrder()}
                        </select>
                    </div>
                }
                <div className="col-6 selectdiv">
                    <label htmlFor="orderStatus" className="opacity-50 mb-1">Order Status</label>
                    <select className="form-select" id="orderStatus" value={orderStatus} onChange={handleChangeOrderStatus}>
                        <option value={OrderState.ORDER_STATE_NONE}>All Orders</option>
                        <option value={OrderState.ORDER_STATE_PLACED}>Received</option>
                        <option value={OrderState.ORDER_STATE_FILLED}>Done</option>
                        <option value={OrderState.ORDER_STATE_REJECTED}>Rejected</option>
                        <option value={OrderState.ORDER_STATE_PARTIAL}>Part Done</option>
                        <option value={OrderState.ORDER_STATE_CANCELED}>Withdraw</option>
                        <option value={OrderState.ORDER_STATE_MODIFIED}>Amend</option>
                    </select>
                </div>
                <div className="col-6 selectdiv">
                    <label htmlFor="orderType" className="opacity-50 mb-1">Order Type</label>
                    <select className="form-select" id="orderType" value={orderType} onChange={handleChangeOrderType}>
                        <option value={OrderType.OP_NONE}>All Orders</option>
                        <option value={OrderType.OP_LIMIT}>Limit</option>
                        <option value={OrderType.OP_MARKET}>Market</option>
                    </select>
                </div>
                <div className="col-6">
                    <label className="opacity-50 mb-1">Symbol</label>
                    <>  
                        <Autocomplete
                            className="w-100 ticker-search"
                            onChange={handleSelectSymbol}
                            onKeyDown={handleSelectSymbol}
                            options={listTickerSearch}
                            value={symbol !== '' ? symbolCodeWithCompanyName : ''}
                            PaperComponent={({ children }) => (
                                <Paper className="paper">{children}</Paper>
                            )}
                            renderInput={(params) => <TextField {...params} placeholder="Search" onKeyDown={(e) => {
                                e.stopPropagation();
                            }} />}
                            disablePortal
                        />
                    </>
                </div>
                <div className="col-6 selectdiv">
                    <label htmlFor="orderVia" className="opacity-50 mb-1">Order Via</label>
                    <select className="form-select" id="orderVia" value={orderVia} onChange={handleChangeOrderVia}>
                        {LIST_ORDERS_VIA.map((item) => {
                                return <option value={item.value}>{item.label}</option>
                        })}
                    </select>
                </div>
                {selectedRecord.length > 0 &&
                    <>
                        <div className="col-6"><button className="button-amend" onClick={handleAmend}>AMEND</button></div>
                        <div className="col-6"><button className="button-withdraw" onClick={() => {
                            setShowMultiOrderCancelModel(true)
                        }}>WITHDRAW</button></div>
                    </>
                }
            </div>
        </div>
    )

    const _renderSearchBox = () => {
        return <div className="card-body bg-dark-light pb-0 form-line">
            <div className="row g-2 justify-content-between align-items-end">
                <div className="col-2">
                    <div className="mb-3 selectdiv">
                        <label htmlFor="currentDate" className="opacity-50 mb-1">Order History</label>
                        <select className="form-select" id="currentDate" value={orderTime} onChange={handleChangeTime}>
                            <option value={TimeFrameOrderStatus.today}>{`Today's Orders`}</option>
                            <option value={TimeFrameOrderStatus.pass}>Past Orders</option>
                        </select>
                    </div>
                </div>
                {isPassTime && <div className="col-2">
                    <div className="mb-3 selectdiv">
                        <label className="opacity-50 mb-1"></label>
                        <select className="form-select" value={timeFrameOrder} onChange={handleChangePassDay} >
                            {renderTimeFramePassOrder()}
                        </select>
                    </div>
                </div>}
                <div className='col-2'>
                    <div className="mb-3 selectdiv">
                        <label htmlFor="orderStatus" className="opacity-50 mb-1">Order Status</label>
                        <select className="form-select" id="orderStatus" value={orderStatus} onChange={handleChangeOrderStatus}>
                            <option value={OrderState.ORDER_STATE_NONE}>All Orders</option>
                            <option value={OrderState.ORDER_STATE_PLACED}>Received</option>
                            <option value={OrderState.ORDER_STATE_FILLED}>Done</option>
                            <option value={OrderState.ORDER_STATE_REJECTED}>Rejected</option>
                            <option value={OrderState.ORDER_STATE_PARTIAL}>Part Done</option>
                            <option value={OrderState.ORDER_STATE_CANCELED}>Withdraw</option>
                            <option value={OrderState.ORDER_STATE_MODIFIED}>Amend</option>
                        </select>
                    </div>
                </div>
                <div className='col-2'>
                    <div className="mb-3 selectdiv">
                        <label htmlFor="orderType" className="opacity-50 mb-1">Order Type</label>
                        <select className="form-select" id="orderType" value={orderType} onChange={handleChangeOrderType}>
                            <option value={OrderType.OP_NONE}>All Orders</option>
                            <option value={OrderType.OP_LIMIT}>Limit</option>
                            <option value={OrderType.OP_MARKET}>Market</option>
                        </select>
                    </div>
                </div>
                <div className={`${isPassTime ? 'col-2' : 'col-3'}`}>
                    <div className="mb-3">
                        <label className="opacity-50 mb-1">Symbol</label>
                        <div className="position-relative input-search-order">
                            <>
                                <Autocomplete
                                    className="w-100 ticker-search"
                                    onChange={handleSelectSymbol}
                                    onKeyDown={handleSelectSymbol}
                                    disablePortal
                                    options={listTickerSearch}
                                    value={symbol !== '' ? symbolCodeWithCompanyName : ''}
                                    PaperComponent={({ children }) => (
                                        <Paper className="paper">{children}</Paper>
                                    )}
                                    renderInput={(params) => <TextField {...params} placeholder="Search" onKeyDown={(e) => {
                                        e.stopPropagation();
                                    }} />}
                                    autoFocus={false}
                                    autoHighlight={false}
                                    autoComplete={false}
                                />
                            </>
                        </div>
                    </div>
                </div>
                <div className='col-2'>
                    <div className="mb-3 selectdiv">
                        <label htmlFor="orderVia" className="opacity-50 mb-1">Order Via</label>
                        <select className="form-select" id="orderVia" value={orderVia} onChange={handleChangeOrderVia}>
                            {LIST_ORDERS_VIA.map((item) => {
                                return <option value={item.value}>{item.label}</option>
                            })}
                        </select>
                    </div>
                </div>
                {selectedRecord.length > 0 ?
                    <div className={isPassTime ? "col-12" : "col-2 row"}>
                        <div className="d-flex">
                            <button className="button-amend box-amend me-1" onClick={handleAmend}>AMEND</button>
                            <button className="button-withdraw box-withdraw" onClick={() => {setShowMultiOrderCancelModel(true)}}>WITHDRAW</button>
                        </div>
                    </div> : 
                    <div className=""></div>
                }
            </div>
        </div>
    }

    const _renderResultBox = () => {
        return <div className="card-body px-lg-5">
            <div className="table-responsive cus-border mb-3">
                <table className="table table-sm table-striped table-borderless mb-0">
                    {_renderTableHeader()}
                    <tbody>
                        {_renderTableBody()}
                    </tbody>
                </table>
            </div>
            {_renderPaging()}
            <div className="mt-0 opacity-75">Note: Please refresh the [US Asian Hours] Order Status screen to view the latest order status.</div>
        </div>
    }

    const _renderPaging = () => {
        return <div className="d-flex justify-content-between align-items-center">
            <div className="d-flex align-items-center mt-15">
                <span className="me-2">Show</span>
                <select className="form-select me-2" value={paging.pageSize} onChange={onChangePageSize}>
                    <option value="10">10</option>
                    <option value="20">20</option>
                    <option value="50">50</option>
                </select>
                <span>entries</span>
            </div>
            <Pagination
                activePage={paging.pageIndex}
                itemsCountPerPage={paging.pageSize}
                totalItemsCount={paging.totalRecord}
                pageRangeDisplayed={sizeScreen < SCREEN_SIZE_WEB_MIN ? 3 : 5}
                hideFirstLastPages={sizeScreen < SCREEN_SIZE_WEB_MIN ? true : false}
                onChange={onPageChange}
                itemClass="page-item"
                linkClass="page-link"
            />
        </div>
    }

    const handleCheckAll = (event) => {
        const temp = [];
        if (event.target.checked) {
            listOrders.forEach(item => {
                if (item.state === OrderState.ORDER_STATE_PLACED || (item.state === OrderState.ORDER_STATE_MATCHED && item.msgCode === systemModel.MsgCode.MT_RET_OK)) {
                    temp.push(item);
                }
            })
        }
        setSelectedRecord(temp);
    }

    const checkedAll = () => {
        const selectedLength = selectedRecord.length;
        const temp = [];
        listOrders.forEach(item => {
            if (item.state === OrderState.ORDER_STATE_PLACED || (item.state === OrderState.ORDER_STATE_MATCHED && item.msgCode === systemModel.MsgCode.MT_RET_OK)) {
                temp.push(item);
            }
        })
        return selectedLength > 0 && temp.length === selectedLength;
    }

    const disableCheckAll = () => {
        const temp = [];
        listOrders.forEach(item => {
            if (item.state === OrderState.ORDER_STATE_PLACED || (item.state === OrderState.ORDER_STATE_MATCHED && item.msgCode === systemModel.MsgCode.MT_RET_OK)) {
                temp.push(item)
            }
        })
        return temp.length === 0;
    }

    const _renderTableHeader = () => {
        return <thead>
            <tr className="align-top lh-sm">
                <th><input className="form-check-input" onChange={handleCheckAll} checked={checkedAll()} disabled={disableCheckAll()} type="checkbox" /></th>
                <th>{t('order.product')}</th>
                <th>{t('order.orderNo')}</th>
                <th>
                    <div>{t('order.name')}</div>
                    <div>{t('order.symbol')}</div>
                </th>
                <th>{t('order.orderStatus')}</th>
                <th>{t('order.orderType')}</th>
                <th className="mw-px-100">{t('order.submitted')}</th>
                <th>{t('order.action')}</th>
                <th>{t('order.message')}</th>
                <th>{t('order.submittedDate')}</th>
                <th className="text-center">{t('order.dateTime')}</th>
                <th>{t('order.currency')}</th>
                <th className="mw-px-100">{t('order.avgExecutedPrice')}</th>
            </tr>
        </thead>
    }

    const getOrderStatus = (state) => {
        switch (state) {
            case OrderState.ORDER_STATE_PARTIAL: {
                return 'Part Done'
            }
            case OrderState.ORDER_STATE_MATCHED: {
                return 'Part Done'
            }
            case OrderState.ORDER_STATE_REJECTED: {
                return 'Rejected';
            }
            case OrderState.ORDER_STATE_PLACED: {
                return "Received";
            }
            case OrderState.ORDER_STATE_CANCELED: {
                return "Withdraw";
            }
            case OrderState.ORDER_STATE_MODIFIED: {
                return "Amend";
            }
            default: {
                return 'Done';
            }
        }
    }

    const renderOrderType = (orderType) => {
        switch (orderType) {
            case OrderType.OP_LIMIT: {
                return "Limit";
            }
            case OrderType.OP_MARKET: {
                return "Market";
            }
            default: {
                return "";
            }
        }
    }

    const getOrderSide = (side) => {
        if (side === tradingModelPb.Side.BUY)
            return <span className="text-success">Buy</span>;
        else return <span className="text-danger">Sell</span>
    }

    const getOrderSide1 = (side) => {
        if (side === tradingModelPb.Side.BUY)
            return <strong className="text-success">Buy</strong>;
        else return <strong className="text-danger">Sell</strong>
    }

    const getSymbolDetail = useCallback((symbolCode) => {
        const symbol = symbolMap?.get(symbolCode);

        if (symbol) {
            return symbol;
        }

        // Retrieve symbolList from localStorage if symbolMap is empty or doesn't include the symbol
        return symbolList?.find(symbol => symbol?.symbolCode === symbolCode);
    }, [symbolMap])

    const defineComment = (msgCode, comment) => {
        if (msgCode === systemModel.MsgCode.MT_RET_NOT_ENOUGH_MIN_ORDER_VALUE) {
            return `The order is less than USD ${formatCurrency(localStorage.getItem(LocalStorageKey.MIN_ORDER_VALUE))}`;
        }
        if (msgCode === systemModel.MsgCode.MT_RET_ERROR_FROM_BO) {
            return comment;
        }
        const messageDisplay = MESSAGE_ERROR.get(msgCode);
        return messageDisplay || '-';
    }

    const getProductType = (symbol) => {
        if(!symbol) return ProductType.PRODUCT_TYPE_NONE;
        const symbolDetail = symbolMap.get(symbol);

        return symbolDetail ? symbolDetail.productType : ProductType.PRODUCT_TYPE_EQ;
    }

    const renderCssClassProductType = (productType) => {
        switch (productType) {
            case ProductType.PRODUCT_TYPE_ETF:
                return 'bg-violet';
            default:
                return 'bg-info';
        }
    }

    const _renderTableBody = () => (
        listOrders.map((order, idx) => {
            return <tr className="align-middle lh-sm" key={idx}>
                <td><input type="checkbox" className="form-check-input" disabled={!enableCheckBox(order?.state, order?.msgCode)} checked={selectedRecord.indexOf(order) >= 0}
                    onChange={(event) => {
                        const temp = [...selectedRecord];
                        if (event.target.checked) {
                            if (temp.indexOf(order) < 0) {
                                temp.push(order);
                            }
                        } else {
                            const idx = temp.indexOf(order);
                            if (idx >= 0) {
                                temp.splice(idx, 1);
                            }
                        }
                        setSelectedRecord(temp);
                    }}
                /></td>
                <td><span className={`badge ${renderCssClassProductType(getProductType(order?.symbolCode))}`}>
                    {PRODUCT_TYPE[getProductType(order?.symbolCode)]}</span></td>
                <td><span onClick={() => {
                    setShowDetailModal(true);
                    const remain = convertNumber(order?.amount) - convertNumber(order?.filledAmount);
                    setRemainQtyDisplay(remain);
                    setOrder(order)
                }} className="link-btn">{order.externalOrderId}</span></td>
                <td>
                    <div>{getSymbolDetail(order.symbolCode)?.symbolName}</div>
                    <div>{order?.symbolCode}</div>
                </td>
                <td className={getClassState(order.state)} >{getOrderStatus(order.state)}</td>
                <td>{renderOrderType(order.orderType)}</td>
                <td>
                    <div className="d-flex justify-content-between align-items-center">
                        <span>Price</span>
                        <span>{formatCurrencyDecimal(order.price, DIGIT_DEFAULT)}</span>
                    </div>
                    <div className="d-flex justify-content-between align-items-center">
                        <span>Qty</span>
                        <span>{formatCurrencyDecimal(order.amount, 0)}</span>
                    </div>
                </td>
                <td className="text-center">{getOrderSide(order.side)}</td>
                <td>{(order.state !== OrderState.ORDER_STATE_MATCHED && order.state !== OrderState.ORDER_STATE_PARTIAL) ? defineComment(order.msgCode, order?.comment) : '-'}</td>
                <td>
                    <div className="text-nowrap">{moment(convertNumber(order.time)).format(FORMAT_DATE_TIME_SECOND)}</div>
                </td>
                <td className="text-center">
                    {order.state !== OrderState.ORDER_STATE_PLACED && <div className="text-nowrap">{moment(convertNumber(order.executedDatetime)).format(FORMAT_DATE_TIME_SECOND)}</div>}
                    {order.state === OrderState.ORDER_STATE_PLACED && <div className="text-nowrap">-</div>}
                </td>
                <td className="text-center">{order?.currencyCode ? order?.currencyCode : '-'}</td>
                <td>
                    <div className="d-flex justify-content-between align-items-center">
                        <span>Price</span>
                        <span>{(convertNumber(order.execPrice) > 0 && convertNumber(order?.filledAmount) > 0) ? formatCurrencyDecimal(order?.averagePrice, 5) : '-'}</span>
                    </div>
                    <div className="d-flex justify-content-between align-items-center">
                        <span>Qty</span>
                        <span>{convertNumber(order.filledAmount) > 0 ? formatCurrencyDecimal(order.filledAmount, 0) : '-'}</span>
                    </div>
                </td>
            </tr>
        })
    )

    const renderDataModify = () => (
        selectedRecord.map((order, idx) => (
            <div key={idx} className="text-center">
                <span><b>{order?.externalOrderId} </b></span><span>: </span>
                <span className="text-uppercase">{order?.side === tradingModelPb.Side.BUY ? 'buy' : 'sell'} </span> 
                {order?.state !== OrderState.ORDER_STATE_PLACED && <span>{_renderRemainQty(order?.amount, order?.filledAmount)} shares of </span>}
                {order?.state === OrderState.ORDER_STATE_PLACED && <span>{order?.amount} shares of </span>}
                <span>{getSymbolDetail(order?.symbolCode)?.symbolName} </span>
                <span>({order?.symbolCode}) at limit price of </span>
                <span>$</span>
                <span>{formatCurrency(order?.price)}</span>
            </div>
        ))
    )

    const handleCancelMultiOrder = () => {
        console.log(`Start send multi-cancel order request at: ${moment().format('YYYY-MM-DD HH:mm:ss')}.${moment().millisecond()}`);
        setShowMessageCancelSuccess(true);
        if (pwdReqWithdraw === PASSWORD_REQ_SUBMIT.ON) {
            getE2EEPresessionInfo();
        } else {
            selectedRecord.forEach(order => {
                requestWithDrawOrder(order, preSessionInfo, pinR, pinROnly8);
            });
            setShowMultiOrderCancelModel(false);
            setSelectedRecord([]);
        }
    }

    const _renderRemainQty = (amount, filledAmount) => {
        if (!amount) return 0;
        if (amount && !filledAmount) return amount;
        return Decimal(amount).minus(Decimal(filledAmount))?.toString();
    }

    const renderMultiCancelOrder = () => {
        return <Modal show={showMultiOrderCancelModel} size="sm" centered="true">
            <div className="modal-header justify-content-center">
                <h5 className="modal-title" id="orderDetailLabel">Withdraw Orders</h5>
            </div>
            <div className="text-center">Confirm to withdraw following orders</div>
            <div className="modal-body mt-15 mb-20">
                {renderDataModify()}
            </div>
            {pwdReqWithdraw === PASSWORD_REQ_SUBMIT.ON &&
                    <div className="mb-3 px-3">
                        <label className="mb-1 opacity-50">Password Confirmation</label>
                        <div className="d-flex">
                            <input type={isShowPassword ? 'text' : 'password'} value={password} placeholder="" id="txtPassword"
                            onChange={(event) => setPassword(event.target.value)}
                            className="form-control text-center rounded-xl bg-dark-light" />
                            <span className="mt-5px" onClick={() => setIsShowPassword(!isShowPassword)}>
                                <i className={`ml-30 ml-rem bi ${isShowPassword ? 'bi-eye-fill' : 'bi-eye-slash-fill'}`}></i>
                            </span>
                        </div>
                    </div>
                }
            <div className="modal-footer d-flex align-items-center border-top">
                <div className="row w-100">
                    <button className="col-6 text-center btn-multiCalcel"
                        onClick={() => {
                        setShowMultiOrderCancelModel(false);
                        setSelectedRecord([]);
                        setPassword("");
                        setIsShowPassword(false);
                    }}>Cancel</button>
                    <button className="col-6 text-center btn-multiCalcel" onClick={() => handleCancelMultiOrder()}
                        disabled={password === '' && pwdReqWithdraw === PASSWORD_REQ_SUBMIT.ON}
                    >Confirm</button>
                </div>
            </div>
        </Modal>
    }

    const _renderOrderDetailModal = () => {
        return <Modal show={showDetailModal} size="sm" centered="true">
            <div className="modal-header ">
                <h5 className="modal-title" id="orderDetailLabel">Order Details</h5>
                <button type="button" className="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close" onClick={() => setShowDetailModal(false)}></button>
            </div>
            <div className="modal-body p-0">
                <div className="d-flex justify-content-between align-items-center px-3 mb-3">
                    <div>
                        <div>{getSymbolDetail(order?.symbolCode)?.symbolName}</div>
                        <div><span className={`badge ${renderCssClassProductType(getProductType(order?.symbolCode))}`}>
                            {PRODUCT_TYPE[getProductType(order?.symbolCode)]}</span> 
                            <span className="opacity-50">{order?.symbolCode}</span>
                        </div>
                    </div>
                    <div className="fs-5 lh-sm text-end">
                        <div>{getOrderStatus(order?.state)}</div>
                    </div>
                </div>
                <div className="p-3 table-responsive mb-3 bg-dark-light">
                    <table className="table table-borderless mb-0">
                        <tbody>
                            <tr>
                                <td className="pt-0 px-0">
                                    <div className="opacity-50">Order No</div>
                                    <div><strong>{order?.externalOrderId}</strong></div>
                                </td>
                                <td className="pt-0 px-0">
                                    <div className="opacity-50">Order Type</div>
                                    <div><strong>{renderOrderType(order?.orderType)}</strong></div>
                                </td>
                                <td className="pt-0 px-0">
                                    <div className="opacity-50">Action</div>
                                    <div className="text-uppercase">{getOrderSide1(order?.side)}</div>
                                </td>
                            </tr>
                            <tr>
                                <td className="pt-0 px-0">
                                    <div className="opacity-50">Price (USD)</div>
                                    <div><strong>{formatCurrencyDecimal(order?.price, DIGIT_DEFAULT)}</strong></div>
                                </td>
                                <td className="pt-0 px-0">
                                    <div className="opacity-50">Quantity</div>
                                    <div><strong>{formatCurrencyDecimal(order?.amount, 0)}</strong></div>
                                </td>
                                <td className="pt-0 px-0">
                                    <div className="opacity-50">Validity</div>
                                    <div><strong>Day</strong></div>
                                </td>
                            </tr>
                            <tr>
                                <td className="p-0">
                                    <div className="opacity-50">Currency</div>
                                    <div><strong>{order?.currencyCode}</strong></div>
                                </td>
                                <td className="p-0">
                                    <div className="opacity-50">Date/Time</div>
                                    <div>
                                        <strong>{moment(convertNumber(order?.time)).format("YYYY-MM-DD")}</strong>
                                        <br />
                                        <strong>{moment(convertNumber(order?.time)).format("HH:mm:ss")}</strong>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div className="opacity-50 px-3 mb-2">
                    Execution Log
                </div>
                    <div className="p-3 table-responsive mb-3 bg-dark-light">
                        <table className="table table-borderless mb-0">
                            <thead>
                                <th className="opacity-50">Status</th>
                                <th className="opacity-50">
                                    {order?.state === OrderState.ORDER_STATE_FILLED || order?.state === OrderState.ORDER_STATE_MATCHED
                                     || order?.state === OrderState.ORDER_STATE_PARTIAL ? <><span>Executed</span><br/><span>Price</span></> : 'Price'}
                                </th>
                                <th className="opacity-50">
                                    {order?.state === OrderState.ORDER_STATE_FILLED || order?.state === OrderState.ORDER_STATE_MATCHED
                                     || order?.state === OrderState.ORDER_STATE_PARTIAL ? <><span>Executed</span><br/><span>Quantity</span></>  : 'Quantity'}
                                </th>
                            </thead>
                            <tbody>
                                <tr>
                                    <td className="pt-0 px-0">
                                        <div><strong>{getOrderStatus(order?.state)}</strong></div>
                                        {order?.state === OrderState.ORDER_STATE_PLACED && <div className="opacity-50">{moment(convertNumber(order?.time)).format(FORMAT_DATE_TIME_SECOND)}</div>}
                                        {order?.state !== OrderState.ORDER_STATE_PLACED && <div className="opacity-50">{moment(convertNumber(order?.executedDatetime)).format(FORMAT_DATE_TIME_SECOND)}</div>}
                                    </td>
                                    <td className="pt-0 pl-px-8">
                                        {(order?.state === OrderState.ORDER_STATE_FILLED || order?.state === OrderState.ORDER_STATE_PARTIAL ||
                                            order?.state === OrderState.ORDER_STATE_MATCHED) && <div>
                                                <strong>{formatCurrencyDecimal(order?.lastPrice, DIGIT_DEFAULT)}</strong>
                                            </div>}
                                        {(order?.state === OrderState.ORDER_STATE_PLACED || order?.state === OrderState.ORDER_STATE_REJECTED ||
                                            order?.state === OrderState.ORDER_STATE_MODIFIED || order?.state === OrderState.ORDER_STATE_CANCELED) && <div><strong>-</strong></div>}
                                    </td>
                                    <td className="pt-0 pl-px-8">
                                        {order?.state === OrderState.ORDER_STATE_PLACED && <div><strong>-</strong></div>}
                                        {order?.state === OrderState.ORDER_STATE_CANCELED && <div><strong>{remainQty}</strong></div>}
                                        {(order?.state === OrderState.ORDER_STATE_FILLED || order?.state === OrderState.ORDER_STATE_PARTIAL ||
                                            order?.state === OrderState.ORDER_STATE_MATCHED || order?.state === OrderState.ORDER_STATE_REJECTED) 
                                            && <div><strong>{order?.filledAmount}</strong></div>}
                                        {order?.state === OrderState.ORDER_STATE_MODIFIED && 
                                            <div><strong>{order?.withdrawAmount}</strong></div>}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
            </div>
            {((order?.state === OrderState.ORDER_STATE_MATCHED && order?.msgCode === systemModel.MsgCode.MT_RET_OK) || order?.state === OrderState.ORDER_STATE_PLACED) && <div className="modal-footer d-flex justify-content-center align-items-center bg-dark-light">
                <button className="button-amend" onClick={() => {
                            setShowAmendModal(true); 
                            setShowDetailModal(false);
                }}>AMEND</button>
                <button className="button-withdraw" onClick={() => {
                    setShowDetailModal(false);
                    setShowWithDrawModal(true);
                }}>WITHDRAW</button>
            </div>}
        </Modal>
    }

    const handleKeyDown = (e) => {
        e.key !== 'Delete' ? setIsAllow(true) : setIsAllow(false);
    }

    const handleAllowed = (values) => {
        const { value, floatValue } = values; 
        if (typeof floatValue === 'undefined' || typeof value === 'undefined') {
            return true;
        }
        if (isAllowed && (value.charAt(0) === '0' || value.charAt(0) === '-')) {
            return false;
        }
        if (props.maximum) {
            return floatValue <= props.maximum;
        } else {
            return true;
        }
    }

    const handleChangeVolume = (event) => {
        let tempValue = '0';
        if (event.value && event.value !== '') {
            tempValue = event.value;
        }
        setIsExceedQty(remainQty < convertNumber(tempValue));
        setRemainQtyDisplay(convertNumber(tempValue));
        const temp = new Decimal(tempValue);
        const grossValue = temp.times(order?.price);
        setIsInvalidMinValue(grossValue.lt(Decimal(minOrderValue)));
        setIsInvalidQty(!checkVolumeLotSize(tempValue, getSymbolDetail(order?.symbolCode)?.lotSize));
    }

    const resetAmendForm = () => {
        setIsExceedQty(false);
    }

    const placeAmendOrder = () => {
        const price = new Decimal(order.price);
        const grossValue = price.times(remainQtyDisplay);
        if (convertNumber(grossValue) < convertNumber(minOrderValue)) {
            setTitleError(MESSAGE_ERROR.get(systemModel.MsgCode.MT_RET_NOT_ENOUGH_MIN_ORDER_VALUE));
            setIsShowError(true);
            setShowAmendModal(false);
            return;
        }
        if (pwdReqAmend === PASSWORD_REQ_SUBMIT.ON) {
            getE2EEPresessionInfo();
        } else {
            requestAmendOrder(preSessionInfo, pinR, pinROnly8)
        }
    }

    const requestAmendOrder = (preSessionInfo, pinR, pinROnly8) => {
        let wsConnect = wsService.getWsConnected();
        let currentDate = new Date();
        if (wsConnect) {
            let modifyOrder = new tradingServicePb.ModifyOrderRequest();
            modifyOrder.setHiddenConfirmFlg(pwdReqAmend === PASSWORD_REQ_SUBMIT.OFF);

            let orderPb = new tradingModelPb.Order();
            orderPb.setOrderId(order.orderId);
            orderPb.setAmount(`${remainQtyDisplay}`);
            orderPb.setPrice(`${order.price}`);
            orderPb.setUid(convertNumber(accountId));
            orderPb.setSymbolCode(order.symbolCode);
            orderPb.setSide(order.side);
            orderPb.setOrderType(order.orderType);
            orderPb.setExecuteMode(tradingModelPb.ExecutionMode.MARKET);
            orderPb.setOrderMode(tradingModelPb.OrderMode.REGULAR);
            orderPb.setRoute(tradingModelPb.OrderRoute.ROUTE_WEB);
            orderPb.setSubmittedId(accountId);
            orderPb.setCurrencyCode(order?.currencyCode);

            if(flagMsgCode) {
                orderPb.setMsgCode(systemModel.MsgCode.MT_RET_FORWARD_EXT_SYSTEM);
            }
            modifyOrder.addOrder(orderPb);

            if (pwdReqAmend === PASSWORD_REQ_SUBMIT.ON) {
                modifyOrder.setSessionId(preSessionInfo.sessionId);
                modifyOrder.setRandomNumber(preSessionInfo.randomNumber);
                modifyOrder.setHashPassword(pinR);
                modifyOrder.setHashPasswordOnly8(pinROnly8);
            }

            let rpcMsg = new rpc.RpcMessage();
            rpcMsg.setPayloadClass(rpc.RpcMessage.Payload.MODIFY_ORDER_REQ);
            rpcMsg.setPayloadData(modifyOrder.serializeBinary());
            rpcMsg.setContextId(currentDate.getTime());

            wsService.sendMessage(rpcMsg.serializeBinary());
        }
    }

    const withdrawOrder = () => {
        setShowMessageCancelSuccess(true);
        if (pwdReqWithdraw === PASSWORD_REQ_SUBMIT.ON) {
            getE2EEPresessionInfo();
        } else {
            requestWithDrawOrder(order, preSessionInfo, pinR, pinROnly8)
        }
    }

    const requestWithDrawOrder = (order, preSessionInfo, pinR, pinROnly8) => {
        let wsConnect = wsService.getWsConnected();
        let currentDate = new Date();
        const amount = order?.state === OrderState.ORDER_STATE_PLACED ? order?.amount : _renderRemainQty(order?.amount, order?.filledAmount);
        const lastPrice = convertNumber(order?.lastPrice) > 0 ? order?.lastPrice : order?.price;
        if (wsConnect) {
            let cancelOrder = new tradingServicePb.CancelOrderRequest();
            cancelOrder.setHiddenConfirmFlg(pwdReqWithdraw === PASSWORD_REQ_SUBMIT.OFF);

            let orderPb = new tradingModelPb.Order();
            orderPb.setOrderId(order.orderId);
            orderPb.setExternalOrderId(order?.externalOrderId);
            orderPb.setAmount(`${amount}`);
            orderPb.setTotalFilledAmount(`${convertNumber(order?.filledAmount)}`);
            orderPb.setFilledAmount(`${amount}`);
            orderPb.setPrice(`${order.price}`);
            orderPb.setLastPrice(`${lastPrice}`);
            orderPb.setUid(convertNumber(accountId));
            orderPb.setSymbolCode(order.symbolCode);
            orderPb.setSide(order.side);
            orderPb.setOrderType(order.orderType);
            orderPb.setExecuteMode(tradingModelPb.ExecutionMode.MARKET);
            orderPb.setOrderMode(tradingModelPb.OrderMode.REGULAR);
            orderPb.setRoute(tradingModelPb.OrderRoute.ROUTE_WEB);
            orderPb.setSubmittedId(accountId);
            orderPb.setCurrencyCode(order?.currencyCode);

            if(flagMsgCode) {
                orderPb.setMsgCode(systemModel.MsgCode.MT_RET_FORWARD_EXT_SYSTEM);
            }
            cancelOrder.addOrder(orderPb);

            if (pwdReqWithdraw === PASSWORD_REQ_SUBMIT.ON) {
                cancelOrder.setSessionId(preSessionInfo.sessionId);
                cancelOrder.setRandomNumber(preSessionInfo.randomNumber);
                cancelOrder.setHashPassword(pinR);
                cancelOrder.setHashPasswordOnly8(pinROnly8);
            }

            let rpcMsg = new rpc.RpcMessage();
            rpcMsg.setPayloadClass(rpc.RpcMessage.Payload.CANCEL_ORDER_REQ);
            rpcMsg.setPayloadData(cancelOrder.serializeBinary());
            rpcMsg.setContextId(currentDate.getTime());

            wsService.sendMessage(rpcMsg.serializeBinary());
            console.log(`Send cancel order request at: ${moment().format('YYYY-MM-DD HH:mm:ss')}.${moment().millisecond()}`);
        }
    }

    const disableAmendOrder = () => {
        return isExceedQty // qty modify must be less than current qty
            || remainQty === convertNumber(remainQtyDisplay) // disable when no have change qty
            || convertNumber(remainQtyDisplay) === 0 // disable when qty = 0
            || (pwdReqAmend === PASSWORD_REQ_SUBMIT.ON && password === '') // disable when password confirm is blank
            || isInvalidMinValue // disable when grossValue < minOrderValue
            || isInvalidQty
    }

    const _renderOrderWithDrawModal = () => (
        <Modal show={showWithDrawModal} size="sm" centered="true">
            <div className="modal-header">
                <h5 className="modal-title" id="orderDetailLabel">Withdraw Order</h5>
                <button type="button" className="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close" onClick={() => {
                    setShowWithDrawModal(false);
                    setPassword("");
                    setIsShowPassword(false);
                }}></button>
            </div>
            <div className='modal-body p-0'>
                <div className="d-flex justify-content-between align-items-center px-3 mb-3">
                    <div>
                        <div>{getSymbolDetail(order?.symbolCode)?.symbolName}</div>
                        <div><span className={`badge ${renderCssClassProductType(getProductType(order?.symbolCode))}`}>
                            {PRODUCT_TYPE[getProductType(order?.symbolCode)]}</span>
                            <span className="opacity-50">{order?.symbolCode}</span>
                        </div>
                    </div>
                    <div className="fs-3">{getOrderStatus(order?.state)}</div>
                </div>
                <div className="p-3 bg-dark-light table-responsive mb-3">
                    <table className="table table-borderless mb-0">
                        <tbody>
                            <tr>
                                <td className="pt-0 px-0">
                                    <div className="opacity-50">Order No</div>
                                    <div><strong>{order?.externalOrderId}</strong></div>
                                </td>
                                <td className="pt-0 px-0">
                                    <div className="opacity-50">Price (USD)</div>
                                    <div><strong>{formatCurrencyDecimal(order?.price, DIGIT_DEFAULT)}</strong></div>
                                </td>
                                <td className="pt-0 px-0">
                                    <div className="opacity-50">Quantity</div>
                                    {order?.state === OrderState.ORDER_STATE_PLACED && <div><strong>{formatCurrencyDecimal(order?.amount, 0)}</strong></div>}
                                    {order?.state !== OrderState.ORDER_STATE_PLACED && <div><strong>{formatCurrencyDecimal(remainQty, 0)}</strong></div>}
                                </td>
                            </tr>
                            <tr>
                                <td className="pt-0 px-0">
                                    <div className="opacity-50">Action</div>
                                    <div className="text-uppercase">{getOrderSide1(order?.side)}</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                {pwdReqWithdraw === PASSWORD_REQ_SUBMIT.ON &&
                    <div className="mb-3 px-3">
                        <label className="mb-1 opacity-50">Password Confirmation</label>
                        <div className="d-flex">
                            <input type={isShowPassword ? 'text' : 'password'} value={password} placeholder="" id="txtPassword"
                            onChange={(event) => setPassword(event.target.value)}
                            className="form-control text-center rounded-xl bg-dark-light" />
                            <span className="mt-5px" onClick={() => setIsShowPassword(!isShowPassword)}>
                                <i className={`ml-30 ml-rem bi ${isShowPassword ? 'bi-eye-fill' : 'bi-eye-slash-fill'}`}></i>
                            </span>
                        </div>
                    </div>
                }
            </div>
            <div className="modal-footer d-flex justify-content-center align-items-center">
                <button type="button" className="btn btn-danger rounded-xl text-transform px-3 mx-2" 
                disabled={pwdReqWithdraw === PASSWORD_REQ_SUBMIT.ON && password === ''}
                onClick={() => withdrawOrder(order)}>Withdraw Order</button>
            </div>
        </Modal>
    )

    const _renderOrderAmendModal = () => {
        return <Modal show={showAmendModal} size="sm" centered="true">
            <div className="modal-header">
                <h5 className="modal-title" id="orderDetailLabel">Amend Order</h5>
                <button type="button" className="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close" onClick={() => {
                    setShowAmendModal(false);
                    resetAmendForm();
                    setRemainQtyDisplay(remainQty);
                    setPassword('');
                    setIsShowPassword(false);
                }}></button>
            </div>
            <div className='modal-body p-0'>
                <div className="d-flex justify-content-between align-items-center px-3 mb-3">
                    <div>
                        <div>{getSymbolDetail(order?.symbolCode)?.symbolName}</div>
                        <div><span className={`badge ${renderCssClassProductType(getProductType(order?.symbolCode))}`}>
                            {PRODUCT_TYPE[getProductType(order?.symbolCode)]}</span> 
                            <span className="opacity-50">{order?.symbolCode}</span>
                        </div>
                    </div>
                    <div className="fs-3">{getOrderStatus(order?.state)}</div>
                </div>
                <div className="p-3 bg-dark-light table-responsive mb-3">
                    <table className="table table-borderless mb-0">
                        <tbody>
                            <tr>
                                <td className="pt-0 px-0">
                                    <span className="opacity-50">Limit Price (USD)</span>
                                </td>
                                <td className="pt-0 px-0 text-end">{formatCurrencyDecimal(order?.price, DIGIT_DEFAULT)}</td>
                            </tr>
                            <tr className="border-top">
                                <td className="px-0">
                                    <span className="opacity-50">Quantity (Lot Size: {getSymbolDetail(order?.symbolCode)?.lotSize})</span>
                                </td>
                                <td className="px-0 text-end">
                                    {/* <input value={order?.filledAmount}></input> */}
                                    <div onKeyDown={handleKeyDown}>
                                        <NumberFormat thousandSeparator={true} decimalScale={0} value={remainQtyDisplay !== 0 ? remainQtyDisplay : '' } 
                                            className="form-control text-end border-0 p-1 fs-5 input_number"
                                            onValueChange={handleChangeVolume}
                                            isAllowed={(values) => handleAllowed(values)}
                                            maxLength={MAX_LENGTH_VOLUME}
                                        />
                                        {isExceedQty && <div className="text-danger text-nowrap">Quantity is exceed order quantity.</div>}
                                        {isInvalidMinValue && <div className="text-danger text-nowrap">{MESSAGE_ERROR.get(systemModel.MsgCode.MT_RET_NOT_ENOUGH_MIN_ORDER_VALUE)}</div>}
                                        {isInvalidQty && <div className="text-danger text-nowrap">Invalid quantity</div>}
                                    </div>
                                    
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                {pwdReqAmend === PASSWORD_REQ_SUBMIT.ON &&
                    <div className="mb-3 px-3">
                        <label className="mb-1 opacity-50">Password Confirmation</label>
                        <div className="d-flex">
                            <input type={isShowPassword ? 'text' : 'password'} value={password} placeholder="" 
                            className="form-control text-center rounded-xl bg-dark-light" id="txtPassword"
                            onChange={(event) => setPassword(event.target.value)} />
                            <span className="mt-5px" onClick={() => setIsShowPassword(!isShowPassword)}>
                                <i className={`ml-30 ml-rem bi ${isShowPassword ? 'bi-eye-fill' : 'bi-eye-slash-fill'}`}></i>
                            </span>
                        </div>
                    </div>
                }
            </div>
            <div className="modal-footer d-flex justify-content-center align-items-center">
                <button type="button" className="btn btn-warning rounded-xl text-transform px-3 mx-2" 
                disabled={disableAmendOrder()}
                onClick={() => placeAmendOrder()}>Amend Order</button>
            </div>
        </Modal>
    }

    const _renderAmendResultModal = () => {
        return <Modal show={showAmendResultModal} size="sm" centered="true">
            <div className="modal-header text-center">
                <h5 className="modal-title" id="amendLabel">Amend Order</h5>
            </div>
            <div className="modal-body pt-0">
                <p className="text-center text-success-light mb-1 fs-5"><i className="bi bi-check-circle-fill"></i></p>
                <p className="text-center">Amend request has been sent</p>
            </div>
            <div className="modal-footer p-0 d-flex justify-content-center align-items-center border-top">
                <button type="button" className="btn btn-link text-info-light flex-grow-1 border-end border-radius-0 m-0" onClick={() => setShowAmendResultModal(false)}>Cancel</button>
                <button type="button" className="btn btn-link text-info-light flex-grow-1 border-radius-0 m-0" onClick={() => setShowAmendResultModal(false)} >Ok</button>
            </div>
        </Modal>
    }

    const _renderWithdrawResultModal = () => {
        return <Modal show={showWithdrawResultModal} size="sm" centered="true">
            <div className="modal-header text-center">
                <h5 className="modal-title" id="withdrawLabel">Withdraw Order</h5>
            </div>
            <div className="modal-body pt-0">
                <p className="text-center text-success-light mb-1 fs-5"><i className="bi bi-check-circle-fill"></i></p>
                <p className="text-center">Withdrawal request has been sent</p>
            </div>
            <div className="modal-footer p-0 d-flex justify-content-center align-items-center border-top">
                <button type="button" className="btn btn-link text-info-light flex-grow-1 border-end border-radius-0 m-0" onClick={() => setShowWithdrawResultModal(false)}>Cancel</button>
                <button type="button" className="btn btn-link text-info-light flex-grow-1 border-radius-0 m-0" onClick={() => setShowWithdrawResultModal(false)}>Ok</button>
            </div>
        </Modal>
    }

    const handleRefresh = () => {
        setIsRefresh(prev => !prev);
        setTimeFrameOrder(today);
        setOrderTime(TimeFrameOrderStatus.today);
        setOrderStatus(OrderState.ORDER_STATE_NONE);
        setOrderType(OrderType.OP_NONE);
        setOrderVia('');
        setSymbol('');
        setSelectedRecord([]);
        setIsPassTime(false);
        setPaging(prev => ({
            ...prev,
            pageIndex: DEFAULT_PAGE_INDEX,
            pageSize: DEFAULT_PAGE_SIZE,
         })
        )
    }

    return (
        <div className="site">
            <Header page="order-status" />
            <div className="site-main py-3">
                <div className="container">
                    <div className="card">
                        <div className="card-header justify-content-between d-flex header-order-status">
                            <div><h5 className="card-title mb-0">Order Status</h5></div>
                            <div className="text-status" onClick={handleRefresh}>
                                <span><i className="bi bi-arrow-clockwise"></i></span>
                                <span className="ms-1"                         
                                >Refresh</span>
                            </div>
                        </div>
                        {sizeScreen >= SCREEN_SIZE_WEB_MIN ? _renderSearchBox() : _renderSearchBoxMobile()}
                        {_renderResultBox()}
                    </div>
                </div>
            </div>
            <ToastContainer theme="colored" />
            {_renderOrderDetailModal()}
            {_renderOrderAmendModal()}
            {_renderAmendResultModal()}
            {_renderWithdrawResultModal()}
            {_renderSuccessModal()}
            {_renderOrderWithDrawModal()}
            {renderMultiCancelOrder()}
            {isShowError && <PopupTimeout title={titleError} setErrorFunc={setIsShowError}/>}
            <div>
                <input type="hidden" id="E2EE_PUBLIC_KEY" />
                <input type="hidden" id="E2EE_SESSION_ID" />
                <input type="hidden" id="E2EE_RANDOM_NUMBER" />
            </div>
            {isShowLazyLoad && <LazyLoad />}
        </div>
    )
}

export default React.memo(OrderStatus);