export const onSessionTimeout = () => {
    console.log('Session Timeout')
    const platform = getMobileOperatingSystem()
    if (platform === "Android") {
        /*global Android*/
        /*eslint no-undef: "error"*/
        Android.onSessionTimeout('Session Timeout Android');
    }
    if (platform === "IOS") {
        window.webkit.messageHandlers.onSessionTimeout.postMessage('Session Timeout IOS');
    }
    
}

export const getMobileOperatingSystem = () => {
    var userAgent = navigator.userAgent || navigator.vendor || window.opera;
    if (/windows phone/i.test(userAgent)) {
        return "windowsPhone";
    }
    if (/android/i.test(userAgent)) {
        return "Android";
    }
    if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
        return "IOS";
    }
    return "unknown";
}