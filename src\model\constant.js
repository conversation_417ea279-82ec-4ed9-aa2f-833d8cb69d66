import * as smpb from '../model/proto/system_model_pb';
import { ProductType } from '../model/proto/query_model_pb';
import { formatCurrency } from './utils';

const systemModel = smpb;

export const LocalStorageKey = {
    LANGUAGE: 'retail_language',
    SYMBOL_LIST: 'retail_symbols_list',
    TOKEN: 'retail_token',
    ACCOUNT_TYPE: 'retail_account_type',
    ACCOUNT_ID: 'retail_accountId',
    POEM_ID: 'retail_poem_id',
    PWD_REQ_SUBMIT: 'retail_pwd_req_submit',

    PWD_REQ_AMEND: 'retail_pwd_req_amend',
    PWD_REQ_WITHDRAW: 'retail_pwd_req_withdraw',

    TOKEN_EXPIRED: 'retail_token_expired',
    MIN_ORDER_VALUE: 'retail_min_order_value',
    MAX_ORDER_VOLUME: 'retail_max_order_volume',
    ACK_FLAG: 'retail_ack_flag',
    SYMBOL_LIST_FULL: 'retail_symbol_list_full',
    LAZY_LOAD_STATUS: 'lazyLoadStatus',
    MAX_ORDER_VALUE: 'retail_max_order_value',
    THEME: 'retail_theme',
    DETECT_SIDE_NOT_EMBED: 'detect_side_not_embed'
}

export const Pages = {
    OUTSTANDING_POSITION: "OUTSTANDING_POSITION",
    ACCOUNT_DETAIL: "ACCOUNT_DETAIL",
    SCRIPT_POSITION: "SCRIPT_POSITION",
    MONTH_TRANSACTION: "MONTH_TRANSACTION",
    TRANSCTION_HISTORY: "TRANSCTION_HISTORY",
    SETTLED_POSTION: "SETTLED_POSTION"
}

export const SocketKey = {
    MAX_RETRY_WS: "MAX_RETRY_WS",
    SOCKET_CONNECTED: "SOCKET_CONNECTED",
    SOCKET_RECONNECTED: "SOCKET_RECONNECTED",
    SOCKET_EXPIRE_TOKEN: "SOCKET_EXPIRE_TOKEN"
}

export const OrderTypeName = {
    buy: 'BUY',
    sell: 'SELL'
}
export const MARKET = {
    US: 'US',
};
export const MarketDepthLength = 3;

export const TimeFrameOrderStatus = {
    today: 'today',
    pass: 'pass'
}

export const ORDER_SIDE = {
    BUY: 'buy',
    SELL: 'sell'
}

export const CURRENCY = {
    USD: 'US$',
    SGD: 'S$'
}

export const CURRENCY_DISPLAY = {
    USD: 'USD',
    SGD: 'SGD'
}

export const PROGRESS = {
    none: '0',
    done: '100'
}

export const DATE = {
    today: 'today'
}

export const FROM_DATE_TIME = '00:00:00';

export const TO_DATE_TIME = '23:59:59';

export const FORMAT_DATE_TIME_SECOND = 'YYYY-MM-DD HH:mm:ss';

export const FORMAT_DATE_TIME = 'DD-MM-YYYY';

export const FORMAT_DATE_TIME_YEAR = 'YYYY-MM-DD';

export const FORMAT_DATE_TIME_DISPLAY = 'DD/MM/YYYY, HH:mm:ss.SSS';

export const MAX_LENGTH_VOLUME = 15;

export const MESSAGE_ERROR = new Map([
    [systemModel.MsgCode.MT_RET_ERROR, 'Internal Server Error'],
    [systemModel.MsgCode.MT_RET_ERR_PARAM, 'Invalid Format'],
    [systemModel.MsgCode.MT_RET_ERR_PERMISSIONS, 'Not authorized to access the resoures'],
    [systemModel.MsgCode.MT_RET_ERR_TIMEOUT, 'Request timed out'],

    [systemModel.MsgCode.MT_RET_ERR_NOTFOUND, 'Not Found'],
    [systemModel.MsgCode.MT_RET_AUTH_ACCOUNT_INVALID, 'Account invalid'],
    [systemModel.MsgCode.MT_RET_AUTH_ACCOUNT_DISABLED, 'Authen account disabled'],
    [systemModel.MsgCode.MT_RET_REQUEST_INVALID_VOLUME, 'Invalid Quantity'],
    [systemModel.MsgCode.MT_RET_FORWARD_EXT_SYSTEM, 'Orders forwarded to external trading system'],

    [systemModel.MsgCode.MT_RET_ERR_NOT_ENOUGH_MONEY, 'Not enough money'],
    [systemModel.MsgCode.MT_RET_REQUEST_INVALID_FILL, 'Insufficient liquidity for this trade'],
    [systemModel.MsgCode.MT_RET_REQUEST_LIMIT_VOLUME, 'Request limit quantity'],
    [systemModel.MsgCode.MT_RET_REQUEST_INVALID_ORDER_TYPE, 'Invalid Order Type'],

    [systemModel.MsgCode.MT_RET_REQUEST_INVALID_LIMIT_PRICE, 'Request invalid limt price'],
    [systemModel.MsgCode.MT_RET_REQUEST_INVALID_TRIGGER_PRICE, 'Request invalid trigger price'],
    [systemModel.MsgCode.MT_RET_REQUEST_PROHIBITED_OPEN_ORDER, 'Request prohibited open order'],
    [systemModel.MsgCode.MT_RET_REQUEST_PROHIBITED_CLOSE_ORDER, 'Request prohibited close order'],

    [systemModel.MsgCode.MT_RET_MARKET_CLOSED, 'Market closed'],
    [systemModel.MsgCode.MT_RET_INVALID_TICK_SIZE, 'Invalid price'],
    [systemModel.MsgCode.MT_RET_INVALID_PRICE_RANGE, 'Out of price range'],
    [systemModel.MsgCode.MT_RET_INVALID_MIN_LOT, 'Invalid min lot'],

    [systemModel.MsgCode.MT_RET_INVALID_LOT_SIZE, 'Invalid lot size'],
    [systemModel.MsgCode.MT_RET_NEGATIVE_QTY, 'Negative qty'],
    [systemModel.MsgCode.MT_RET_EXCEED_MAX_ORDER_VOLUME, 'Quantity is exceed max order quantity'],
    [systemModel.MsgCode.MT_RET_NOT_ENOUGH_MIN_ORDER_VALUE, `The order is less than USD ${formatCurrency(localStorage.getItem(LocalStorageKey.MIN_ORDER_VALUE))}`],
    [systemModel.MsgCode.MT_RET_INVALID_HOLIDAY_SESSION, 'Market closed due to holiday'],
    [systemModel.MsgCode.MT_RET_TOKEN_EXPIRED, 'Token expired'],
    [systemModel.MsgCode.MT_RET_EXIST_LIMIT_ORDER_IN_QUEUE, 'There is an existing limit order in queue'],
    [systemModel.MsgCode.MT_RET_INVALID_PASSWORD, 'Invalid password'],
    [systemModel.MsgCode.MT_RET_EXCEED_MAX_ORDER_VALUE, 'Gross value is exceed max order value'],
    [systemModel.MsgCode.MT_RET_UNKNOWN_ORDER_ID, 'Insufficient liquidity for this trade'],
    [systemModel.MsgCode.MT_RET_REQUEST_MARKET_ACCESS_DENIED, 'Not eligible to trade in current market'],
  ]);

export const COMMENT_FROM_SERVER = {
    ERROR_ORDER_VALUE: 'Order not enough min order value',
    MARKET_CLOSE_HOLIDAY: 'Order in holiday session'
}

export const PASSWORD_REQ_SUBMIT = {
    ON: '1',
    OFF: '0'
}

export const UNSTABLE_CONNECTION = 'Connection failed. Please check your internet connection';

export const EXISTING_LIMIT_ORDER = 'Order is not submitted as you have a pending order in queue. Kindly withdraw your existing order before placing a new order.'

export const NO_CHANGE_IN_BENEFICIARY_RESPONSE = "No-Change-in-Beneficiary Trade";
export const NO_CHANGE_IN_BENEFICIARY_DISPLAY = "Order not placed as there is no change in beneficiary";

export const HANDLE_MODIFY_REQUEST = 'Handle modify request';
export const HANDLE_NEW_ORDER_REQUEST = 'Handle new order request';

export const TOAST_MESSAGE_SELECT_AMEND = 'Please select only one order to amend';

export const DIGIT_DEFAULT = 2;

export const THEME_DEFAULT ='dark';

export const PRODUCT_TYPE = {
    [ProductType.PRODUCT_TYPE_EQ]: 'EQ',
    [ProductType.PRODUCT_TYPE_ETF]: 'ETF',
    [ProductType.PRODUCT_TYPE_NONE]: '',
};

export const TOAST_MESSAGE_CANCEL_REQUEST = 'Withdrawal request has been sent';

export const DEFAULT_PAGE_INDEX = 1;

export const DEFAULT_PAGE_SIZE = 10;