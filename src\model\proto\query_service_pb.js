// source: query_service.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = (function() {
  if (this) { return this; }
  if (typeof window !== 'undefined') { return window; }
  if (typeof global !== 'undefined') { return global; }
  if (typeof self !== 'undefined') { return self; }
  return Function('return this')();
}.call(null));

var trading_model_pb = require('./trading_model_pb.js');
goog.object.extend(proto, trading_model_pb);
var system_model_pb = require('./system_model_pb.js');
goog.object.extend(proto, system_model_pb);
var query_model_pb = require('./query_model_pb.js');
goog.object.extend(proto, query_model_pb);
goog.exportSymbol('proto.GetContractRequest', null, global);
goog.exportSymbol('proto.GetContractResponse', null, global);
goog.exportSymbol('proto.GetOrderHistoryRequest', null, global);
goog.exportSymbol('proto.GetOrderHistoryResponse', null, global);
goog.exportSymbol('proto.GetOrderRequest', null, global);
goog.exportSymbol('proto.GetOrderResponse', null, global);
goog.exportSymbol('proto.GetOrderStatusRequest', null, global);
goog.exportSymbol('proto.GetOrderStatusResponse', null, global);
goog.exportSymbol('proto.GetPositionRequest', null, global);
goog.exportSymbol('proto.GetPositionResponse', null, global);
goog.exportSymbol('proto.GetTradeHistoryRequest', null, global);
goog.exportSymbol('proto.GetTradeHistoryResponse', null, global);
goog.exportSymbol('proto.SymbolListRequest', null, global);
goog.exportSymbol('proto.SymbolListResponse', null, global);
goog.exportSymbol('proto.SymbolUpdateRequest', null, global);
goog.exportSymbol('proto.SymbolUpdateResponse', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.GetOrderRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.GetOrderRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.GetOrderRequest.displayName = 'proto.GetOrderRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.GetOrderResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.GetOrderResponse.repeatedFields_, null);
};
goog.inherits(proto.GetOrderResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.GetOrderResponse.displayName = 'proto.GetOrderResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.GetContractRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.GetContractRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.GetContractRequest.displayName = 'proto.GetContractRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.GetContractResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.GetContractResponse.repeatedFields_, null);
};
goog.inherits(proto.GetContractResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.GetContractResponse.displayName = 'proto.GetContractResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.GetPositionRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.GetPositionRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.GetPositionRequest.displayName = 'proto.GetPositionRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.GetPositionResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.GetPositionResponse.repeatedFields_, null);
};
goog.inherits(proto.GetPositionResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.GetPositionResponse.displayName = 'proto.GetPositionResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.SymbolListRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.SymbolListRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.SymbolListRequest.displayName = 'proto.SymbolListRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.SymbolListResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.SymbolListResponse.repeatedFields_, null);
};
goog.inherits(proto.SymbolListResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.SymbolListResponse.displayName = 'proto.SymbolListResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.GetTradeHistoryRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.GetTradeHistoryRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.GetTradeHistoryRequest.displayName = 'proto.GetTradeHistoryRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.GetTradeHistoryResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.GetTradeHistoryResponse.repeatedFields_, null);
};
goog.inherits(proto.GetTradeHistoryResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.GetTradeHistoryResponse.displayName = 'proto.GetTradeHistoryResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.GetOrderHistoryRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.GetOrderHistoryRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.GetOrderHistoryRequest.displayName = 'proto.GetOrderHistoryRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.GetOrderHistoryResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.GetOrderHistoryResponse.repeatedFields_, null);
};
goog.inherits(proto.GetOrderHistoryResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.GetOrderHistoryResponse.displayName = 'proto.GetOrderHistoryResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.SymbolUpdateRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.SymbolUpdateRequest.repeatedFields_, null);
};
goog.inherits(proto.SymbolUpdateRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.SymbolUpdateRequest.displayName = 'proto.SymbolUpdateRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.SymbolUpdateResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.SymbolUpdateResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.SymbolUpdateResponse.displayName = 'proto.SymbolUpdateResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.GetOrderStatusRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.GetOrderStatusRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.GetOrderStatusRequest.displayName = 'proto.GetOrderStatusRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.GetOrderStatusResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.GetOrderStatusResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.GetOrderStatusResponse.displayName = 'proto.GetOrderStatusResponse';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.GetOrderRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.GetOrderRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.GetOrderRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GetOrderRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    symbolCode: jspb.Message.getFieldWithDefault(msg, 1, ""),
    accountId: jspb.Message.getFieldWithDefault(msg, 2, 0),
    side: jspb.Message.getFieldWithDefault(msg, 3, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.GetOrderRequest}
 */
proto.GetOrderRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.GetOrderRequest;
  return proto.GetOrderRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.GetOrderRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.GetOrderRequest}
 */
proto.GetOrderRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSymbolCode(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setAccountId(value);
      break;
    case 3:
      var value = /** @type {!proto.Side} */ (reader.readEnum());
      msg.setSide(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.GetOrderRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.GetOrderRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.GetOrderRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GetOrderRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSymbolCode();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getAccountId();
  if (f !== 0) {
    writer.writeInt64(
      2,
      f
    );
  }
  f = message.getSide();
  if (f !== 0.0) {
    writer.writeEnum(
      3,
      f
    );
  }
};


/**
 * optional string symbol_code = 1;
 * @return {string}
 */
proto.GetOrderRequest.prototype.getSymbolCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.GetOrderRequest} returns this
 */
proto.GetOrderRequest.prototype.setSymbolCode = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional int64 account_id = 2;
 * @return {number}
 */
proto.GetOrderRequest.prototype.getAccountId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.GetOrderRequest} returns this
 */
proto.GetOrderRequest.prototype.setAccountId = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional Side side = 3;
 * @return {!proto.Side}
 */
proto.GetOrderRequest.prototype.getSide = function() {
  return /** @type {!proto.Side} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {!proto.Side} value
 * @return {!proto.GetOrderRequest} returns this
 */
proto.GetOrderRequest.prototype.setSide = function(value) {
  return jspb.Message.setProto3EnumField(this, 3, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.GetOrderResponse.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.GetOrderResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.GetOrderResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.GetOrderResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GetOrderResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    msgCode: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msgText: jspb.Message.getFieldWithDefault(msg, 2, ""),
    orderList: jspb.Message.toObjectList(msg.getOrderList(),
    trading_model_pb.Order.toObject, includeInstance),
    accountId: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.GetOrderResponse}
 */
proto.GetOrderResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.GetOrderResponse;
  return proto.GetOrderResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.GetOrderResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.GetOrderResponse}
 */
proto.GetOrderResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.MsgCode} */ (reader.readEnum());
      msg.setMsgCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsgText(value);
      break;
    case 3:
      var value = new trading_model_pb.Order;
      reader.readMessage(value,trading_model_pb.Order.deserializeBinaryFromReader);
      msg.addOrder(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setAccountId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.GetOrderResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.GetOrderResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.GetOrderResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GetOrderResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMsgCode();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getMsgText();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getOrderList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      trading_model_pb.Order.serializeBinaryToWriter
    );
  }
  f = message.getAccountId();
  if (f !== 0) {
    writer.writeInt64(
      4,
      f
    );
  }
};


/**
 * optional MsgCode msg_code = 1;
 * @return {!proto.MsgCode}
 */
proto.GetOrderResponse.prototype.getMsgCode = function() {
  return /** @type {!proto.MsgCode} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.MsgCode} value
 * @return {!proto.GetOrderResponse} returns this
 */
proto.GetOrderResponse.prototype.setMsgCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional string msg_text = 2;
 * @return {string}
 */
proto.GetOrderResponse.prototype.getMsgText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.GetOrderResponse} returns this
 */
proto.GetOrderResponse.prototype.setMsgText = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * repeated Order order = 3;
 * @return {!Array<!proto.Order>}
 */
proto.GetOrderResponse.prototype.getOrderList = function() {
  return /** @type{!Array<!proto.Order>} */ (
    jspb.Message.getRepeatedWrapperField(this, trading_model_pb.Order, 3));
};


/**
 * @param {!Array<!proto.Order>} value
 * @return {!proto.GetOrderResponse} returns this
*/
proto.GetOrderResponse.prototype.setOrderList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.Order=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Order}
 */
proto.GetOrderResponse.prototype.addOrder = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.Order, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.GetOrderResponse} returns this
 */
proto.GetOrderResponse.prototype.clearOrderList = function() {
  return this.setOrderList([]);
};


/**
 * optional int64 account_id = 4;
 * @return {number}
 */
proto.GetOrderResponse.prototype.getAccountId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.GetOrderResponse} returns this
 */
proto.GetOrderResponse.prototype.setAccountId = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.GetContractRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.GetContractRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.GetContractRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GetContractRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    symbolCode: jspb.Message.getFieldWithDefault(msg, 1, ""),
    accountId: jspb.Message.getFieldWithDefault(msg, 2, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.GetContractRequest}
 */
proto.GetContractRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.GetContractRequest;
  return proto.GetContractRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.GetContractRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.GetContractRequest}
 */
proto.GetContractRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSymbolCode(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setAccountId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.GetContractRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.GetContractRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.GetContractRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GetContractRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSymbolCode();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getAccountId();
  if (f !== 0) {
    writer.writeInt64(
      2,
      f
    );
  }
};


/**
 * optional string symbol_code = 1;
 * @return {string}
 */
proto.GetContractRequest.prototype.getSymbolCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.GetContractRequest} returns this
 */
proto.GetContractRequest.prototype.setSymbolCode = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional int64 account_id = 2;
 * @return {number}
 */
proto.GetContractRequest.prototype.getAccountId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.GetContractRequest} returns this
 */
proto.GetContractRequest.prototype.setAccountId = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.GetContractResponse.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.GetContractResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.GetContractResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.GetContractResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GetContractResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    msgCode: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msgText: jspb.Message.getFieldWithDefault(msg, 2, ""),
    contractList: jspb.Message.toObjectList(msg.getContractList(),
    trading_model_pb.Contract.toObject, includeInstance),
    accountId: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.GetContractResponse}
 */
proto.GetContractResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.GetContractResponse;
  return proto.GetContractResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.GetContractResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.GetContractResponse}
 */
proto.GetContractResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.MsgCode} */ (reader.readEnum());
      msg.setMsgCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsgText(value);
      break;
    case 3:
      var value = new trading_model_pb.Contract;
      reader.readMessage(value,trading_model_pb.Contract.deserializeBinaryFromReader);
      msg.addContract(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setAccountId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.GetContractResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.GetContractResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.GetContractResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GetContractResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMsgCode();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getMsgText();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getContractList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      trading_model_pb.Contract.serializeBinaryToWriter
    );
  }
  f = message.getAccountId();
  if (f !== 0) {
    writer.writeInt64(
      4,
      f
    );
  }
};


/**
 * optional MsgCode msg_code = 1;
 * @return {!proto.MsgCode}
 */
proto.GetContractResponse.prototype.getMsgCode = function() {
  return /** @type {!proto.MsgCode} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.MsgCode} value
 * @return {!proto.GetContractResponse} returns this
 */
proto.GetContractResponse.prototype.setMsgCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional string msg_text = 2;
 * @return {string}
 */
proto.GetContractResponse.prototype.getMsgText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.GetContractResponse} returns this
 */
proto.GetContractResponse.prototype.setMsgText = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * repeated Contract contract = 3;
 * @return {!Array<!proto.Contract>}
 */
proto.GetContractResponse.prototype.getContractList = function() {
  return /** @type{!Array<!proto.Contract>} */ (
    jspb.Message.getRepeatedWrapperField(this, trading_model_pb.Contract, 3));
};


/**
 * @param {!Array<!proto.Contract>} value
 * @return {!proto.GetContractResponse} returns this
*/
proto.GetContractResponse.prototype.setContractList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.Contract=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Contract}
 */
proto.GetContractResponse.prototype.addContract = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.Contract, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.GetContractResponse} returns this
 */
proto.GetContractResponse.prototype.clearContractList = function() {
  return this.setContractList([]);
};


/**
 * optional int64 account_id = 4;
 * @return {number}
 */
proto.GetContractResponse.prototype.getAccountId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.GetContractResponse} returns this
 */
proto.GetContractResponse.prototype.setAccountId = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.GetPositionRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.GetPositionRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.GetPositionRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GetPositionRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    accountId: jspb.Message.getFieldWithDefault(msg, 1, 0),
    symbolCode: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.GetPositionRequest}
 */
proto.GetPositionRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.GetPositionRequest;
  return proto.GetPositionRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.GetPositionRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.GetPositionRequest}
 */
proto.GetPositionRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setAccountId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSymbolCode(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.GetPositionRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.GetPositionRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.GetPositionRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GetPositionRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAccountId();
  if (f !== 0) {
    writer.writeInt64(
      1,
      f
    );
  }
  f = message.getSymbolCode();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int64 account_id = 1;
 * @return {number}
 */
proto.GetPositionRequest.prototype.getAccountId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.GetPositionRequest} returns this
 */
proto.GetPositionRequest.prototype.setAccountId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string symbol_code = 2;
 * @return {string}
 */
proto.GetPositionRequest.prototype.getSymbolCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.GetPositionRequest} returns this
 */
proto.GetPositionRequest.prototype.setSymbolCode = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.GetPositionResponse.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.GetPositionResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.GetPositionResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.GetPositionResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GetPositionResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    msgCode: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msgText: jspb.Message.getFieldWithDefault(msg, 2, ""),
    contractList: jspb.Message.toObjectList(msg.getContractList(),
    trading_model_pb.Position.toObject, includeInstance),
    symbolCode: jspb.Message.getFieldWithDefault(msg, 4, ""),
    accountId: jspb.Message.getFieldWithDefault(msg, 5, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.GetPositionResponse}
 */
proto.GetPositionResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.GetPositionResponse;
  return proto.GetPositionResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.GetPositionResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.GetPositionResponse}
 */
proto.GetPositionResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.MsgCode} */ (reader.readEnum());
      msg.setMsgCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsgText(value);
      break;
    case 3:
      var value = new trading_model_pb.Position;
      reader.readMessage(value,trading_model_pb.Position.deserializeBinaryFromReader);
      msg.addContract(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setSymbolCode(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setAccountId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.GetPositionResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.GetPositionResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.GetPositionResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GetPositionResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMsgCode();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getMsgText();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getContractList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      trading_model_pb.Position.serializeBinaryToWriter
    );
  }
  f = message.getSymbolCode();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getAccountId();
  if (f !== 0) {
    writer.writeInt64(
      5,
      f
    );
  }
};


/**
 * optional MsgCode msg_code = 1;
 * @return {!proto.MsgCode}
 */
proto.GetPositionResponse.prototype.getMsgCode = function() {
  return /** @type {!proto.MsgCode} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.MsgCode} value
 * @return {!proto.GetPositionResponse} returns this
 */
proto.GetPositionResponse.prototype.setMsgCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional string msg_text = 2;
 * @return {string}
 */
proto.GetPositionResponse.prototype.getMsgText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.GetPositionResponse} returns this
 */
proto.GetPositionResponse.prototype.setMsgText = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * repeated Position contract = 3;
 * @return {!Array<!proto.Position>}
 */
proto.GetPositionResponse.prototype.getContractList = function() {
  return /** @type{!Array<!proto.Position>} */ (
    jspb.Message.getRepeatedWrapperField(this, trading_model_pb.Position, 3));
};


/**
 * @param {!Array<!proto.Position>} value
 * @return {!proto.GetPositionResponse} returns this
*/
proto.GetPositionResponse.prototype.setContractList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.Position=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Position}
 */
proto.GetPositionResponse.prototype.addContract = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.Position, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.GetPositionResponse} returns this
 */
proto.GetPositionResponse.prototype.clearContractList = function() {
  return this.setContractList([]);
};


/**
 * optional string symbol_code = 4;
 * @return {string}
 */
proto.GetPositionResponse.prototype.getSymbolCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.GetPositionResponse} returns this
 */
proto.GetPositionResponse.prototype.setSymbolCode = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional int64 account_id = 5;
 * @return {number}
 */
proto.GetPositionResponse.prototype.getAccountId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.GetPositionResponse} returns this
 */
proto.GetPositionResponse.prototype.setAccountId = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.SymbolListRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.SymbolListRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.SymbolListRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.SymbolListRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    accountId: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.SymbolListRequest}
 */
proto.SymbolListRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.SymbolListRequest;
  return proto.SymbolListRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.SymbolListRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.SymbolListRequest}
 */
proto.SymbolListRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setAccountId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.SymbolListRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.SymbolListRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.SymbolListRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.SymbolListRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAccountId();
  if (f !== 0) {
    writer.writeInt64(
      1,
      f
    );
  }
};


/**
 * optional int64 account_id = 1;
 * @return {number}
 */
proto.SymbolListRequest.prototype.getAccountId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.SymbolListRequest} returns this
 */
proto.SymbolListRequest.prototype.setAccountId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.SymbolListResponse.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.SymbolListResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.SymbolListResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.SymbolListResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.SymbolListResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    msgCode: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msgText: jspb.Message.getFieldWithDefault(msg, 2, ""),
    symbolList: jspb.Message.toObjectList(msg.getSymbolList(),
    query_model_pb.Symbol.toObject, includeInstance),
    accountId: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.SymbolListResponse}
 */
proto.SymbolListResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.SymbolListResponse;
  return proto.SymbolListResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.SymbolListResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.SymbolListResponse}
 */
proto.SymbolListResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.MsgCode} */ (reader.readEnum());
      msg.setMsgCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsgText(value);
      break;
    case 3:
      var value = new query_model_pb.Symbol;
      reader.readMessage(value,query_model_pb.Symbol.deserializeBinaryFromReader);
      msg.addSymbol(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setAccountId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.SymbolListResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.SymbolListResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.SymbolListResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.SymbolListResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMsgCode();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getMsgText();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSymbolList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      query_model_pb.Symbol.serializeBinaryToWriter
    );
  }
  f = message.getAccountId();
  if (f !== 0) {
    writer.writeInt64(
      4,
      f
    );
  }
};


/**
 * optional MsgCode msg_code = 1;
 * @return {!proto.MsgCode}
 */
proto.SymbolListResponse.prototype.getMsgCode = function() {
  return /** @type {!proto.MsgCode} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.MsgCode} value
 * @return {!proto.SymbolListResponse} returns this
 */
proto.SymbolListResponse.prototype.setMsgCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional string msg_text = 2;
 * @return {string}
 */
proto.SymbolListResponse.prototype.getMsgText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.SymbolListResponse} returns this
 */
proto.SymbolListResponse.prototype.setMsgText = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * repeated Symbol symbol = 3;
 * @return {!Array<!proto.Symbol>}
 */
proto.SymbolListResponse.prototype.getSymbolList = function() {
  return /** @type{!Array<!proto.Symbol>} */ (
    jspb.Message.getRepeatedWrapperField(this, query_model_pb.Symbol, 3));
};


/**
 * @param {!Array<!proto.Symbol>} value
 * @return {!proto.SymbolListResponse} returns this
*/
proto.SymbolListResponse.prototype.setSymbolList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.Symbol=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Symbol}
 */
proto.SymbolListResponse.prototype.addSymbol = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.Symbol, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.SymbolListResponse} returns this
 */
proto.SymbolListResponse.prototype.clearSymbolList = function() {
  return this.setSymbolList([]);
};


/**
 * optional int64 account_id = 4;
 * @return {number}
 */
proto.SymbolListResponse.prototype.getAccountId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.SymbolListResponse} returns this
 */
proto.SymbolListResponse.prototype.setAccountId = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.GetTradeHistoryRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.GetTradeHistoryRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.GetTradeHistoryRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GetTradeHistoryRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    symbolCode: jspb.Message.getFieldWithDefault(msg, 1, ""),
    orderType: jspb.Message.getFieldWithDefault(msg, 2, 0),
    fromDatetime: jspb.Message.getFieldWithDefault(msg, 3, 0),
    toDatetime: jspb.Message.getFieldWithDefault(msg, 4, 0),
    accountId: jspb.Message.getFieldWithDefault(msg, 5, 0),
    side: jspb.Message.getFieldWithDefault(msg, 6, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.GetTradeHistoryRequest}
 */
proto.GetTradeHistoryRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.GetTradeHistoryRequest;
  return proto.GetTradeHistoryRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.GetTradeHistoryRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.GetTradeHistoryRequest}
 */
proto.GetTradeHistoryRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSymbolCode(value);
      break;
    case 2:
      var value = /** @type {!proto.OrderType} */ (reader.readEnum());
      msg.setOrderType(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setFromDatetime(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setToDatetime(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setAccountId(value);
      break;
    case 6:
      var value = /** @type {!proto.Side} */ (reader.readEnum());
      msg.setSide(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.GetTradeHistoryRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.GetTradeHistoryRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.GetTradeHistoryRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GetTradeHistoryRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSymbolCode();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getOrderType();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
  f = message.getFromDatetime();
  if (f !== 0) {
    writer.writeInt64(
      3,
      f
    );
  }
  f = message.getToDatetime();
  if (f !== 0) {
    writer.writeInt64(
      4,
      f
    );
  }
  f = message.getAccountId();
  if (f !== 0) {
    writer.writeInt64(
      5,
      f
    );
  }
  f = message.getSide();
  if (f !== 0.0) {
    writer.writeEnum(
      6,
      f
    );
  }
};


/**
 * optional string symbol_code = 1;
 * @return {string}
 */
proto.GetTradeHistoryRequest.prototype.getSymbolCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.GetTradeHistoryRequest} returns this
 */
proto.GetTradeHistoryRequest.prototype.setSymbolCode = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional OrderType order_type = 2;
 * @return {!proto.OrderType}
 */
proto.GetTradeHistoryRequest.prototype.getOrderType = function() {
  return /** @type {!proto.OrderType} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.OrderType} value
 * @return {!proto.GetTradeHistoryRequest} returns this
 */
proto.GetTradeHistoryRequest.prototype.setOrderType = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};


/**
 * optional int64 from_datetime = 3;
 * @return {number}
 */
proto.GetTradeHistoryRequest.prototype.getFromDatetime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.GetTradeHistoryRequest} returns this
 */
proto.GetTradeHistoryRequest.prototype.setFromDatetime = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional int64 to_datetime = 4;
 * @return {number}
 */
proto.GetTradeHistoryRequest.prototype.getToDatetime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.GetTradeHistoryRequest} returns this
 */
proto.GetTradeHistoryRequest.prototype.setToDatetime = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional int64 account_id = 5;
 * @return {number}
 */
proto.GetTradeHistoryRequest.prototype.getAccountId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.GetTradeHistoryRequest} returns this
 */
proto.GetTradeHistoryRequest.prototype.setAccountId = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional Side side = 6;
 * @return {!proto.Side}
 */
proto.GetTradeHistoryRequest.prototype.getSide = function() {
  return /** @type {!proto.Side} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {!proto.Side} value
 * @return {!proto.GetTradeHistoryRequest} returns this
 */
proto.GetTradeHistoryRequest.prototype.setSide = function(value) {
  return jspb.Message.setProto3EnumField(this, 6, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.GetTradeHistoryResponse.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.GetTradeHistoryResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.GetTradeHistoryResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.GetTradeHistoryResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GetTradeHistoryResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    msgCode: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msgText: jspb.Message.getFieldWithDefault(msg, 2, ""),
    tradeList: jspb.Message.toObjectList(msg.getTradeList(),
    trading_model_pb.Trade.toObject, includeInstance),
    accountId: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.GetTradeHistoryResponse}
 */
proto.GetTradeHistoryResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.GetTradeHistoryResponse;
  return proto.GetTradeHistoryResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.GetTradeHistoryResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.GetTradeHistoryResponse}
 */
proto.GetTradeHistoryResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.MsgCode} */ (reader.readEnum());
      msg.setMsgCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsgText(value);
      break;
    case 3:
      var value = new trading_model_pb.Trade;
      reader.readMessage(value,trading_model_pb.Trade.deserializeBinaryFromReader);
      msg.addTrade(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setAccountId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.GetTradeHistoryResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.GetTradeHistoryResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.GetTradeHistoryResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GetTradeHistoryResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMsgCode();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getMsgText();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getTradeList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      trading_model_pb.Trade.serializeBinaryToWriter
    );
  }
  f = message.getAccountId();
  if (f !== 0) {
    writer.writeInt64(
      4,
      f
    );
  }
};


/**
 * optional MsgCode msg_code = 1;
 * @return {!proto.MsgCode}
 */
proto.GetTradeHistoryResponse.prototype.getMsgCode = function() {
  return /** @type {!proto.MsgCode} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.MsgCode} value
 * @return {!proto.GetTradeHistoryResponse} returns this
 */
proto.GetTradeHistoryResponse.prototype.setMsgCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional string msg_text = 2;
 * @return {string}
 */
proto.GetTradeHistoryResponse.prototype.getMsgText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.GetTradeHistoryResponse} returns this
 */
proto.GetTradeHistoryResponse.prototype.setMsgText = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * repeated Trade trade = 3;
 * @return {!Array<!proto.Trade>}
 */
proto.GetTradeHistoryResponse.prototype.getTradeList = function() {
  return /** @type{!Array<!proto.Trade>} */ (
    jspb.Message.getRepeatedWrapperField(this, trading_model_pb.Trade, 3));
};


/**
 * @param {!Array<!proto.Trade>} value
 * @return {!proto.GetTradeHistoryResponse} returns this
*/
proto.GetTradeHistoryResponse.prototype.setTradeList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.Trade=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Trade}
 */
proto.GetTradeHistoryResponse.prototype.addTrade = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.Trade, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.GetTradeHistoryResponse} returns this
 */
proto.GetTradeHistoryResponse.prototype.clearTradeList = function() {
  return this.setTradeList([]);
};


/**
 * optional int64 account_id = 4;
 * @return {number}
 */
proto.GetTradeHistoryResponse.prototype.getAccountId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.GetTradeHistoryResponse} returns this
 */
proto.GetTradeHistoryResponse.prototype.setAccountId = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.GetOrderHistoryRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.GetOrderHistoryRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.GetOrderHistoryRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GetOrderHistoryRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    symbolCode: jspb.Message.getFieldWithDefault(msg, 1, ""),
    orderType: jspb.Message.getFieldWithDefault(msg, 2, 0),
    fromDatetime: jspb.Message.getFieldWithDefault(msg, 3, 0),
    toDatetime: jspb.Message.getFieldWithDefault(msg, 4, 0),
    orderState: jspb.Message.getFieldWithDefault(msg, 5, 0),
    accountId: jspb.Message.getFieldWithDefault(msg, 6, 0),
    side: jspb.Message.getFieldWithDefault(msg, 7, 0),
    psplAccountNo: jspb.Message.getFieldWithDefault(msg, 8, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.GetOrderHistoryRequest}
 */
proto.GetOrderHistoryRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.GetOrderHistoryRequest;
  return proto.GetOrderHistoryRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.GetOrderHistoryRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.GetOrderHistoryRequest}
 */
proto.GetOrderHistoryRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSymbolCode(value);
      break;
    case 2:
      var value = /** @type {!proto.OrderType} */ (reader.readEnum());
      msg.setOrderType(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setFromDatetime(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setToDatetime(value);
      break;
    case 5:
      var value = /** @type {!proto.OrderState} */ (reader.readEnum());
      msg.setOrderState(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setAccountId(value);
      break;
    case 7:
      var value = /** @type {!proto.Side} */ (reader.readEnum());
      msg.setSide(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setPsplAccountNo(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.GetOrderHistoryRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.GetOrderHistoryRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.GetOrderHistoryRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GetOrderHistoryRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSymbolCode();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getOrderType();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
  f = message.getFromDatetime();
  if (f !== 0) {
    writer.writeInt64(
      3,
      f
    );
  }
  f = message.getToDatetime();
  if (f !== 0) {
    writer.writeInt64(
      4,
      f
    );
  }
  f = message.getOrderState();
  if (f !== 0.0) {
    writer.writeEnum(
      5,
      f
    );
  }
  f = message.getAccountId();
  if (f !== 0) {
    writer.writeInt64(
      6,
      f
    );
  }
  f = message.getSide();
  if (f !== 0.0) {
    writer.writeEnum(
      7,
      f
    );
  }
  f = message.getPsplAccountNo();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
};


/**
 * optional string symbol_code = 1;
 * @return {string}
 */
proto.GetOrderHistoryRequest.prototype.getSymbolCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.GetOrderHistoryRequest} returns this
 */
proto.GetOrderHistoryRequest.prototype.setSymbolCode = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional OrderType order_type = 2;
 * @return {!proto.OrderType}
 */
proto.GetOrderHistoryRequest.prototype.getOrderType = function() {
  return /** @type {!proto.OrderType} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.OrderType} value
 * @return {!proto.GetOrderHistoryRequest} returns this
 */
proto.GetOrderHistoryRequest.prototype.setOrderType = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};


/**
 * optional int64 from_datetime = 3;
 * @return {number}
 */
proto.GetOrderHistoryRequest.prototype.getFromDatetime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.GetOrderHistoryRequest} returns this
 */
proto.GetOrderHistoryRequest.prototype.setFromDatetime = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional int64 to_datetime = 4;
 * @return {number}
 */
proto.GetOrderHistoryRequest.prototype.getToDatetime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.GetOrderHistoryRequest} returns this
 */
proto.GetOrderHistoryRequest.prototype.setToDatetime = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional OrderState order_state = 5;
 * @return {!proto.OrderState}
 */
proto.GetOrderHistoryRequest.prototype.getOrderState = function() {
  return /** @type {!proto.OrderState} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {!proto.OrderState} value
 * @return {!proto.GetOrderHistoryRequest} returns this
 */
proto.GetOrderHistoryRequest.prototype.setOrderState = function(value) {
  return jspb.Message.setProto3EnumField(this, 5, value);
};


/**
 * optional int64 account_id = 6;
 * @return {number}
 */
proto.GetOrderHistoryRequest.prototype.getAccountId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.GetOrderHistoryRequest} returns this
 */
proto.GetOrderHistoryRequest.prototype.setAccountId = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional Side side = 7;
 * @return {!proto.Side}
 */
proto.GetOrderHistoryRequest.prototype.getSide = function() {
  return /** @type {!proto.Side} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {!proto.Side} value
 * @return {!proto.GetOrderHistoryRequest} returns this
 */
proto.GetOrderHistoryRequest.prototype.setSide = function(value) {
  return jspb.Message.setProto3EnumField(this, 7, value);
};


/**
 * optional string pspl_account_no = 8;
 * @return {string}
 */
proto.GetOrderHistoryRequest.prototype.getPsplAccountNo = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/**
 * @param {string} value
 * @return {!proto.GetOrderHistoryRequest} returns this
 */
proto.GetOrderHistoryRequest.prototype.setPsplAccountNo = function(value) {
  return jspb.Message.setProto3StringField(this, 8, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.GetOrderHistoryResponse.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.GetOrderHistoryResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.GetOrderHistoryResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.GetOrderHistoryResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GetOrderHistoryResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    msgCode: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msgText: jspb.Message.getFieldWithDefault(msg, 2, ""),
    orderList: jspb.Message.toObjectList(msg.getOrderList(),
    trading_model_pb.Order.toObject, includeInstance),
    accountId: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.GetOrderHistoryResponse}
 */
proto.GetOrderHistoryResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.GetOrderHistoryResponse;
  return proto.GetOrderHistoryResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.GetOrderHistoryResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.GetOrderHistoryResponse}
 */
proto.GetOrderHistoryResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.MsgCode} */ (reader.readEnum());
      msg.setMsgCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsgText(value);
      break;
    case 3:
      var value = new trading_model_pb.Order;
      reader.readMessage(value,trading_model_pb.Order.deserializeBinaryFromReader);
      msg.addOrder(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setAccountId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.GetOrderHistoryResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.GetOrderHistoryResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.GetOrderHistoryResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GetOrderHistoryResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMsgCode();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getMsgText();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getOrderList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      trading_model_pb.Order.serializeBinaryToWriter
    );
  }
  f = message.getAccountId();
  if (f !== 0) {
    writer.writeInt64(
      4,
      f
    );
  }
};


/**
 * optional MsgCode msg_code = 1;
 * @return {!proto.MsgCode}
 */
proto.GetOrderHistoryResponse.prototype.getMsgCode = function() {
  return /** @type {!proto.MsgCode} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.MsgCode} value
 * @return {!proto.GetOrderHistoryResponse} returns this
 */
proto.GetOrderHistoryResponse.prototype.setMsgCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional string msg_text = 2;
 * @return {string}
 */
proto.GetOrderHistoryResponse.prototype.getMsgText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.GetOrderHistoryResponse} returns this
 */
proto.GetOrderHistoryResponse.prototype.setMsgText = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * repeated Order order = 3;
 * @return {!Array<!proto.Order>}
 */
proto.GetOrderHistoryResponse.prototype.getOrderList = function() {
  return /** @type{!Array<!proto.Order>} */ (
    jspb.Message.getRepeatedWrapperField(this, trading_model_pb.Order, 3));
};


/**
 * @param {!Array<!proto.Order>} value
 * @return {!proto.GetOrderHistoryResponse} returns this
*/
proto.GetOrderHistoryResponse.prototype.setOrderList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.Order=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Order}
 */
proto.GetOrderHistoryResponse.prototype.addOrder = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.Order, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.GetOrderHistoryResponse} returns this
 */
proto.GetOrderHistoryResponse.prototype.clearOrderList = function() {
  return this.setOrderList([]);
};


/**
 * optional int64 account_id = 4;
 * @return {number}
 */
proto.GetOrderHistoryResponse.prototype.getAccountId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.GetOrderHistoryResponse} returns this
 */
proto.GetOrderHistoryResponse.prototype.setAccountId = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.SymbolUpdateRequest.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.SymbolUpdateRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.SymbolUpdateRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.SymbolUpdateRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.SymbolUpdateRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    symbolsList: jspb.Message.toObjectList(msg.getSymbolsList(),
    query_model_pb.Symbol.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.SymbolUpdateRequest}
 */
proto.SymbolUpdateRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.SymbolUpdateRequest;
  return proto.SymbolUpdateRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.SymbolUpdateRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.SymbolUpdateRequest}
 */
proto.SymbolUpdateRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new query_model_pb.Symbol;
      reader.readMessage(value,query_model_pb.Symbol.deserializeBinaryFromReader);
      msg.addSymbols(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.SymbolUpdateRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.SymbolUpdateRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.SymbolUpdateRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.SymbolUpdateRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSymbolsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      query_model_pb.Symbol.serializeBinaryToWriter
    );
  }
};


/**
 * repeated Symbol symbols = 1;
 * @return {!Array<!proto.Symbol>}
 */
proto.SymbolUpdateRequest.prototype.getSymbolsList = function() {
  return /** @type{!Array<!proto.Symbol>} */ (
    jspb.Message.getRepeatedWrapperField(this, query_model_pb.Symbol, 1));
};


/**
 * @param {!Array<!proto.Symbol>} value
 * @return {!proto.SymbolUpdateRequest} returns this
*/
proto.SymbolUpdateRequest.prototype.setSymbolsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.Symbol=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Symbol}
 */
proto.SymbolUpdateRequest.prototype.addSymbols = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.Symbol, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.SymbolUpdateRequest} returns this
 */
proto.SymbolUpdateRequest.prototype.clearSymbolsList = function() {
  return this.setSymbolsList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.SymbolUpdateResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.SymbolUpdateResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.SymbolUpdateResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.SymbolUpdateResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    msgCode: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msgText: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.SymbolUpdateResponse}
 */
proto.SymbolUpdateResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.SymbolUpdateResponse;
  return proto.SymbolUpdateResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.SymbolUpdateResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.SymbolUpdateResponse}
 */
proto.SymbolUpdateResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.MsgCode} */ (reader.readEnum());
      msg.setMsgCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsgText(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.SymbolUpdateResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.SymbolUpdateResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.SymbolUpdateResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.SymbolUpdateResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMsgCode();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getMsgText();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional MsgCode msg_code = 1;
 * @return {!proto.MsgCode}
 */
proto.SymbolUpdateResponse.prototype.getMsgCode = function() {
  return /** @type {!proto.MsgCode} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.MsgCode} value
 * @return {!proto.SymbolUpdateResponse} returns this
 */
proto.SymbolUpdateResponse.prototype.setMsgCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional string msg_text = 2;
 * @return {string}
 */
proto.SymbolUpdateResponse.prototype.getMsgText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.SymbolUpdateResponse} returns this
 */
proto.SymbolUpdateResponse.prototype.setMsgText = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.GetOrderStatusRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.GetOrderStatusRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.GetOrderStatusRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GetOrderStatusRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    accountId: jspb.Message.getFieldWithDefault(msg, 1, 0),
    symbolCode: jspb.Message.getFieldWithDefault(msg, 2, ""),
    orderId: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.GetOrderStatusRequest}
 */
proto.GetOrderStatusRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.GetOrderStatusRequest;
  return proto.GetOrderStatusRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.GetOrderStatusRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.GetOrderStatusRequest}
 */
proto.GetOrderStatusRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setAccountId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSymbolCode(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setOrderId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.GetOrderStatusRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.GetOrderStatusRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.GetOrderStatusRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GetOrderStatusRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAccountId();
  if (f !== 0) {
    writer.writeInt64(
      1,
      f
    );
  }
  f = message.getSymbolCode();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getOrderId();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional int64 account_id = 1;
 * @return {number}
 */
proto.GetOrderStatusRequest.prototype.getAccountId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.GetOrderStatusRequest} returns this
 */
proto.GetOrderStatusRequest.prototype.setAccountId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string symbol_code = 2;
 * @return {string}
 */
proto.GetOrderStatusRequest.prototype.getSymbolCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.GetOrderStatusRequest} returns this
 */
proto.GetOrderStatusRequest.prototype.setSymbolCode = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string order_id = 3;
 * @return {string}
 */
proto.GetOrderStatusRequest.prototype.getOrderId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.GetOrderStatusRequest} returns this
 */
proto.GetOrderStatusRequest.prototype.setOrderId = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.GetOrderStatusResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.GetOrderStatusResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.GetOrderStatusResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GetOrderStatusResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    msgCode: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msgText: jspb.Message.getFieldWithDefault(msg, 2, ""),
    order: (f = msg.getOrder()) && trading_model_pb.Order.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.GetOrderStatusResponse}
 */
proto.GetOrderStatusResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.GetOrderStatusResponse;
  return proto.GetOrderStatusResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.GetOrderStatusResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.GetOrderStatusResponse}
 */
proto.GetOrderStatusResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.MsgCode} */ (reader.readEnum());
      msg.setMsgCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsgText(value);
      break;
    case 3:
      var value = new trading_model_pb.Order;
      reader.readMessage(value,trading_model_pb.Order.deserializeBinaryFromReader);
      msg.setOrder(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.GetOrderStatusResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.GetOrderStatusResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.GetOrderStatusResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GetOrderStatusResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMsgCode();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getMsgText();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getOrder();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      trading_model_pb.Order.serializeBinaryToWriter
    );
  }
};


/**
 * optional MsgCode msg_code = 1;
 * @return {!proto.MsgCode}
 */
proto.GetOrderStatusResponse.prototype.getMsgCode = function() {
  return /** @type {!proto.MsgCode} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.MsgCode} value
 * @return {!proto.GetOrderStatusResponse} returns this
 */
proto.GetOrderStatusResponse.prototype.setMsgCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional string msg_text = 2;
 * @return {string}
 */
proto.GetOrderStatusResponse.prototype.getMsgText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.GetOrderStatusResponse} returns this
 */
proto.GetOrderStatusResponse.prototype.setMsgText = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional Order order = 3;
 * @return {?proto.Order}
 */
proto.GetOrderStatusResponse.prototype.getOrder = function() {
  return /** @type{?proto.Order} */ (
    jspb.Message.getWrapperField(this, trading_model_pb.Order, 3));
};


/**
 * @param {?proto.Order|undefined} value
 * @return {!proto.GetOrderStatusResponse} returns this
*/
proto.GetOrderStatusResponse.prototype.setOrder = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.GetOrderStatusResponse} returns this
 */
proto.GetOrderStatusResponse.prototype.clearOrder = function() {
  return this.setOrder(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.GetOrderStatusResponse.prototype.hasOrder = function() {
  return jspb.Message.getField(this, 3) != null;
};


goog.object.extend(exports, proto);
