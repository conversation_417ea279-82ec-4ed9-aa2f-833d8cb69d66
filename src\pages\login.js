import { useState } from 'react';
import { API_LOGIN } from '../constants/api.constant';
import axios from 'axios';
import { LocalStorageKey } from '../model/constant';
import jwt_decode from 'jwt-decode';
import NotifyFirstLogin from '../components/notify-firstlogin';
import { wsService } from '../services/websocket-service';

const Login = () => {
    const [accountId, setAccountId] = useState('');
    const [password, setPassword] = useState('');
    const [isMessErr, setIsMessErr] = useState(false);
    const [isShowNotiFirstLogin, setIsShowNotiFirstLogin] = useState(false);
    const api_url = window.apiUrl;

    const btnDisabled = () => {
        const isInvalidAccountId = accountId.trim() === '';
        const isInvalidPassword = password.trim() === '';
        return isInvalidAccountId || isInvalidPassword;
    }
    const handleKeyUp = (event) => {
        if (event.key === 'Enter' && btnDisabled() !== true) {
            btnLogin();
        }
    }
    const btnLogin = () => {
        setIsMessErr(false);
        const url = `${api_url}${API_LOGIN}`;
        const param = {
            poem_id: accountId.trim(),
            password: password.trim(),
            account_type: 'retail'
        }

        return axios.post(url, param).then((resp) => {
            if (resp?.data?.meta?.code === 200) {
                const token = resp?.data?.data
                if (token) {
                    sessionStorage.setItem(LocalStorageKey.TOKEN, token);
                    wsService.startWebsocket()
                    const decoded = jwt_decode(resp?.data?.data);
                    if (decoded) {
                        sessionStorage.setItem(LocalStorageKey.ACCOUNT_ID, decoded.account_id);
                        sessionStorage.setItem(LocalStorageKey.POEM_ID, decoded.poem_id);
                        sessionStorage.setItem(LocalStorageKey.PWD_REQ_SUBMIT, decoded?.pwd_req_submit);
                        sessionStorage.setItem(LocalStorageKey.PWD_REQ_AMEND, decoded?.pwd_req_amend);
                        sessionStorage.setItem(LocalStorageKey.PWD_REQ_WITHDRAW, decoded?.pwd_req_withdraw);
                        sessionStorage.setItem(LocalStorageKey.TOKEN_EXPIRED, decoded?.exp);
                        localStorage.setItem(LocalStorageKey.MIN_ORDER_VALUE, decoded?.min_order_value);
                        localStorage.setItem(LocalStorageKey.ACK_FLAG, decoded?.ack_flag);
                        localStorage.setItem(LocalStorageKey.MAX_ORDER_VOLUME, decoded?.max_order_volume);
                        localStorage.setItem(LocalStorageKey.MAX_ORDER_VALUE, decoded?.max_order_value);
                        sessionStorage.setItem(LocalStorageKey.THEME, decoded?.theme);
                        sessionStorage.setItem(LocalStorageKey.DETECT_SIDE_NOT_EMBED, true);
                    }
                    sessionStorage.setItem(LocalStorageKey.ACCOUNT_TYPE, 'retail');
                    window.location.href = process.env.PUBLIC_URL ? process.env.PUBLIC_URL : "/";
                }
            }
        },
        (error) => {
            setIsMessErr(true);
        });
    }
    return<>
    <div className="h-full page login" onKeyUp={handleKeyUp}>
            <div className="h-full site-main d-flex align-items-center">
                <div className="container">
                    <div className="row justify-content-center">
                        <div className="col-lg-4 margin-top-12">
                            <h3 className="text-center text-primary mb-3">
                                <img src="img/logo.svg" style={{ maxHeight: "7rem" }} alt="Poems" className="site-logo logo-light" />
                            </h3>
                            <div className="card card-login shadow">
                                <div className="card-body">
                                    <h4 className="text-primary-custom">Login</h4>
                                    <div className="mb-3">
                                        <label className="d-block mb-1 text-secondary">AccountNo</label>
                                        <div className="input-group">
                                            <input
                                                onChange={(e) => setAccountId(e.target.value)}
                                                type="text"
                                                className="form-control border-end-0" />
                                            <span className="input-group-text bg-transparent"><i className="bi bi-person-fill opacity-50"></i></span>
                                        </div>
                                    </div>
                                    <div className="mb-3">
                                        <label className="d-block mb-1 text-secondary">Password</label>
                                        <div className="input-group">
                                            <input
                                                onChange={(e) => setPassword(e.target.value)}
                                                type="password" name="password"
                                                className="form-control border-end-0"
                                                
                                            />
                                            <span className="input-group-text bg-transparent"><i className="bi bi-lock-fill opacity-50"></i></span>
                                        </div>
                                    </div>
                                    {isMessErr && <div className='mb-3'>
                                        <label className="d-block mb-1 text-danger">Login ID and Password is NOT matching</label>
                                    </div>}
                                    <div className="mb-3">
                                        <div className="form-check">
                                            <input className="form-check-input" type="checkbox" name="remember" id="remember"
                                            />
                                            <label className="form-check-label" htmlFor="remember">
                                                Remember me
                                            </label>
                                        </div>
                                    </div>
                                    <div className="mt-1">
                                        <button
                                            disabled={btnDisabled()}
                                            onClick={btnLogin}
                                            className="btn btn-primary pt-2 pb-2 text-white d-block text-uppercase btn-login mb-2 width-100-percent"><strong>Login</strong></button>
                                        <p className="text-center"><a href="#">Forgot Password</a></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>
    </>
}
export default Login