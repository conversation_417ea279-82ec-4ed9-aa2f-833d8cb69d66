{"header": {"dashbroad": "Dashboard", "accountManagement": "Account Management", "orderStatus": "Order Status", "announcement": "Announcement", "accountNo": "Account No:"}, "market": {"name": "Name", "symbol": "Symbol", "last": "Last", "chg": "Chg", "chgPercent": "Chg(%)", "bvol": "BVol", "bid": "Bid", "ask": "Ask", "svol": "SVol", "vol": "Vol", "open": "Open", "high": "High", "low": "Low", "preClose": "Prev. <PERSON>", "buy": "Buy", "sell": "<PERSON>ll"}, "order": {"quantity": "Quantity", "lotSize": "Lot Size", "qty": "Quantity", "paymentType": "Payment Type", "ccy": "<PERSON><PERSON><PERSON><PERSON>", "settlement": "Settlement", "cash": "Cash", "cashBalance": "Cash Balance", "maxPurchase": "Max Purchase", "sellLimit": "<PERSON><PERSON>", "buyLimit": "Buy Limit", "grossValue": "Gross Value", "indicativeGrossValue": "Indicative Gross Value", "grossValueDescription": "(*Market prices may change)", "placeBuyMarket": "Place buy market order", "placeBuyLimit": "Place buy limit order", "placeSellMarket": "Place sell market order", "placeSellLimit": "Place sell limit order", "confirm": "Order Confirmation", "type": "Order Type", "market": "Market Order", "limit": "Limit Order", "marketClosed": "Market closed", "settlementCcy": "Settlement CCY", "placed": "your order placed", "successfully": "Successfully!", "visitPage": "Please refer to the order status page in US [Asian Hrs] tab for checking of your order status.", "noteLimitOrder": "Kindly note that Limit order placed will not be shown in the Dashboard Bid and Ask quotes.", "confirmBtn": "Confirm", "errorValue": "Order volume is less than USD 20,000", "closeHoliday": "Market closed due to holiday", "confirmPassword": "Password Confirmation", "maxPurchaseWithMargin": "Max Purchase with <PERSON><PERSON>", "availableCash": "Available Cash", "availableCashWithoutMargin": "Available Cash Without Margin", "marketPrice": "Market Price (USD)", "product": "Product", "orderNo": "Order No", "name": "Name", "symbol": "Symbol", "orderStatus": "Order Status", "submitted": "Submitted", "action": "Action", "message": "Message", "submittedDate": "Submitted Date", "numberOfShares": "Number of shares", "dateTime": "Date/Time(R)", "currency": "<PERSON><PERSON><PERSON><PERSON>", "limitPrice": "Limit Price (USD)", "orderType": "Order Type", "totalExecuted": "Total Executed", "avgExecutedPrice": "Average Executed Price"}, "message": {"qtyInvalid": "Quantity must be a multiple of <PERSON> Size", "priceInvalid": "Order price is invalid", "orderFail": "Place order fail", "orderSuccess": "Place order success", "grossBuyInvalid": "Exceeded Buy Limit", "grossSellInvalid": "Exceeded <PERSON><PERSON>", "outOfDailyPriceLimits": "Out of daily price limits", "handleFail": "Handle request failed"}}