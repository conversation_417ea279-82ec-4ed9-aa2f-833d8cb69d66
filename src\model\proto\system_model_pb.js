// source: system_model.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = (function() {
  if (this) { return this; }
  if (typeof window !== 'undefined') { return window; }
  if (typeof global !== 'undefined') { return global; }
  if (typeof self !== 'undefined') { return self; }
  return Function('return this')();
}.call(null));

goog.exportSymbol('proto.Account', null, global);
goog.exportSymbol('proto.Account.BoolFlag', null, global);
goog.exportSymbol('proto.Account.TradingRights', null, global);
goog.exportSymbol('proto.AccountBalance', null, global);
goog.exportSymbol('proto.AccountPortfolio', null, global);
goog.exportSymbol('proto.AdminSettings', null, global);
goog.exportSymbol('proto.DealAction', null, global);
goog.exportSymbol('proto.GroupAccount', null, global);
goog.exportSymbol('proto.GroupType', null, global);
goog.exportSymbol('proto.Holiday', null, global);
goog.exportSymbol('proto.HolidayStatus', null, global);
goog.exportSymbol('proto.LoginInfo', null, global);
goog.exportSymbol('proto.MarketDataSource', null, global);
goog.exportSymbol('proto.MarketSettings', null, global);
goog.exportSymbol('proto.MarketStatus', null, global);
goog.exportSymbol('proto.MsgCode', null, global);
goog.exportSymbol('proto.SessionSettings', null, global);
goog.exportSymbol('proto.SessionStatusCode', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.LoginInfo = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.LoginInfo, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.LoginInfo.displayName = 'proto.LoginInfo';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.Account = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.Account.repeatedFields_, null);
};
goog.inherits(proto.Account, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.Account.displayName = 'proto.Account';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.AdminSettings = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.AdminSettings.repeatedFields_, null);
};
goog.inherits(proto.AdminSettings, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.AdminSettings.displayName = 'proto.AdminSettings';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.GroupAccount = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.GroupAccount, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.GroupAccount.displayName = 'proto.GroupAccount';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.SessionSettings = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.SessionSettings, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.SessionSettings.displayName = 'proto.SessionSettings';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.Holiday = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.Holiday, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.Holiday.displayName = 'proto.Holiday';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.AccountBalance = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.AccountBalance, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.AccountBalance.displayName = 'proto.AccountBalance';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.AccountPortfolio = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.AccountPortfolio, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.AccountPortfolio.displayName = 'proto.AccountPortfolio';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.MarketDataSource = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.MarketDataSource, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.MarketDataSource.displayName = 'proto.MarketDataSource';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.MarketSettings = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.MarketSettings.repeatedFields_, null);
};
goog.inherits(proto.MarketSettings, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.MarketSettings.displayName = 'proto.MarketSettings';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.LoginInfo.prototype.toObject = function(opt_includeInstance) {
  return proto.LoginInfo.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.LoginInfo} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.LoginInfo.toObject = function(includeInstance, msg) {
  var f, obj = {
    login: jspb.Message.getFieldWithDefault(msg, 1, ""),
    macAddress: jspb.Message.getFieldWithDefault(msg, 3, ""),
    localIp: jspb.Message.getFieldWithDefault(msg, 4, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.LoginInfo}
 */
proto.LoginInfo.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.LoginInfo;
  return proto.LoginInfo.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.LoginInfo} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.LoginInfo}
 */
proto.LoginInfo.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setLogin(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setMacAddress(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setLocalIp(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.LoginInfo.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.LoginInfo.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.LoginInfo} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.LoginInfo.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLogin();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getMacAddress();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getLocalIp();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
};


/**
 * optional string login = 1;
 * @return {string}
 */
proto.LoginInfo.prototype.getLogin = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.LoginInfo} returns this
 */
proto.LoginInfo.prototype.setLogin = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string mac_address = 3;
 * @return {string}
 */
proto.LoginInfo.prototype.getMacAddress = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.LoginInfo} returns this
 */
proto.LoginInfo.prototype.setMacAddress = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string local_ip = 4;
 * @return {string}
 */
proto.LoginInfo.prototype.getLocalIp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.LoginInfo} returns this
 */
proto.LoginInfo.prototype.setLocalIp = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.Account.repeatedFields_ = [19];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.Account.prototype.toObject = function(opt_includeInstance) {
  return proto.Account.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.Account} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Account.toObject = function(includeInstance, msg) {
  var f, obj = {
    accountId: jspb.Message.getFieldWithDefault(msg, 1, 0),
    name: jspb.Message.getFieldWithDefault(msg, 2, ""),
    groupId: jspb.Message.getFieldWithDefault(msg, 3, 0),
    enableFlg: jspb.Message.getBooleanFieldWithDefault(msg, 4, false),
    tradingRights: jspb.Message.getFieldWithDefault(msg, 5, 0),
    apiFlg: jspb.Message.getBooleanFieldWithDefault(msg, 6, false),
    registeredDate: jspb.Message.getFieldWithDefault(msg, 7, 0),
    comment: jspb.Message.getFieldWithDefault(msg, 8, ""),
    apiKey: jspb.Message.getFieldWithDefault(msg, 9, ""),
    password: jspb.Message.getFieldWithDefault(msg, 10, ""),
    secretKey: jspb.Message.getFieldWithDefault(msg, 11, ""),
    recvAdminNewsFlg: jspb.Message.getFieldWithDefault(msg, 12, 0),
    recvMatchNotiFlg: jspb.Message.getFieldWithDefault(msg, 13, 0),
    email: jspb.Message.getFieldWithDefault(msg, 14, ""),
    phone: jspb.Message.getFieldWithDefault(msg, 15, ""),
    enableSecretKeyFlg: jspb.Message.getFieldWithDefault(msg, 16, 0),
    numTrades: jspb.Message.getFieldWithDefault(msg, 17, 0),
    numPendingOrders: jspb.Message.getFieldWithDefault(msg, 18, 0),
    allowedMarketsList: (f = jspb.Message.getRepeatedField(msg, 19)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.Account}
 */
proto.Account.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.Account;
  return proto.Account.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.Account} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.Account}
 */
proto.Account.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setAccountId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setGroupId(value);
      break;
    case 4:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setEnableFlg(value);
      break;
    case 5:
      var value = /** @type {!proto.Account.TradingRights} */ (reader.readEnum());
      msg.setTradingRights(value);
      break;
    case 6:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setApiFlg(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setRegisteredDate(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setComment(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setApiKey(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setPassword(value);
      break;
    case 11:
      var value = /** @type {string} */ (reader.readString());
      msg.setSecretKey(value);
      break;
    case 12:
      var value = /** @type {!proto.Account.BoolFlag} */ (reader.readEnum());
      msg.setRecvAdminNewsFlg(value);
      break;
    case 13:
      var value = /** @type {!proto.Account.BoolFlag} */ (reader.readEnum());
      msg.setRecvMatchNotiFlg(value);
      break;
    case 14:
      var value = /** @type {string} */ (reader.readString());
      msg.setEmail(value);
      break;
    case 15:
      var value = /** @type {string} */ (reader.readString());
      msg.setPhone(value);
      break;
    case 16:
      var value = /** @type {!proto.Account.BoolFlag} */ (reader.readEnum());
      msg.setEnableSecretKeyFlg(value);
      break;
    case 17:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setNumTrades(value);
      break;
    case 18:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setNumPendingOrders(value);
      break;
    case 19:
      var value = /** @type {string} */ (reader.readString());
      msg.addAllowedMarkets(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.Account.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.Account.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.Account} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Account.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAccountId();
  if (f !== 0) {
    writer.writeInt64(
      1,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getGroupId();
  if (f !== 0) {
    writer.writeInt64(
      3,
      f
    );
  }
  f = message.getEnableFlg();
  if (f) {
    writer.writeBool(
      4,
      f
    );
  }
  f = message.getTradingRights();
  if (f !== 0.0) {
    writer.writeEnum(
      5,
      f
    );
  }
  f = message.getApiFlg();
  if (f) {
    writer.writeBool(
      6,
      f
    );
  }
  f = message.getRegisteredDate();
  if (f !== 0) {
    writer.writeInt64(
      7,
      f
    );
  }
  f = message.getComment();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getApiKey();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getPassword();
  if (f.length > 0) {
    writer.writeString(
      10,
      f
    );
  }
  f = message.getSecretKey();
  if (f.length > 0) {
    writer.writeString(
      11,
      f
    );
  }
  f = message.getRecvAdminNewsFlg();
  if (f !== 0.0) {
    writer.writeEnum(
      12,
      f
    );
  }
  f = message.getRecvMatchNotiFlg();
  if (f !== 0.0) {
    writer.writeEnum(
      13,
      f
    );
  }
  f = message.getEmail();
  if (f.length > 0) {
    writer.writeString(
      14,
      f
    );
  }
  f = message.getPhone();
  if (f.length > 0) {
    writer.writeString(
      15,
      f
    );
  }
  f = message.getEnableSecretKeyFlg();
  if (f !== 0.0) {
    writer.writeEnum(
      16,
      f
    );
  }
  f = message.getNumTrades();
  if (f !== 0) {
    writer.writeInt64(
      17,
      f
    );
  }
  f = message.getNumPendingOrders();
  if (f !== 0) {
    writer.writeInt64(
      18,
      f
    );
  }
  f = message.getAllowedMarketsList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      19,
      f
    );
  }
};


/**
 * @enum {number}
 */
proto.Account.BoolFlag = {
  BOOL_FLAG_NONE: 0,
  BOOL_FLAG_ON: 1,
  BOOL_FLAG_OFF: 2
};

/**
 * @enum {number}
 */
proto.Account.TradingRights = {
  TRADING_RIGHTS_NONE: 0,
  TRADING_OPEN_ONLY: 1,
  TRADING_CLOSE_ONLY: 2,
  TRADING_NORMAL: 3
};

/**
 * optional int64 account_id = 1;
 * @return {number}
 */
proto.Account.prototype.getAccountId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.Account} returns this
 */
proto.Account.prototype.setAccountId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string name = 2;
 * @return {string}
 */
proto.Account.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.Account} returns this
 */
proto.Account.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int64 group_id = 3;
 * @return {number}
 */
proto.Account.prototype.getGroupId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.Account} returns this
 */
proto.Account.prototype.setGroupId = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional bool enable_flg = 4;
 * @return {boolean}
 */
proto.Account.prototype.getEnableFlg = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 4, false));
};


/**
 * @param {boolean} value
 * @return {!proto.Account} returns this
 */
proto.Account.prototype.setEnableFlg = function(value) {
  return jspb.Message.setProto3BooleanField(this, 4, value);
};


/**
 * optional TradingRights trading_rights = 5;
 * @return {!proto.Account.TradingRights}
 */
proto.Account.prototype.getTradingRights = function() {
  return /** @type {!proto.Account.TradingRights} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {!proto.Account.TradingRights} value
 * @return {!proto.Account} returns this
 */
proto.Account.prototype.setTradingRights = function(value) {
  return jspb.Message.setProto3EnumField(this, 5, value);
};


/**
 * optional bool api_flg = 6;
 * @return {boolean}
 */
proto.Account.prototype.getApiFlg = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 6, false));
};


/**
 * @param {boolean} value
 * @return {!proto.Account} returns this
 */
proto.Account.prototype.setApiFlg = function(value) {
  return jspb.Message.setProto3BooleanField(this, 6, value);
};


/**
 * optional int64 registered_date = 7;
 * @return {number}
 */
proto.Account.prototype.getRegisteredDate = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {number} value
 * @return {!proto.Account} returns this
 */
proto.Account.prototype.setRegisteredDate = function(value) {
  return jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional string comment = 8;
 * @return {string}
 */
proto.Account.prototype.getComment = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/**
 * @param {string} value
 * @return {!proto.Account} returns this
 */
proto.Account.prototype.setComment = function(value) {
  return jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional string api_key = 9;
 * @return {string}
 */
proto.Account.prototype.getApiKey = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/**
 * @param {string} value
 * @return {!proto.Account} returns this
 */
proto.Account.prototype.setApiKey = function(value) {
  return jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional string password = 10;
 * @return {string}
 */
proto.Account.prototype.getPassword = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/**
 * @param {string} value
 * @return {!proto.Account} returns this
 */
proto.Account.prototype.setPassword = function(value) {
  return jspb.Message.setProto3StringField(this, 10, value);
};


/**
 * optional string secret_key = 11;
 * @return {string}
 */
proto.Account.prototype.getSecretKey = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};


/**
 * @param {string} value
 * @return {!proto.Account} returns this
 */
proto.Account.prototype.setSecretKey = function(value) {
  return jspb.Message.setProto3StringField(this, 11, value);
};


/**
 * optional BoolFlag recv_admin_news_flg = 12;
 * @return {!proto.Account.BoolFlag}
 */
proto.Account.prototype.getRecvAdminNewsFlg = function() {
  return /** @type {!proto.Account.BoolFlag} */ (jspb.Message.getFieldWithDefault(this, 12, 0));
};


/**
 * @param {!proto.Account.BoolFlag} value
 * @return {!proto.Account} returns this
 */
proto.Account.prototype.setRecvAdminNewsFlg = function(value) {
  return jspb.Message.setProto3EnumField(this, 12, value);
};


/**
 * optional BoolFlag recv_match_noti_flg = 13;
 * @return {!proto.Account.BoolFlag}
 */
proto.Account.prototype.getRecvMatchNotiFlg = function() {
  return /** @type {!proto.Account.BoolFlag} */ (jspb.Message.getFieldWithDefault(this, 13, 0));
};


/**
 * @param {!proto.Account.BoolFlag} value
 * @return {!proto.Account} returns this
 */
proto.Account.prototype.setRecvMatchNotiFlg = function(value) {
  return jspb.Message.setProto3EnumField(this, 13, value);
};


/**
 * optional string email = 14;
 * @return {string}
 */
proto.Account.prototype.getEmail = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 14, ""));
};


/**
 * @param {string} value
 * @return {!proto.Account} returns this
 */
proto.Account.prototype.setEmail = function(value) {
  return jspb.Message.setProto3StringField(this, 14, value);
};


/**
 * optional string phone = 15;
 * @return {string}
 */
proto.Account.prototype.getPhone = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 15, ""));
};


/**
 * @param {string} value
 * @return {!proto.Account} returns this
 */
proto.Account.prototype.setPhone = function(value) {
  return jspb.Message.setProto3StringField(this, 15, value);
};


/**
 * optional BoolFlag enable_secret_key_flg = 16;
 * @return {!proto.Account.BoolFlag}
 */
proto.Account.prototype.getEnableSecretKeyFlg = function() {
  return /** @type {!proto.Account.BoolFlag} */ (jspb.Message.getFieldWithDefault(this, 16, 0));
};


/**
 * @param {!proto.Account.BoolFlag} value
 * @return {!proto.Account} returns this
 */
proto.Account.prototype.setEnableSecretKeyFlg = function(value) {
  return jspb.Message.setProto3EnumField(this, 16, value);
};


/**
 * optional int64 num_trades = 17;
 * @return {number}
 */
proto.Account.prototype.getNumTrades = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 17, 0));
};


/**
 * @param {number} value
 * @return {!proto.Account} returns this
 */
proto.Account.prototype.setNumTrades = function(value) {
  return jspb.Message.setProto3IntField(this, 17, value);
};


/**
 * optional int64 num_pending_orders = 18;
 * @return {number}
 */
proto.Account.prototype.getNumPendingOrders = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 18, 0));
};


/**
 * @param {number} value
 * @return {!proto.Account} returns this
 */
proto.Account.prototype.setNumPendingOrders = function(value) {
  return jspb.Message.setProto3IntField(this, 18, value);
};


/**
 * repeated string allowed_markets = 19;
 * @return {!Array<string>}
 */
proto.Account.prototype.getAllowedMarketsList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 19));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.Account} returns this
 */
proto.Account.prototype.setAllowedMarketsList = function(value) {
  return jspb.Message.setField(this, 19, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.Account} returns this
 */
proto.Account.prototype.addAllowedMarkets = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 19, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.Account} returns this
 */
proto.Account.prototype.clearAllowedMarketsList = function() {
  return this.setAllowedMarketsList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.AdminSettings.repeatedFields_ = [3,4];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.AdminSettings.prototype.toObject = function(opt_includeInstance) {
  return proto.AdminSettings.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.AdminSettings} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.AdminSettings.toObject = function(includeInstance, msg) {
  var f, obj = {
    grossBuyLimit: jspb.Message.getFieldWithDefault(msg, 1, 0),
    grossSellLimit: jspb.Message.getFieldWithDefault(msg, 2, 0),
    sessionSettingsList: jspb.Message.toObjectList(msg.getSessionSettingsList(),
    proto.SessionSettings.toObject, includeInstance),
    holidaySettingsList: jspb.Message.toObjectList(msg.getHolidaySettingsList(),
    proto.Holiday.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.AdminSettings}
 */
proto.AdminSettings.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.AdminSettings;
  return proto.AdminSettings.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.AdminSettings} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.AdminSettings}
 */
proto.AdminSettings.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setGrossBuyLimit(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setGrossSellLimit(value);
      break;
    case 3:
      var value = new proto.SessionSettings;
      reader.readMessage(value,proto.SessionSettings.deserializeBinaryFromReader);
      msg.addSessionSettings(value);
      break;
    case 4:
      var value = new proto.Holiday;
      reader.readMessage(value,proto.Holiday.deserializeBinaryFromReader);
      msg.addHolidaySettings(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.AdminSettings.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.AdminSettings.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.AdminSettings} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.AdminSettings.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getGrossBuyLimit();
  if (f !== 0) {
    writer.writeInt64(
      1,
      f
    );
  }
  f = message.getGrossSellLimit();
  if (f !== 0) {
    writer.writeInt64(
      2,
      f
    );
  }
  f = message.getSessionSettingsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      proto.SessionSettings.serializeBinaryToWriter
    );
  }
  f = message.getHolidaySettingsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      4,
      f,
      proto.Holiday.serializeBinaryToWriter
    );
  }
};


/**
 * optional int64 gross_buy_limit = 1;
 * @return {number}
 */
proto.AdminSettings.prototype.getGrossBuyLimit = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.AdminSettings} returns this
 */
proto.AdminSettings.prototype.setGrossBuyLimit = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional int64 gross_sell_limit = 2;
 * @return {number}
 */
proto.AdminSettings.prototype.getGrossSellLimit = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.AdminSettings} returns this
 */
proto.AdminSettings.prototype.setGrossSellLimit = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * repeated SessionSettings session_settings = 3;
 * @return {!Array<!proto.SessionSettings>}
 */
proto.AdminSettings.prototype.getSessionSettingsList = function() {
  return /** @type{!Array<!proto.SessionSettings>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.SessionSettings, 3));
};


/**
 * @param {!Array<!proto.SessionSettings>} value
 * @return {!proto.AdminSettings} returns this
*/
proto.AdminSettings.prototype.setSessionSettingsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.SessionSettings=} opt_value
 * @param {number=} opt_index
 * @return {!proto.SessionSettings}
 */
proto.AdminSettings.prototype.addSessionSettings = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.SessionSettings, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.AdminSettings} returns this
 */
proto.AdminSettings.prototype.clearSessionSettingsList = function() {
  return this.setSessionSettingsList([]);
};


/**
 * repeated Holiday holiday_settings = 4;
 * @return {!Array<!proto.Holiday>}
 */
proto.AdminSettings.prototype.getHolidaySettingsList = function() {
  return /** @type{!Array<!proto.Holiday>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.Holiday, 4));
};


/**
 * @param {!Array<!proto.Holiday>} value
 * @return {!proto.AdminSettings} returns this
*/
proto.AdminSettings.prototype.setHolidaySettingsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 4, value);
};


/**
 * @param {!proto.Holiday=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Holiday}
 */
proto.AdminSettings.prototype.addHolidaySettings = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 4, opt_value, proto.Holiday, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.AdminSettings} returns this
 */
proto.AdminSettings.prototype.clearHolidaySettingsList = function() {
  return this.setHolidaySettingsList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.GroupAccount.prototype.toObject = function(opt_includeInstance) {
  return proto.GroupAccount.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.GroupAccount} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GroupAccount.toObject = function(includeInstance, msg) {
  var f, obj = {
    groupId: jspb.Message.getFieldWithDefault(msg, 1, 0),
    groupName: jspb.Message.getFieldWithDefault(msg, 2, ""),
    groupType: jspb.Message.getFieldWithDefault(msg, 3, 0),
    maxOrderVolume: jspb.Message.getFieldWithDefault(msg, 4, 0),
    minOrderValue: jspb.Message.getFieldWithDefault(msg, 5, ""),
    maxOrderValue: jspb.Message.getFieldWithDefault(msg, 6, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.GroupAccount}
 */
proto.GroupAccount.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.GroupAccount;
  return proto.GroupAccount.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.GroupAccount} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.GroupAccount}
 */
proto.GroupAccount.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setGroupId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setGroupName(value);
      break;
    case 3:
      var value = /** @type {!proto.GroupType} */ (reader.readEnum());
      msg.setGroupType(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setMaxOrderVolume(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setMinOrderValue(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setMaxOrderValue(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.GroupAccount.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.GroupAccount.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.GroupAccount} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.GroupAccount.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getGroupId();
  if (f !== 0) {
    writer.writeInt64(
      1,
      f
    );
  }
  f = message.getGroupName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getGroupType();
  if (f !== 0.0) {
    writer.writeEnum(
      3,
      f
    );
  }
  f = message.getMaxOrderVolume();
  if (f !== 0) {
    writer.writeInt64(
      4,
      f
    );
  }
  f = message.getMinOrderValue();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getMaxOrderValue();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
};


/**
 * optional int64 group_id = 1;
 * @return {number}
 */
proto.GroupAccount.prototype.getGroupId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.GroupAccount} returns this
 */
proto.GroupAccount.prototype.setGroupId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string group_name = 2;
 * @return {string}
 */
proto.GroupAccount.prototype.getGroupName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.GroupAccount} returns this
 */
proto.GroupAccount.prototype.setGroupName = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional GroupType group_type = 3;
 * @return {!proto.GroupType}
 */
proto.GroupAccount.prototype.getGroupType = function() {
  return /** @type {!proto.GroupType} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {!proto.GroupType} value
 * @return {!proto.GroupAccount} returns this
 */
proto.GroupAccount.prototype.setGroupType = function(value) {
  return jspb.Message.setProto3EnumField(this, 3, value);
};


/**
 * optional int64 max_order_volume = 4;
 * @return {number}
 */
proto.GroupAccount.prototype.getMaxOrderVolume = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.GroupAccount} returns this
 */
proto.GroupAccount.prototype.setMaxOrderVolume = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional string min_order_value = 5;
 * @return {string}
 */
proto.GroupAccount.prototype.getMinOrderValue = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.GroupAccount} returns this
 */
proto.GroupAccount.prototype.setMinOrderValue = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string max_order_value = 6;
 * @return {string}
 */
proto.GroupAccount.prototype.getMaxOrderValue = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.GroupAccount} returns this
 */
proto.GroupAccount.prototype.setMaxOrderValue = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.SessionSettings.prototype.toObject = function(opt_includeInstance) {
  return proto.SessionSettings.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.SessionSettings} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.SessionSettings.toObject = function(includeInstance, msg) {
  var f, obj = {
    dayOfWeek: jspb.Message.getFieldWithDefault(msg, 1, 0),
    openTime: jspb.Message.getFieldWithDefault(msg, 2, 0),
    closeTime: jspb.Message.getFieldWithDefault(msg, 3, 0),
    preOrderTime: jspb.Message.getFieldWithDefault(msg, 4, 0),
    externalOpenTime: jspb.Message.getFieldWithDefault(msg, 5, 0),
    externalPrecloseTime: jspb.Message.getFieldWithDefault(msg, 6, 0),
    externalCloseTime: jspb.Message.getFieldWithDefault(msg, 7, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.SessionSettings}
 */
proto.SessionSettings.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.SessionSettings;
  return proto.SessionSettings.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.SessionSettings} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.SessionSettings}
 */
proto.SessionSettings.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDayOfWeek(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setOpenTime(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setCloseTime(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setPreOrderTime(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setExternalOpenTime(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setExternalPrecloseTime(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setExternalCloseTime(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.SessionSettings.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.SessionSettings.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.SessionSettings} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.SessionSettings.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getDayOfWeek();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getOpenTime();
  if (f !== 0) {
    writer.writeInt64(
      2,
      f
    );
  }
  f = message.getCloseTime();
  if (f !== 0) {
    writer.writeInt64(
      3,
      f
    );
  }
  f = message.getPreOrderTime();
  if (f !== 0) {
    writer.writeInt64(
      4,
      f
    );
  }
  f = message.getExternalOpenTime();
  if (f !== 0) {
    writer.writeInt64(
      5,
      f
    );
  }
  f = message.getExternalPrecloseTime();
  if (f !== 0) {
    writer.writeInt64(
      6,
      f
    );
  }
  f = message.getExternalCloseTime();
  if (f !== 0) {
    writer.writeInt64(
      7,
      f
    );
  }
};


/**
 * optional int32 day_of_week = 1;
 * @return {number}
 */
proto.SessionSettings.prototype.getDayOfWeek = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.SessionSettings} returns this
 */
proto.SessionSettings.prototype.setDayOfWeek = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional int64 open_time = 2;
 * @return {number}
 */
proto.SessionSettings.prototype.getOpenTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.SessionSettings} returns this
 */
proto.SessionSettings.prototype.setOpenTime = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional int64 close_time = 3;
 * @return {number}
 */
proto.SessionSettings.prototype.getCloseTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.SessionSettings} returns this
 */
proto.SessionSettings.prototype.setCloseTime = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional int64 pre_order_time = 4;
 * @return {number}
 */
proto.SessionSettings.prototype.getPreOrderTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.SessionSettings} returns this
 */
proto.SessionSettings.prototype.setPreOrderTime = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional int64 external_open_time = 5;
 * @return {number}
 */
proto.SessionSettings.prototype.getExternalOpenTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.SessionSettings} returns this
 */
proto.SessionSettings.prototype.setExternalOpenTime = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional int64 external_preclose_time = 6;
 * @return {number}
 */
proto.SessionSettings.prototype.getExternalPrecloseTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.SessionSettings} returns this
 */
proto.SessionSettings.prototype.setExternalPrecloseTime = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional int64 external_close_time = 7;
 * @return {number}
 */
proto.SessionSettings.prototype.getExternalCloseTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {number} value
 * @return {!proto.SessionSettings} returns this
 */
proto.SessionSettings.prototype.setExternalCloseTime = function(value) {
  return jspb.Message.setProto3IntField(this, 7, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.Holiday.prototype.toObject = function(opt_includeInstance) {
  return proto.Holiday.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.Holiday} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Holiday.toObject = function(includeInstance, msg) {
  var f, obj = {
    holidayId: jspb.Message.getFieldWithDefault(msg, 1, 0),
    holidayStatus: jspb.Message.getFieldWithDefault(msg, 2, 0),
    timestampFrom: jspb.Message.getFieldWithDefault(msg, 3, 0),
    timestampTo: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.Holiday}
 */
proto.Holiday.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.Holiday;
  return proto.Holiday.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.Holiday} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.Holiday}
 */
proto.Holiday.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setHolidayId(value);
      break;
    case 2:
      var value = /** @type {!proto.HolidayStatus} */ (reader.readEnum());
      msg.setHolidayStatus(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setTimestampFrom(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setTimestampTo(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.Holiday.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.Holiday.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.Holiday} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Holiday.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getHolidayId();
  if (f !== 0) {
    writer.writeInt64(
      1,
      f
    );
  }
  f = message.getHolidayStatus();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
  f = message.getTimestampFrom();
  if (f !== 0) {
    writer.writeInt64(
      3,
      f
    );
  }
  f = message.getTimestampTo();
  if (f !== 0) {
    writer.writeInt64(
      4,
      f
    );
  }
};


/**
 * optional int64 holiday_id = 1;
 * @return {number}
 */
proto.Holiday.prototype.getHolidayId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.Holiday} returns this
 */
proto.Holiday.prototype.setHolidayId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional HolidayStatus holiday_status = 2;
 * @return {!proto.HolidayStatus}
 */
proto.Holiday.prototype.getHolidayStatus = function() {
  return /** @type {!proto.HolidayStatus} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.HolidayStatus} value
 * @return {!proto.Holiday} returns this
 */
proto.Holiday.prototype.setHolidayStatus = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};


/**
 * optional int64 timestamp_from = 3;
 * @return {number}
 */
proto.Holiday.prototype.getTimestampFrom = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.Holiday} returns this
 */
proto.Holiday.prototype.setTimestampFrom = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional int64 timestamp_to = 4;
 * @return {number}
 */
proto.Holiday.prototype.getTimestampTo = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.Holiday} returns this
 */
proto.Holiday.prototype.setTimestampTo = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.AccountBalance.prototype.toObject = function(opt_includeInstance) {
  return proto.AccountBalance.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.AccountBalance} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.AccountBalance.toObject = function(includeInstance, msg) {
  var f, obj = {
    accountId: jspb.Message.getFieldWithDefault(msg, 1, 0),
    balance: jspb.Message.getFieldWithDefault(msg, 2, ""),
    collateral: jspb.Message.getFieldWithDefault(msg, 3, ""),
    realizedPl: jspb.Message.getFieldWithDefault(msg, 4, ""),
    marginRequired: jspb.Message.getFieldWithDefault(msg, 5, ""),
    marginMaintain: jspb.Message.getFieldWithDefault(msg, 6, ""),
    marginDeficit: jspb.Message.getFieldWithDefault(msg, 7, ""),
    unrealizedPl: jspb.Message.getFieldWithDefault(msg, 8, ""),
    buyingPower: jspb.Message.getFieldWithDefault(msg, 9, ""),
    equity: jspb.Message.getFieldWithDefault(msg, 10, ""),
    withdrawableAmount: jspb.Message.getFieldWithDefault(msg, 11, ""),
    currencyCode: jspb.Message.getFieldWithDefault(msg, 12, ""),
    password: jspb.Message.getFieldWithDefault(msg, 13, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.AccountBalance}
 */
proto.AccountBalance.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.AccountBalance;
  return proto.AccountBalance.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.AccountBalance} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.AccountBalance}
 */
proto.AccountBalance.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setAccountId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setBalance(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setCollateral(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setRealizedPl(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setMarginRequired(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setMarginMaintain(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setMarginDeficit(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setUnrealizedPl(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setBuyingPower(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setEquity(value);
      break;
    case 11:
      var value = /** @type {string} */ (reader.readString());
      msg.setWithdrawableAmount(value);
      break;
    case 12:
      var value = /** @type {string} */ (reader.readString());
      msg.setCurrencyCode(value);
      break;
    case 13:
      var value = /** @type {string} */ (reader.readString());
      msg.setPassword(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.AccountBalance.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.AccountBalance.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.AccountBalance} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.AccountBalance.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAccountId();
  if (f !== 0) {
    writer.writeInt64(
      1,
      f
    );
  }
  f = message.getBalance();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getCollateral();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getRealizedPl();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getMarginRequired();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getMarginMaintain();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getMarginDeficit();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getUnrealizedPl();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getBuyingPower();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getEquity();
  if (f.length > 0) {
    writer.writeString(
      10,
      f
    );
  }
  f = message.getWithdrawableAmount();
  if (f.length > 0) {
    writer.writeString(
      11,
      f
    );
  }
  f = message.getCurrencyCode();
  if (f.length > 0) {
    writer.writeString(
      12,
      f
    );
  }
  f = message.getPassword();
  if (f.length > 0) {
    writer.writeString(
      13,
      f
    );
  }
};


/**
 * optional int64 account_id = 1;
 * @return {number}
 */
proto.AccountBalance.prototype.getAccountId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.AccountBalance} returns this
 */
proto.AccountBalance.prototype.setAccountId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string balance = 2;
 * @return {string}
 */
proto.AccountBalance.prototype.getBalance = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.AccountBalance} returns this
 */
proto.AccountBalance.prototype.setBalance = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string collateral = 3;
 * @return {string}
 */
proto.AccountBalance.prototype.getCollateral = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.AccountBalance} returns this
 */
proto.AccountBalance.prototype.setCollateral = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string realized_pl = 4;
 * @return {string}
 */
proto.AccountBalance.prototype.getRealizedPl = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.AccountBalance} returns this
 */
proto.AccountBalance.prototype.setRealizedPl = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string margin_required = 5;
 * @return {string}
 */
proto.AccountBalance.prototype.getMarginRequired = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.AccountBalance} returns this
 */
proto.AccountBalance.prototype.setMarginRequired = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string margin_maintain = 6;
 * @return {string}
 */
proto.AccountBalance.prototype.getMarginMaintain = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.AccountBalance} returns this
 */
proto.AccountBalance.prototype.setMarginMaintain = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string margin_deficit = 7;
 * @return {string}
 */
proto.AccountBalance.prototype.getMarginDeficit = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.AccountBalance} returns this
 */
proto.AccountBalance.prototype.setMarginDeficit = function(value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string unrealized_pl = 8;
 * @return {string}
 */
proto.AccountBalance.prototype.getUnrealizedPl = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/**
 * @param {string} value
 * @return {!proto.AccountBalance} returns this
 */
proto.AccountBalance.prototype.setUnrealizedPl = function(value) {
  return jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional string buying_power = 9;
 * @return {string}
 */
proto.AccountBalance.prototype.getBuyingPower = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/**
 * @param {string} value
 * @return {!proto.AccountBalance} returns this
 */
proto.AccountBalance.prototype.setBuyingPower = function(value) {
  return jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional string equity = 10;
 * @return {string}
 */
proto.AccountBalance.prototype.getEquity = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/**
 * @param {string} value
 * @return {!proto.AccountBalance} returns this
 */
proto.AccountBalance.prototype.setEquity = function(value) {
  return jspb.Message.setProto3StringField(this, 10, value);
};


/**
 * optional string withdrawable_amount = 11;
 * @return {string}
 */
proto.AccountBalance.prototype.getWithdrawableAmount = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};


/**
 * @param {string} value
 * @return {!proto.AccountBalance} returns this
 */
proto.AccountBalance.prototype.setWithdrawableAmount = function(value) {
  return jspb.Message.setProto3StringField(this, 11, value);
};


/**
 * optional string currency_code = 12;
 * @return {string}
 */
proto.AccountBalance.prototype.getCurrencyCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 12, ""));
};


/**
 * @param {string} value
 * @return {!proto.AccountBalance} returns this
 */
proto.AccountBalance.prototype.setCurrencyCode = function(value) {
  return jspb.Message.setProto3StringField(this, 12, value);
};


/**
 * optional string password = 13;
 * @return {string}
 */
proto.AccountBalance.prototype.getPassword = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 13, ""));
};


/**
 * @param {string} value
 * @return {!proto.AccountBalance} returns this
 */
proto.AccountBalance.prototype.setPassword = function(value) {
  return jspb.Message.setProto3StringField(this, 13, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.AccountPortfolio.prototype.toObject = function(opt_includeInstance) {
  return proto.AccountPortfolio.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.AccountPortfolio} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.AccountPortfolio.toObject = function(includeInstance, msg) {
  var f, obj = {
    accountId: jspb.Message.getFieldWithDefault(msg, 1, 0),
    symbolCode: jspb.Message.getFieldWithDefault(msg, 2, ""),
    totalBuyVolume: jspb.Message.getFieldWithDefault(msg, 3, 0),
    totalBuyAmount: jspb.Message.getFieldWithDefault(msg, 4, ""),
    totalSellVolume: jspb.Message.getFieldWithDefault(msg, 5, 0),
    totalSellAmount: jspb.Message.getFieldWithDefault(msg, 6, ""),
    marketPrice: jspb.Message.getFieldWithDefault(msg, 7, ""),
    investedValue: jspb.Message.getFieldWithDefault(msg, 8, ""),
    currentValue: jspb.Message.getFieldWithDefault(msg, 9, ""),
    realizedPl: jspb.Message.getFieldWithDefault(msg, 10, ""),
    unrealizedPl: jspb.Message.getFieldWithDefault(msg, 11, ""),
    currencyCode: jspb.Message.getFieldWithDefault(msg, 12, ""),
    ownedVolume: jspb.Message.getFieldWithDefault(msg, 13, 0),
    ownedAmount: jspb.Message.getFieldWithDefault(msg, 14, ""),
    totalVolume: jspb.Message.getFieldWithDefault(msg, 15, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.AccountPortfolio}
 */
proto.AccountPortfolio.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.AccountPortfolio;
  return proto.AccountPortfolio.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.AccountPortfolio} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.AccountPortfolio}
 */
proto.AccountPortfolio.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setAccountId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSymbolCode(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setTotalBuyVolume(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setTotalBuyAmount(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setTotalSellVolume(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setTotalSellAmount(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setMarketPrice(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setInvestedValue(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setCurrentValue(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setRealizedPl(value);
      break;
    case 11:
      var value = /** @type {string} */ (reader.readString());
      msg.setUnrealizedPl(value);
      break;
    case 12:
      var value = /** @type {string} */ (reader.readString());
      msg.setCurrencyCode(value);
      break;
    case 13:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setOwnedVolume(value);
      break;
    case 14:
      var value = /** @type {string} */ (reader.readString());
      msg.setOwnedAmount(value);
      break;
    case 15:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setTotalVolume(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.AccountPortfolio.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.AccountPortfolio.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.AccountPortfolio} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.AccountPortfolio.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAccountId();
  if (f !== 0) {
    writer.writeInt64(
      1,
      f
    );
  }
  f = message.getSymbolCode();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getTotalBuyVolume();
  if (f !== 0) {
    writer.writeInt64(
      3,
      f
    );
  }
  f = message.getTotalBuyAmount();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getTotalSellVolume();
  if (f !== 0) {
    writer.writeInt64(
      5,
      f
    );
  }
  f = message.getTotalSellAmount();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getMarketPrice();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getInvestedValue();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getCurrentValue();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getRealizedPl();
  if (f.length > 0) {
    writer.writeString(
      10,
      f
    );
  }
  f = message.getUnrealizedPl();
  if (f.length > 0) {
    writer.writeString(
      11,
      f
    );
  }
  f = message.getCurrencyCode();
  if (f.length > 0) {
    writer.writeString(
      12,
      f
    );
  }
  f = message.getOwnedVolume();
  if (f !== 0) {
    writer.writeInt64(
      13,
      f
    );
  }
  f = message.getOwnedAmount();
  if (f.length > 0) {
    writer.writeString(
      14,
      f
    );
  }
  f = message.getTotalVolume();
  if (f !== 0) {
    writer.writeInt64(
      15,
      f
    );
  }
};


/**
 * optional int64 account_id = 1;
 * @return {number}
 */
proto.AccountPortfolio.prototype.getAccountId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.AccountPortfolio} returns this
 */
proto.AccountPortfolio.prototype.setAccountId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string symbol_code = 2;
 * @return {string}
 */
proto.AccountPortfolio.prototype.getSymbolCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.AccountPortfolio} returns this
 */
proto.AccountPortfolio.prototype.setSymbolCode = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int64 total_buy_volume = 3;
 * @return {number}
 */
proto.AccountPortfolio.prototype.getTotalBuyVolume = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.AccountPortfolio} returns this
 */
proto.AccountPortfolio.prototype.setTotalBuyVolume = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional string total_buy_amount = 4;
 * @return {string}
 */
proto.AccountPortfolio.prototype.getTotalBuyAmount = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.AccountPortfolio} returns this
 */
proto.AccountPortfolio.prototype.setTotalBuyAmount = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional int64 total_sell_volume = 5;
 * @return {number}
 */
proto.AccountPortfolio.prototype.getTotalSellVolume = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.AccountPortfolio} returns this
 */
proto.AccountPortfolio.prototype.setTotalSellVolume = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional string total_sell_amount = 6;
 * @return {string}
 */
proto.AccountPortfolio.prototype.getTotalSellAmount = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.AccountPortfolio} returns this
 */
proto.AccountPortfolio.prototype.setTotalSellAmount = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string market_price = 7;
 * @return {string}
 */
proto.AccountPortfolio.prototype.getMarketPrice = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.AccountPortfolio} returns this
 */
proto.AccountPortfolio.prototype.setMarketPrice = function(value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string invested_value = 8;
 * @return {string}
 */
proto.AccountPortfolio.prototype.getInvestedValue = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/**
 * @param {string} value
 * @return {!proto.AccountPortfolio} returns this
 */
proto.AccountPortfolio.prototype.setInvestedValue = function(value) {
  return jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional string current_value = 9;
 * @return {string}
 */
proto.AccountPortfolio.prototype.getCurrentValue = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/**
 * @param {string} value
 * @return {!proto.AccountPortfolio} returns this
 */
proto.AccountPortfolio.prototype.setCurrentValue = function(value) {
  return jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional string realized_pl = 10;
 * @return {string}
 */
proto.AccountPortfolio.prototype.getRealizedPl = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/**
 * @param {string} value
 * @return {!proto.AccountPortfolio} returns this
 */
proto.AccountPortfolio.prototype.setRealizedPl = function(value) {
  return jspb.Message.setProto3StringField(this, 10, value);
};


/**
 * optional string unrealized_pl = 11;
 * @return {string}
 */
proto.AccountPortfolio.prototype.getUnrealizedPl = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};


/**
 * @param {string} value
 * @return {!proto.AccountPortfolio} returns this
 */
proto.AccountPortfolio.prototype.setUnrealizedPl = function(value) {
  return jspb.Message.setProto3StringField(this, 11, value);
};


/**
 * optional string currency_code = 12;
 * @return {string}
 */
proto.AccountPortfolio.prototype.getCurrencyCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 12, ""));
};


/**
 * @param {string} value
 * @return {!proto.AccountPortfolio} returns this
 */
proto.AccountPortfolio.prototype.setCurrencyCode = function(value) {
  return jspb.Message.setProto3StringField(this, 12, value);
};


/**
 * optional int64 owned_volume = 13;
 * @return {number}
 */
proto.AccountPortfolio.prototype.getOwnedVolume = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 13, 0));
};


/**
 * @param {number} value
 * @return {!proto.AccountPortfolio} returns this
 */
proto.AccountPortfolio.prototype.setOwnedVolume = function(value) {
  return jspb.Message.setProto3IntField(this, 13, value);
};


/**
 * optional string owned_amount = 14;
 * @return {string}
 */
proto.AccountPortfolio.prototype.getOwnedAmount = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 14, ""));
};


/**
 * @param {string} value
 * @return {!proto.AccountPortfolio} returns this
 */
proto.AccountPortfolio.prototype.setOwnedAmount = function(value) {
  return jspb.Message.setProto3StringField(this, 14, value);
};


/**
 * optional int64 total_volume = 15;
 * @return {number}
 */
proto.AccountPortfolio.prototype.getTotalVolume = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 15, 0));
};


/**
 * @param {number} value
 * @return {!proto.AccountPortfolio} returns this
 */
proto.AccountPortfolio.prototype.setTotalVolume = function(value) {
  return jspb.Message.setProto3IntField(this, 15, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.MarketDataSource.prototype.toObject = function(opt_includeInstance) {
  return proto.MarketDataSource.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.MarketDataSource} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.MarketDataSource.toObject = function(includeInstance, msg) {
  var f, obj = {
    exchangeName: jspb.Message.getFieldWithDefault(msg, 1, ""),
    enableFlg: jspb.Message.getBooleanFieldWithDefault(msg, 2, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.MarketDataSource}
 */
proto.MarketDataSource.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.MarketDataSource;
  return proto.MarketDataSource.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.MarketDataSource} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.MarketDataSource}
 */
proto.MarketDataSource.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setExchangeName(value);
      break;
    case 2:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setEnableFlg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.MarketDataSource.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.MarketDataSource.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.MarketDataSource} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.MarketDataSource.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getExchangeName();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getEnableFlg();
  if (f) {
    writer.writeBool(
      2,
      f
    );
  }
};


/**
 * optional string exchange_name = 1;
 * @return {string}
 */
proto.MarketDataSource.prototype.getExchangeName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.MarketDataSource} returns this
 */
proto.MarketDataSource.prototype.setExchangeName = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional bool enable_flg = 2;
 * @return {boolean}
 */
proto.MarketDataSource.prototype.getEnableFlg = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 2, false));
};


/**
 * @param {boolean} value
 * @return {!proto.MarketDataSource} returns this
 */
proto.MarketDataSource.prototype.setEnableFlg = function(value) {
  return jspb.Message.setProto3BooleanField(this, 2, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.MarketSettings.repeatedFields_ = [4,5];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.MarketSettings.prototype.toObject = function(opt_includeInstance) {
  return proto.MarketSettings.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.MarketSettings} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.MarketSettings.toObject = function(includeInstance, msg) {
  var f, obj = {
    marketCode: jspb.Message.getFieldWithDefault(msg, 1, ""),
    mdStatus: jspb.Message.getFieldWithDefault(msg, 2, 0),
    tradingStatus: jspb.Message.getFieldWithDefault(msg, 3, 0),
    mdTimeList: jspb.Message.toObjectList(msg.getMdTimeList(),
    proto.SessionSettings.toObject, includeInstance),
    tradingTimeList: jspb.Message.toObjectList(msg.getTradingTimeList(),
    proto.SessionSettings.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.MarketSettings}
 */
proto.MarketSettings.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.MarketSettings;
  return proto.MarketSettings.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.MarketSettings} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.MarketSettings}
 */
proto.MarketSettings.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setMarketCode(value);
      break;
    case 2:
      var value = /** @type {!proto.MarketStatus} */ (reader.readEnum());
      msg.setMdStatus(value);
      break;
    case 3:
      var value = /** @type {!proto.MarketStatus} */ (reader.readEnum());
      msg.setTradingStatus(value);
      break;
    case 4:
      var value = new proto.SessionSettings;
      reader.readMessage(value,proto.SessionSettings.deserializeBinaryFromReader);
      msg.addMdTime(value);
      break;
    case 5:
      var value = new proto.SessionSettings;
      reader.readMessage(value,proto.SessionSettings.deserializeBinaryFromReader);
      msg.addTradingTime(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.MarketSettings.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.MarketSettings.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.MarketSettings} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.MarketSettings.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMarketCode();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getMdStatus();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
  f = message.getTradingStatus();
  if (f !== 0.0) {
    writer.writeEnum(
      3,
      f
    );
  }
  f = message.getMdTimeList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      4,
      f,
      proto.SessionSettings.serializeBinaryToWriter
    );
  }
  f = message.getTradingTimeList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      5,
      f,
      proto.SessionSettings.serializeBinaryToWriter
    );
  }
};


/**
 * optional string market_code = 1;
 * @return {string}
 */
proto.MarketSettings.prototype.getMarketCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.MarketSettings} returns this
 */
proto.MarketSettings.prototype.setMarketCode = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional MarketStatus md_status = 2;
 * @return {!proto.MarketStatus}
 */
proto.MarketSettings.prototype.getMdStatus = function() {
  return /** @type {!proto.MarketStatus} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.MarketStatus} value
 * @return {!proto.MarketSettings} returns this
 */
proto.MarketSettings.prototype.setMdStatus = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};


/**
 * optional MarketStatus trading_status = 3;
 * @return {!proto.MarketStatus}
 */
proto.MarketSettings.prototype.getTradingStatus = function() {
  return /** @type {!proto.MarketStatus} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {!proto.MarketStatus} value
 * @return {!proto.MarketSettings} returns this
 */
proto.MarketSettings.prototype.setTradingStatus = function(value) {
  return jspb.Message.setProto3EnumField(this, 3, value);
};


/**
 * repeated SessionSettings md_time = 4;
 * @return {!Array<!proto.SessionSettings>}
 */
proto.MarketSettings.prototype.getMdTimeList = function() {
  return /** @type{!Array<!proto.SessionSettings>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.SessionSettings, 4));
};


/**
 * @param {!Array<!proto.SessionSettings>} value
 * @return {!proto.MarketSettings} returns this
*/
proto.MarketSettings.prototype.setMdTimeList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 4, value);
};


/**
 * @param {!proto.SessionSettings=} opt_value
 * @param {number=} opt_index
 * @return {!proto.SessionSettings}
 */
proto.MarketSettings.prototype.addMdTime = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 4, opt_value, proto.SessionSettings, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.MarketSettings} returns this
 */
proto.MarketSettings.prototype.clearMdTimeList = function() {
  return this.setMdTimeList([]);
};


/**
 * repeated SessionSettings trading_time = 5;
 * @return {!Array<!proto.SessionSettings>}
 */
proto.MarketSettings.prototype.getTradingTimeList = function() {
  return /** @type{!Array<!proto.SessionSettings>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.SessionSettings, 5));
};


/**
 * @param {!Array<!proto.SessionSettings>} value
 * @return {!proto.MarketSettings} returns this
*/
proto.MarketSettings.prototype.setTradingTimeList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 5, value);
};


/**
 * @param {!proto.SessionSettings=} opt_value
 * @param {number=} opt_index
 * @return {!proto.SessionSettings}
 */
proto.MarketSettings.prototype.addTradingTime = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 5, opt_value, proto.SessionSettings, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.MarketSettings} returns this
 */
proto.MarketSettings.prototype.clearTradingTimeList = function() {
  return this.setTradingTimeList([]);
};


/**
 * @enum {number}
 */
proto.GroupType = {
  GT_NONE: 0,
  GT_LP: 1,
  GT_RETAIL: 2
};

/**
 * @enum {number}
 */
proto.HolidayStatus = {
  HS_NONE: 0,
  HS_ACTIVE: 1,
  HS_DEACTIVE: 2
};

/**
 * @enum {number}
 */
proto.DealAction = {
  DEAL_NONE: 0,
  DEAL_BALANCE: 2,
  DEAL_CREDIT: 3,
  DEAL_CHARGE: 4,
  DEAL_BUY: 5,
  DEAL_SELL: 6,
  DEAL_COLLATERAL: 7
};

/**
 * @enum {number}
 */
proto.SessionStatusCode = {
  SESSION_NONE: 0,
  SESSION_CLOSE: 1,
  SESSION_OPEN: 2,
  SESSION_REMOVE: 3,
  SESSION_NOTFOUND: 4
};

/**
 * @enum {number}
 */
proto.MarketStatus = {
  MS_NONE: 0,
  MS_ACTIVE: 1,
  MS_DEACTIVE: 2
};

/**
 * @enum {number}
 */
proto.MsgCode = {
  MT_RET_OK_NONE: 0,
  MT_RET_OK: 1,
  MT_RET_ERROR: 2,
  MT_RET_ERR_PARAMS: 3,
  MT_RET_ERR_PERMISSIONS: 8,
  MT_RET_ERR_TIMEOUT: 9,
  MT_RET_ERR_NOTFOUND: 13,
  MT_RET_FORWARD_EXT_SYSTEM: 100,
  MT_RET_SUBCRIBE_OK: 310,
  MT_RET_SUBCRIBE_ALREADY: 311,
  MT_RET_SUBCRIBE_ERROR: 312,
  MT_RET_UNSUBCRIBE_OK: 320,
  MT_RET_UNSUBCRIBE_ERROR: 322,
  MT_RET_AUTH_ACCOUNT_INVALID: 1001,
  MT_RET_AUTH_ACCOUNT_DISABLED: 1002,
  MT_RET_REQUEST_INVALID_SYMBOL: 10013,
  MT_RET_REQUEST_INVALID_VOLUME: 10014,
  MT_RET_ERR_NOT_ENOUGH_MONEY: 10019,
  MT_RET_REQUEST_INVALID_FILL: 10030,
  MT_RET_REQUEST_LIMIT_VOLUME: 10034,
  MT_RET_REQUEST_INVALID_ORDER_TYPE: 10035,
  MT_RET_REQUEST_INVALID_LIMIT_PRICE: 10036,
  MT_RET_REQUEST_INVALID_TRIGGER_PRICE: 10037,
  MT_RET_REQUEST_PROHIBITED_OPEN_ORDER: 10038,
  MT_RET_REQUEST_PROHIBITED_CLOSE_ORDER: 10039,
  MT_RET_MARKET_CLOSED: 10040,
  MT_RET_INVALID_TICK_SIZE: 10041,
  MT_RET_INVALID_PRICE_RANGE: 10042,
  MT_RET_INVALID_MIN_LOT: 10043,
  MT_RET_INVALID_LOT_SIZE: 10044,
  MT_RET_NEGATIVE_QTY: 10045,
  MT_RET_EXCEED_MAX_ORDER_VOLUME: 10046,
  MT_RET_NOT_ENOUGH_MIN_ORDER_VALUE: 10047,
  MT_RET_INVALID_HOLIDAY_SESSION: 10048,
  MT_RET_INVALID_PASSWORD: 10049,
  MT_RET_TOKEN_EXPIRED: 10050,
  MT_RET_CHANGE_PASSWORD_FAILED: 10051,
  MT_RET_EXIST_LIMIT_ORDER_IN_QUEUE: 10052,
  MT_RET_EXCEED_MAX_ORDER_VALUE: 10053,
  MT_RET_UNKNOWN_ORDER_ID: 10054,
  MT_RET_ERROR_FROM_BO: 10055,
  MT_RET_ORDER_CLOSED: 10056,
  MT_RET_REQUEST_MARKET_ACCESS_DENIED: 10060,
  MT_RET_RMS_PROGRAM_ERROR: 20001,
  MT_RET_RMS_INVALID_INPUT: 20002,
  MT_RET_RMS_INVALID_SIDE: 20003,
  MT_RET_RMS_INVALID_COMPANY: 20004,
  MT_RET_RMS_INVALID_CURRENCY: 20005,
  MT_RET_RMS_INVALID_LINKAGE: 20006,
  MT_RET_RMS_NO_ACCESS: 20007,
  MT_RET_RMS_PASSWORD_FAILED: 20008,
  MT_RET_RMS_ERROR_COMPANY_INFO: 20009,
  MT_RET_RMS_PERMISSION_ACCESS: 20010,
  MT_RET_RMS_INVALID_ORDER_NO: 20011,
  MT_RET_RMS_INVALID_PRICE_LENGTH: 20012,
  MT_RET_RMS_STOP_PRICE_LENGTH: 20013,
  MT_RET_RMS_INVALID_ORDER_TYPE: 20014,
  MT_RET_RMS_INVALID_CLIENT: 20015,
  MT_RET_RMS_DB_ERROR: 20016,
  MT_RET_RMS_FAILED_RISK_CHECK: 30001
};

goog.object.extend(exports, proto);
