syntax = "proto3";
import "trading_model.proto";
import "system_model.proto";

//New Order Single
message NewOrderSingleRequest {
  Order order = 1;
  string secret_key	= 2;	//trading pin
  string session_id = 3; // E2EE SessionID
  string random_number = 4; // E2EE RandomNumber
  string hash_password = 5; // E2EE password encrypted value from client side
  string hash_password_only8 = 6; // E2EE password encrypted value from client side (substring 8)
  bool hidden_confirm_flg = 7; //True -> hide confirm screen
  string pspl_account_no = 8; //Send accountNo when place order in remisier
}

message NewOrderSingleResponse {
	MsgCode msg_code = 1;
	string msg_text = 2;
  	Order order = 3;
}

//New Order Multi
message NewOrderMultiRequest {
  	repeated Order order = 1;
	string secret_key	= 2;	//trading pin
	string session_id = 3; // E2EE SessionID
	string random_number = 4; // E2EE RandomNumber
	string hash_password = 5; // E2EE password encrypted value from client side
	string hash_password_only8 = 6; // E2EE password encrypted value from client side (substring 8)
}

message NewOrderMultiResponse {
	MsgCode msg_code = 1;
	string msg_text = 2;
  	repeated Order order = 3;
}

//Close Order
message CloseOrderRequest {
  	repeated Order order = 1;
	string secret_key	= 2;		//trading pin
	string session_id = 3; // E2EE SessionID
	string random_number = 4; // E2EE RandomNumber
	string hash_password = 5; // E2EE password encrypted value from client side
	string hash_password_only8 = 6; // E2EE password encrypted value from client side (substring 8)
	bool hidden_confirm_flg = 7; 	//True -> hide confirm screen
}

message CloseOrderResponse {
	MsgCode msg_code = 1;
	string msg_text = 2;
	repeated Order order = 3;
}

//Modify Order
message ModifyOrderRequest {
  repeated Order order = 1;
  string secret_key	= 2;			//trading pin
  string session_id = 3; // E2EE SessionID
  string random_number = 4; // E2EE RandomNumber
  string hash_password = 5; // E2EE password encrypted value from client side
  string hash_password_only8 = 6; // E2EE password encrypted value from client side (substring 8)
  bool hidden_confirm_flg = 7; 	//True -> hide confirm screen
  string team_code = 8; //Team code
  string team_password = 9; //Team ID password encrypted value
}

message ModifyOrderResponse {
	MsgCode msg_code = 1;
	string msg_text = 2;
  	repeated Order order = 3;
}

//Cancel Order
message CancelOrderRequest {
  repeated Order order = 1;
  string secret_key	= 2;			//trading pin
  string session_id = 3; // E2EE SessionID
  string random_number = 4; // E2EE RandomNumber
  string hash_password = 5; // E2EE password encrypted value from client side
  string hash_password_only8 = 6; // E2EE password encrypted value from client side (substring 8)
  bool hidden_confirm_flg = 7; 	//True -> hide confirm screen
  string team_code = 8; //Team code
  string team_password = 9; //Team ID password encrypted value
}

message CancelOrderResponse {
	MsgCode msg_code = 1;
	string msg_text = 2;
  	repeated Order order = 3;
}

//Notify order status
message OrderEvent {
  repeated Order order = 1;
}

//Notify trade events
message TradeEvent {
  repeated Trade trade = 1;
}

//Subscribe Trade event
message SubscribeTradeEventRequest {
	repeated string symbol_code = 1;
}
message SubscribeTradeEventResponse {
	MsgCode msg_code = 1;
	string msg_text = 2;
}

//Unsubscribe Trade event
message UnsubscribeTradeEventRequest {
	repeated string symbol_code = 1;
}

message UnsubscribeTradeEventResponse {
	MsgCode msg_code = 1;
	string msg_text = 2;
}

message UpdateOrderStatusRequest {
	Order order = 1; 
}

message UpdateOrderStatusResponse {
	MsgCode msg_code = 2;
	string msg_text = 3;
}