import i18n from 'i18next'
import Backend from 'i18next-http-backend'
import { initReactI18next } from 'react-i18next'
import * as Constant from './model/constant';

i18n
    .use(Backend)
    .use(initReactI18next)
    .init({
        lng: localStorage.getItem(Constant.LocalStorageKey.LANGUAGE),
        backend: {
            loadPath: `${process.env.PUBLIC_URL}/i18n/{{ns}}/{{lng}}.json?v=20220114`
        },
        fallbackLng: 'en',
        debug: true,
        ns: ['translations'],
        defaultNS: 'translations',
        // keySeparator: false,
        interpolation: {
            escapeValue: false,
            formatSeparator: ','
        },
        react: {
            wait: true
        }
    })

export default i18n