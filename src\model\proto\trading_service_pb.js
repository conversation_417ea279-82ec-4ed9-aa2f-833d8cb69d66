// source: trading_service.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = (function() {
  if (this) { return this; }
  if (typeof window !== 'undefined') { return window; }
  if (typeof global !== 'undefined') { return global; }
  if (typeof self !== 'undefined') { return self; }
  return Function('return this')();
}.call(null));

var trading_model_pb = require('./trading_model_pb.js');
goog.object.extend(proto, trading_model_pb);
var system_model_pb = require('./system_model_pb.js');
goog.object.extend(proto, system_model_pb);
goog.exportSymbol('proto.CancelOrderRequest', null, global);
goog.exportSymbol('proto.CancelOrderResponse', null, global);
goog.exportSymbol('proto.CloseOrderRequest', null, global);
goog.exportSymbol('proto.CloseOrderResponse', null, global);
goog.exportSymbol('proto.ModifyOrderRequest', null, global);
goog.exportSymbol('proto.ModifyOrderResponse', null, global);
goog.exportSymbol('proto.NewOrderMultiRequest', null, global);
goog.exportSymbol('proto.NewOrderMultiResponse', null, global);
goog.exportSymbol('proto.NewOrderSingleRequest', null, global);
goog.exportSymbol('proto.NewOrderSingleResponse', null, global);
goog.exportSymbol('proto.OrderEvent', null, global);
goog.exportSymbol('proto.SubscribeTradeEventRequest', null, global);
goog.exportSymbol('proto.SubscribeTradeEventResponse', null, global);
goog.exportSymbol('proto.TradeEvent', null, global);
goog.exportSymbol('proto.UnsubscribeTradeEventRequest', null, global);
goog.exportSymbol('proto.UnsubscribeTradeEventResponse', null, global);
goog.exportSymbol('proto.UpdateOrderStatusRequest', null, global);
goog.exportSymbol('proto.UpdateOrderStatusResponse', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.NewOrderSingleRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.NewOrderSingleRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.NewOrderSingleRequest.displayName = 'proto.NewOrderSingleRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.NewOrderSingleResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.NewOrderSingleResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.NewOrderSingleResponse.displayName = 'proto.NewOrderSingleResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.NewOrderMultiRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.NewOrderMultiRequest.repeatedFields_, null);
};
goog.inherits(proto.NewOrderMultiRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.NewOrderMultiRequest.displayName = 'proto.NewOrderMultiRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.NewOrderMultiResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.NewOrderMultiResponse.repeatedFields_, null);
};
goog.inherits(proto.NewOrderMultiResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.NewOrderMultiResponse.displayName = 'proto.NewOrderMultiResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.CloseOrderRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.CloseOrderRequest.repeatedFields_, null);
};
goog.inherits(proto.CloseOrderRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.CloseOrderRequest.displayName = 'proto.CloseOrderRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.CloseOrderResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.CloseOrderResponse.repeatedFields_, null);
};
goog.inherits(proto.CloseOrderResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.CloseOrderResponse.displayName = 'proto.CloseOrderResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ModifyOrderRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ModifyOrderRequest.repeatedFields_, null);
};
goog.inherits(proto.ModifyOrderRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ModifyOrderRequest.displayName = 'proto.ModifyOrderRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ModifyOrderResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ModifyOrderResponse.repeatedFields_, null);
};
goog.inherits(proto.ModifyOrderResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ModifyOrderResponse.displayName = 'proto.ModifyOrderResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.CancelOrderRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.CancelOrderRequest.repeatedFields_, null);
};
goog.inherits(proto.CancelOrderRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.CancelOrderRequest.displayName = 'proto.CancelOrderRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.CancelOrderResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.CancelOrderResponse.repeatedFields_, null);
};
goog.inherits(proto.CancelOrderResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.CancelOrderResponse.displayName = 'proto.CancelOrderResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.OrderEvent = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.OrderEvent.repeatedFields_, null);
};
goog.inherits(proto.OrderEvent, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.OrderEvent.displayName = 'proto.OrderEvent';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.TradeEvent = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.TradeEvent.repeatedFields_, null);
};
goog.inherits(proto.TradeEvent, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.TradeEvent.displayName = 'proto.TradeEvent';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.SubscribeTradeEventRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.SubscribeTradeEventRequest.repeatedFields_, null);
};
goog.inherits(proto.SubscribeTradeEventRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.SubscribeTradeEventRequest.displayName = 'proto.SubscribeTradeEventRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.SubscribeTradeEventResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.SubscribeTradeEventResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.SubscribeTradeEventResponse.displayName = 'proto.SubscribeTradeEventResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.UnsubscribeTradeEventRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.UnsubscribeTradeEventRequest.repeatedFields_, null);
};
goog.inherits(proto.UnsubscribeTradeEventRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.UnsubscribeTradeEventRequest.displayName = 'proto.UnsubscribeTradeEventRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.UnsubscribeTradeEventResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.UnsubscribeTradeEventResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.UnsubscribeTradeEventResponse.displayName = 'proto.UnsubscribeTradeEventResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.UpdateOrderStatusRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.UpdateOrderStatusRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.UpdateOrderStatusRequest.displayName = 'proto.UpdateOrderStatusRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.UpdateOrderStatusResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.UpdateOrderStatusResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.UpdateOrderStatusResponse.displayName = 'proto.UpdateOrderStatusResponse';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.NewOrderSingleRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.NewOrderSingleRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.NewOrderSingleRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.NewOrderSingleRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    order: (f = msg.getOrder()) && trading_model_pb.Order.toObject(includeInstance, f),
    secretKey: jspb.Message.getFieldWithDefault(msg, 2, ""),
    sessionId: jspb.Message.getFieldWithDefault(msg, 3, ""),
    randomNumber: jspb.Message.getFieldWithDefault(msg, 4, ""),
    hashPassword: jspb.Message.getFieldWithDefault(msg, 5, ""),
    hashPasswordOnly8: jspb.Message.getFieldWithDefault(msg, 6, ""),
    hiddenConfirmFlg: jspb.Message.getBooleanFieldWithDefault(msg, 7, false),
    psplAccountNo: jspb.Message.getFieldWithDefault(msg, 8, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.NewOrderSingleRequest}
 */
proto.NewOrderSingleRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.NewOrderSingleRequest;
  return proto.NewOrderSingleRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.NewOrderSingleRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.NewOrderSingleRequest}
 */
proto.NewOrderSingleRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new trading_model_pb.Order;
      reader.readMessage(value,trading_model_pb.Order.deserializeBinaryFromReader);
      msg.setOrder(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSecretKey(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setSessionId(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setRandomNumber(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setHashPassword(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setHashPasswordOnly8(value);
      break;
    case 7:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setHiddenConfirmFlg(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setPsplAccountNo(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.NewOrderSingleRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.NewOrderSingleRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.NewOrderSingleRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.NewOrderSingleRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOrder();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      trading_model_pb.Order.serializeBinaryToWriter
    );
  }
  f = message.getSecretKey();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSessionId();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getRandomNumber();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getHashPassword();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getHashPasswordOnly8();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getHiddenConfirmFlg();
  if (f) {
    writer.writeBool(
      7,
      f
    );
  }
  f = message.getPsplAccountNo();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
};


/**
 * optional Order order = 1;
 * @return {?proto.Order}
 */
proto.NewOrderSingleRequest.prototype.getOrder = function() {
  return /** @type{?proto.Order} */ (
    jspb.Message.getWrapperField(this, trading_model_pb.Order, 1));
};


/**
 * @param {?proto.Order|undefined} value
 * @return {!proto.NewOrderSingleRequest} returns this
*/
proto.NewOrderSingleRequest.prototype.setOrder = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.NewOrderSingleRequest} returns this
 */
proto.NewOrderSingleRequest.prototype.clearOrder = function() {
  return this.setOrder(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.NewOrderSingleRequest.prototype.hasOrder = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string secret_key = 2;
 * @return {string}
 */
proto.NewOrderSingleRequest.prototype.getSecretKey = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.NewOrderSingleRequest} returns this
 */
proto.NewOrderSingleRequest.prototype.setSecretKey = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string session_id = 3;
 * @return {string}
 */
proto.NewOrderSingleRequest.prototype.getSessionId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.NewOrderSingleRequest} returns this
 */
proto.NewOrderSingleRequest.prototype.setSessionId = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string random_number = 4;
 * @return {string}
 */
proto.NewOrderSingleRequest.prototype.getRandomNumber = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.NewOrderSingleRequest} returns this
 */
proto.NewOrderSingleRequest.prototype.setRandomNumber = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string hash_password = 5;
 * @return {string}
 */
proto.NewOrderSingleRequest.prototype.getHashPassword = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.NewOrderSingleRequest} returns this
 */
proto.NewOrderSingleRequest.prototype.setHashPassword = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string hash_password_only8 = 6;
 * @return {string}
 */
proto.NewOrderSingleRequest.prototype.getHashPasswordOnly8 = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.NewOrderSingleRequest} returns this
 */
proto.NewOrderSingleRequest.prototype.setHashPasswordOnly8 = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional bool hidden_confirm_flg = 7;
 * @return {boolean}
 */
proto.NewOrderSingleRequest.prototype.getHiddenConfirmFlg = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 7, false));
};


/**
 * @param {boolean} value
 * @return {!proto.NewOrderSingleRequest} returns this
 */
proto.NewOrderSingleRequest.prototype.setHiddenConfirmFlg = function(value) {
  return jspb.Message.setProto3BooleanField(this, 7, value);
};


/**
 * optional string pspl_account_no = 8;
 * @return {string}
 */
proto.NewOrderSingleRequest.prototype.getPsplAccountNo = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/**
 * @param {string} value
 * @return {!proto.NewOrderSingleRequest} returns this
 */
proto.NewOrderSingleRequest.prototype.setPsplAccountNo = function(value) {
  return jspb.Message.setProto3StringField(this, 8, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.NewOrderSingleResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.NewOrderSingleResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.NewOrderSingleResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.NewOrderSingleResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    msgCode: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msgText: jspb.Message.getFieldWithDefault(msg, 2, ""),
    order: (f = msg.getOrder()) && trading_model_pb.Order.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.NewOrderSingleResponse}
 */
proto.NewOrderSingleResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.NewOrderSingleResponse;
  return proto.NewOrderSingleResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.NewOrderSingleResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.NewOrderSingleResponse}
 */
proto.NewOrderSingleResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.MsgCode} */ (reader.readEnum());
      msg.setMsgCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsgText(value);
      break;
    case 3:
      var value = new trading_model_pb.Order;
      reader.readMessage(value,trading_model_pb.Order.deserializeBinaryFromReader);
      msg.setOrder(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.NewOrderSingleResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.NewOrderSingleResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.NewOrderSingleResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.NewOrderSingleResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMsgCode();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getMsgText();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getOrder();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      trading_model_pb.Order.serializeBinaryToWriter
    );
  }
};


/**
 * optional MsgCode msg_code = 1;
 * @return {!proto.MsgCode}
 */
proto.NewOrderSingleResponse.prototype.getMsgCode = function() {
  return /** @type {!proto.MsgCode} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.MsgCode} value
 * @return {!proto.NewOrderSingleResponse} returns this
 */
proto.NewOrderSingleResponse.prototype.setMsgCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional string msg_text = 2;
 * @return {string}
 */
proto.NewOrderSingleResponse.prototype.getMsgText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.NewOrderSingleResponse} returns this
 */
proto.NewOrderSingleResponse.prototype.setMsgText = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional Order order = 3;
 * @return {?proto.Order}
 */
proto.NewOrderSingleResponse.prototype.getOrder = function() {
  return /** @type{?proto.Order} */ (
    jspb.Message.getWrapperField(this, trading_model_pb.Order, 3));
};


/**
 * @param {?proto.Order|undefined} value
 * @return {!proto.NewOrderSingleResponse} returns this
*/
proto.NewOrderSingleResponse.prototype.setOrder = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.NewOrderSingleResponse} returns this
 */
proto.NewOrderSingleResponse.prototype.clearOrder = function() {
  return this.setOrder(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.NewOrderSingleResponse.prototype.hasOrder = function() {
  return jspb.Message.getField(this, 3) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.NewOrderMultiRequest.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.NewOrderMultiRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.NewOrderMultiRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.NewOrderMultiRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.NewOrderMultiRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    orderList: jspb.Message.toObjectList(msg.getOrderList(),
    trading_model_pb.Order.toObject, includeInstance),
    secretKey: jspb.Message.getFieldWithDefault(msg, 2, ""),
    sessionId: jspb.Message.getFieldWithDefault(msg, 3, ""),
    randomNumber: jspb.Message.getFieldWithDefault(msg, 4, ""),
    hashPassword: jspb.Message.getFieldWithDefault(msg, 5, ""),
    hashPasswordOnly8: jspb.Message.getFieldWithDefault(msg, 6, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.NewOrderMultiRequest}
 */
proto.NewOrderMultiRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.NewOrderMultiRequest;
  return proto.NewOrderMultiRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.NewOrderMultiRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.NewOrderMultiRequest}
 */
proto.NewOrderMultiRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new trading_model_pb.Order;
      reader.readMessage(value,trading_model_pb.Order.deserializeBinaryFromReader);
      msg.addOrder(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSecretKey(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setSessionId(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setRandomNumber(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setHashPassword(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setHashPasswordOnly8(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.NewOrderMultiRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.NewOrderMultiRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.NewOrderMultiRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.NewOrderMultiRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOrderList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      trading_model_pb.Order.serializeBinaryToWriter
    );
  }
  f = message.getSecretKey();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSessionId();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getRandomNumber();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getHashPassword();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getHashPasswordOnly8();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
};


/**
 * repeated Order order = 1;
 * @return {!Array<!proto.Order>}
 */
proto.NewOrderMultiRequest.prototype.getOrderList = function() {
  return /** @type{!Array<!proto.Order>} */ (
    jspb.Message.getRepeatedWrapperField(this, trading_model_pb.Order, 1));
};


/**
 * @param {!Array<!proto.Order>} value
 * @return {!proto.NewOrderMultiRequest} returns this
*/
proto.NewOrderMultiRequest.prototype.setOrderList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.Order=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Order}
 */
proto.NewOrderMultiRequest.prototype.addOrder = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.Order, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.NewOrderMultiRequest} returns this
 */
proto.NewOrderMultiRequest.prototype.clearOrderList = function() {
  return this.setOrderList([]);
};


/**
 * optional string secret_key = 2;
 * @return {string}
 */
proto.NewOrderMultiRequest.prototype.getSecretKey = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.NewOrderMultiRequest} returns this
 */
proto.NewOrderMultiRequest.prototype.setSecretKey = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string session_id = 3;
 * @return {string}
 */
proto.NewOrderMultiRequest.prototype.getSessionId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.NewOrderMultiRequest} returns this
 */
proto.NewOrderMultiRequest.prototype.setSessionId = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string random_number = 4;
 * @return {string}
 */
proto.NewOrderMultiRequest.prototype.getRandomNumber = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.NewOrderMultiRequest} returns this
 */
proto.NewOrderMultiRequest.prototype.setRandomNumber = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string hash_password = 5;
 * @return {string}
 */
proto.NewOrderMultiRequest.prototype.getHashPassword = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.NewOrderMultiRequest} returns this
 */
proto.NewOrderMultiRequest.prototype.setHashPassword = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string hash_password_only8 = 6;
 * @return {string}
 */
proto.NewOrderMultiRequest.prototype.getHashPasswordOnly8 = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.NewOrderMultiRequest} returns this
 */
proto.NewOrderMultiRequest.prototype.setHashPasswordOnly8 = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.NewOrderMultiResponse.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.NewOrderMultiResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.NewOrderMultiResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.NewOrderMultiResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.NewOrderMultiResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    msgCode: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msgText: jspb.Message.getFieldWithDefault(msg, 2, ""),
    orderList: jspb.Message.toObjectList(msg.getOrderList(),
    trading_model_pb.Order.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.NewOrderMultiResponse}
 */
proto.NewOrderMultiResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.NewOrderMultiResponse;
  return proto.NewOrderMultiResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.NewOrderMultiResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.NewOrderMultiResponse}
 */
proto.NewOrderMultiResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.MsgCode} */ (reader.readEnum());
      msg.setMsgCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsgText(value);
      break;
    case 3:
      var value = new trading_model_pb.Order;
      reader.readMessage(value,trading_model_pb.Order.deserializeBinaryFromReader);
      msg.addOrder(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.NewOrderMultiResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.NewOrderMultiResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.NewOrderMultiResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.NewOrderMultiResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMsgCode();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getMsgText();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getOrderList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      trading_model_pb.Order.serializeBinaryToWriter
    );
  }
};


/**
 * optional MsgCode msg_code = 1;
 * @return {!proto.MsgCode}
 */
proto.NewOrderMultiResponse.prototype.getMsgCode = function() {
  return /** @type {!proto.MsgCode} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.MsgCode} value
 * @return {!proto.NewOrderMultiResponse} returns this
 */
proto.NewOrderMultiResponse.prototype.setMsgCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional string msg_text = 2;
 * @return {string}
 */
proto.NewOrderMultiResponse.prototype.getMsgText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.NewOrderMultiResponse} returns this
 */
proto.NewOrderMultiResponse.prototype.setMsgText = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * repeated Order order = 3;
 * @return {!Array<!proto.Order>}
 */
proto.NewOrderMultiResponse.prototype.getOrderList = function() {
  return /** @type{!Array<!proto.Order>} */ (
    jspb.Message.getRepeatedWrapperField(this, trading_model_pb.Order, 3));
};


/**
 * @param {!Array<!proto.Order>} value
 * @return {!proto.NewOrderMultiResponse} returns this
*/
proto.NewOrderMultiResponse.prototype.setOrderList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.Order=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Order}
 */
proto.NewOrderMultiResponse.prototype.addOrder = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.Order, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.NewOrderMultiResponse} returns this
 */
proto.NewOrderMultiResponse.prototype.clearOrderList = function() {
  return this.setOrderList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.CloseOrderRequest.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.CloseOrderRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.CloseOrderRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.CloseOrderRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CloseOrderRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    orderList: jspb.Message.toObjectList(msg.getOrderList(),
    trading_model_pb.Order.toObject, includeInstance),
    secretKey: jspb.Message.getFieldWithDefault(msg, 2, ""),
    sessionId: jspb.Message.getFieldWithDefault(msg, 3, ""),
    randomNumber: jspb.Message.getFieldWithDefault(msg, 4, ""),
    hashPassword: jspb.Message.getFieldWithDefault(msg, 5, ""),
    hashPasswordOnly8: jspb.Message.getFieldWithDefault(msg, 6, ""),
    hiddenConfirmFlg: jspb.Message.getBooleanFieldWithDefault(msg, 7, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.CloseOrderRequest}
 */
proto.CloseOrderRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.CloseOrderRequest;
  return proto.CloseOrderRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.CloseOrderRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.CloseOrderRequest}
 */
proto.CloseOrderRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new trading_model_pb.Order;
      reader.readMessage(value,trading_model_pb.Order.deserializeBinaryFromReader);
      msg.addOrder(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSecretKey(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setSessionId(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setRandomNumber(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setHashPassword(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setHashPasswordOnly8(value);
      break;
    case 7:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setHiddenConfirmFlg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.CloseOrderRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.CloseOrderRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.CloseOrderRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CloseOrderRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOrderList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      trading_model_pb.Order.serializeBinaryToWriter
    );
  }
  f = message.getSecretKey();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSessionId();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getRandomNumber();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getHashPassword();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getHashPasswordOnly8();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getHiddenConfirmFlg();
  if (f) {
    writer.writeBool(
      7,
      f
    );
  }
};


/**
 * repeated Order order = 1;
 * @return {!Array<!proto.Order>}
 */
proto.CloseOrderRequest.prototype.getOrderList = function() {
  return /** @type{!Array<!proto.Order>} */ (
    jspb.Message.getRepeatedWrapperField(this, trading_model_pb.Order, 1));
};


/**
 * @param {!Array<!proto.Order>} value
 * @return {!proto.CloseOrderRequest} returns this
*/
proto.CloseOrderRequest.prototype.setOrderList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.Order=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Order}
 */
proto.CloseOrderRequest.prototype.addOrder = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.Order, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.CloseOrderRequest} returns this
 */
proto.CloseOrderRequest.prototype.clearOrderList = function() {
  return this.setOrderList([]);
};


/**
 * optional string secret_key = 2;
 * @return {string}
 */
proto.CloseOrderRequest.prototype.getSecretKey = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.CloseOrderRequest} returns this
 */
proto.CloseOrderRequest.prototype.setSecretKey = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string session_id = 3;
 * @return {string}
 */
proto.CloseOrderRequest.prototype.getSessionId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.CloseOrderRequest} returns this
 */
proto.CloseOrderRequest.prototype.setSessionId = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string random_number = 4;
 * @return {string}
 */
proto.CloseOrderRequest.prototype.getRandomNumber = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.CloseOrderRequest} returns this
 */
proto.CloseOrderRequest.prototype.setRandomNumber = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string hash_password = 5;
 * @return {string}
 */
proto.CloseOrderRequest.prototype.getHashPassword = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.CloseOrderRequest} returns this
 */
proto.CloseOrderRequest.prototype.setHashPassword = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string hash_password_only8 = 6;
 * @return {string}
 */
proto.CloseOrderRequest.prototype.getHashPasswordOnly8 = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.CloseOrderRequest} returns this
 */
proto.CloseOrderRequest.prototype.setHashPasswordOnly8 = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional bool hidden_confirm_flg = 7;
 * @return {boolean}
 */
proto.CloseOrderRequest.prototype.getHiddenConfirmFlg = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 7, false));
};


/**
 * @param {boolean} value
 * @return {!proto.CloseOrderRequest} returns this
 */
proto.CloseOrderRequest.prototype.setHiddenConfirmFlg = function(value) {
  return jspb.Message.setProto3BooleanField(this, 7, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.CloseOrderResponse.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.CloseOrderResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.CloseOrderResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.CloseOrderResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CloseOrderResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    msgCode: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msgText: jspb.Message.getFieldWithDefault(msg, 2, ""),
    orderList: jspb.Message.toObjectList(msg.getOrderList(),
    trading_model_pb.Order.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.CloseOrderResponse}
 */
proto.CloseOrderResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.CloseOrderResponse;
  return proto.CloseOrderResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.CloseOrderResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.CloseOrderResponse}
 */
proto.CloseOrderResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.MsgCode} */ (reader.readEnum());
      msg.setMsgCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsgText(value);
      break;
    case 3:
      var value = new trading_model_pb.Order;
      reader.readMessage(value,trading_model_pb.Order.deserializeBinaryFromReader);
      msg.addOrder(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.CloseOrderResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.CloseOrderResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.CloseOrderResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CloseOrderResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMsgCode();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getMsgText();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getOrderList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      trading_model_pb.Order.serializeBinaryToWriter
    );
  }
};


/**
 * optional MsgCode msg_code = 1;
 * @return {!proto.MsgCode}
 */
proto.CloseOrderResponse.prototype.getMsgCode = function() {
  return /** @type {!proto.MsgCode} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.MsgCode} value
 * @return {!proto.CloseOrderResponse} returns this
 */
proto.CloseOrderResponse.prototype.setMsgCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional string msg_text = 2;
 * @return {string}
 */
proto.CloseOrderResponse.prototype.getMsgText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.CloseOrderResponse} returns this
 */
proto.CloseOrderResponse.prototype.setMsgText = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * repeated Order order = 3;
 * @return {!Array<!proto.Order>}
 */
proto.CloseOrderResponse.prototype.getOrderList = function() {
  return /** @type{!Array<!proto.Order>} */ (
    jspb.Message.getRepeatedWrapperField(this, trading_model_pb.Order, 3));
};


/**
 * @param {!Array<!proto.Order>} value
 * @return {!proto.CloseOrderResponse} returns this
*/
proto.CloseOrderResponse.prototype.setOrderList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.Order=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Order}
 */
proto.CloseOrderResponse.prototype.addOrder = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.Order, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.CloseOrderResponse} returns this
 */
proto.CloseOrderResponse.prototype.clearOrderList = function() {
  return this.setOrderList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ModifyOrderRequest.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ModifyOrderRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.ModifyOrderRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ModifyOrderRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ModifyOrderRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    orderList: jspb.Message.toObjectList(msg.getOrderList(),
    trading_model_pb.Order.toObject, includeInstance),
    secretKey: jspb.Message.getFieldWithDefault(msg, 2, ""),
    sessionId: jspb.Message.getFieldWithDefault(msg, 3, ""),
    randomNumber: jspb.Message.getFieldWithDefault(msg, 4, ""),
    hashPassword: jspb.Message.getFieldWithDefault(msg, 5, ""),
    hashPasswordOnly8: jspb.Message.getFieldWithDefault(msg, 6, ""),
    hiddenConfirmFlg: jspb.Message.getBooleanFieldWithDefault(msg, 7, false),
    teamCode: jspb.Message.getFieldWithDefault(msg, 8, ""),
    teamPassword: jspb.Message.getFieldWithDefault(msg, 9, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ModifyOrderRequest}
 */
proto.ModifyOrderRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ModifyOrderRequest;
  return proto.ModifyOrderRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ModifyOrderRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ModifyOrderRequest}
 */
proto.ModifyOrderRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new trading_model_pb.Order;
      reader.readMessage(value,trading_model_pb.Order.deserializeBinaryFromReader);
      msg.addOrder(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSecretKey(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setSessionId(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setRandomNumber(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setHashPassword(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setHashPasswordOnly8(value);
      break;
    case 7:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setHiddenConfirmFlg(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setTeamCode(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setTeamPassword(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ModifyOrderRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ModifyOrderRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ModifyOrderRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ModifyOrderRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOrderList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      trading_model_pb.Order.serializeBinaryToWriter
    );
  }
  f = message.getSecretKey();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSessionId();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getRandomNumber();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getHashPassword();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getHashPasswordOnly8();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getHiddenConfirmFlg();
  if (f) {
    writer.writeBool(
      7,
      f
    );
  }
  f = message.getTeamCode();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getTeamPassword();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
};


/**
 * repeated Order order = 1;
 * @return {!Array<!proto.Order>}
 */
proto.ModifyOrderRequest.prototype.getOrderList = function() {
  return /** @type{!Array<!proto.Order>} */ (
    jspb.Message.getRepeatedWrapperField(this, trading_model_pb.Order, 1));
};


/**
 * @param {!Array<!proto.Order>} value
 * @return {!proto.ModifyOrderRequest} returns this
*/
proto.ModifyOrderRequest.prototype.setOrderList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.Order=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Order}
 */
proto.ModifyOrderRequest.prototype.addOrder = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.Order, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ModifyOrderRequest} returns this
 */
proto.ModifyOrderRequest.prototype.clearOrderList = function() {
  return this.setOrderList([]);
};


/**
 * optional string secret_key = 2;
 * @return {string}
 */
proto.ModifyOrderRequest.prototype.getSecretKey = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ModifyOrderRequest} returns this
 */
proto.ModifyOrderRequest.prototype.setSecretKey = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string session_id = 3;
 * @return {string}
 */
proto.ModifyOrderRequest.prototype.getSessionId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.ModifyOrderRequest} returns this
 */
proto.ModifyOrderRequest.prototype.setSessionId = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string random_number = 4;
 * @return {string}
 */
proto.ModifyOrderRequest.prototype.getRandomNumber = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.ModifyOrderRequest} returns this
 */
proto.ModifyOrderRequest.prototype.setRandomNumber = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string hash_password = 5;
 * @return {string}
 */
proto.ModifyOrderRequest.prototype.getHashPassword = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.ModifyOrderRequest} returns this
 */
proto.ModifyOrderRequest.prototype.setHashPassword = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string hash_password_only8 = 6;
 * @return {string}
 */
proto.ModifyOrderRequest.prototype.getHashPasswordOnly8 = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.ModifyOrderRequest} returns this
 */
proto.ModifyOrderRequest.prototype.setHashPasswordOnly8 = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional bool hidden_confirm_flg = 7;
 * @return {boolean}
 */
proto.ModifyOrderRequest.prototype.getHiddenConfirmFlg = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 7, false));
};


/**
 * @param {boolean} value
 * @return {!proto.ModifyOrderRequest} returns this
 */
proto.ModifyOrderRequest.prototype.setHiddenConfirmFlg = function(value) {
  return jspb.Message.setProto3BooleanField(this, 7, value);
};


/**
 * optional string team_code = 8;
 * @return {string}
 */
proto.ModifyOrderRequest.prototype.getTeamCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/**
 * @param {string} value
 * @return {!proto.ModifyOrderRequest} returns this
 */
proto.ModifyOrderRequest.prototype.setTeamCode = function(value) {
  return jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional string team_password = 9;
 * @return {string}
 */
proto.ModifyOrderRequest.prototype.getTeamPassword = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/**
 * @param {string} value
 * @return {!proto.ModifyOrderRequest} returns this
 */
proto.ModifyOrderRequest.prototype.setTeamPassword = function(value) {
  return jspb.Message.setProto3StringField(this, 9, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ModifyOrderResponse.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ModifyOrderResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.ModifyOrderResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ModifyOrderResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ModifyOrderResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    msgCode: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msgText: jspb.Message.getFieldWithDefault(msg, 2, ""),
    orderList: jspb.Message.toObjectList(msg.getOrderList(),
    trading_model_pb.Order.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ModifyOrderResponse}
 */
proto.ModifyOrderResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ModifyOrderResponse;
  return proto.ModifyOrderResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ModifyOrderResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ModifyOrderResponse}
 */
proto.ModifyOrderResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.MsgCode} */ (reader.readEnum());
      msg.setMsgCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsgText(value);
      break;
    case 3:
      var value = new trading_model_pb.Order;
      reader.readMessage(value,trading_model_pb.Order.deserializeBinaryFromReader);
      msg.addOrder(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ModifyOrderResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ModifyOrderResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ModifyOrderResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ModifyOrderResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMsgCode();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getMsgText();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getOrderList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      trading_model_pb.Order.serializeBinaryToWriter
    );
  }
};


/**
 * optional MsgCode msg_code = 1;
 * @return {!proto.MsgCode}
 */
proto.ModifyOrderResponse.prototype.getMsgCode = function() {
  return /** @type {!proto.MsgCode} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.MsgCode} value
 * @return {!proto.ModifyOrderResponse} returns this
 */
proto.ModifyOrderResponse.prototype.setMsgCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional string msg_text = 2;
 * @return {string}
 */
proto.ModifyOrderResponse.prototype.getMsgText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.ModifyOrderResponse} returns this
 */
proto.ModifyOrderResponse.prototype.setMsgText = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * repeated Order order = 3;
 * @return {!Array<!proto.Order>}
 */
proto.ModifyOrderResponse.prototype.getOrderList = function() {
  return /** @type{!Array<!proto.Order>} */ (
    jspb.Message.getRepeatedWrapperField(this, trading_model_pb.Order, 3));
};


/**
 * @param {!Array<!proto.Order>} value
 * @return {!proto.ModifyOrderResponse} returns this
*/
proto.ModifyOrderResponse.prototype.setOrderList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.Order=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Order}
 */
proto.ModifyOrderResponse.prototype.addOrder = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.Order, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ModifyOrderResponse} returns this
 */
proto.ModifyOrderResponse.prototype.clearOrderList = function() {
  return this.setOrderList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.CancelOrderRequest.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.CancelOrderRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.CancelOrderRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.CancelOrderRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CancelOrderRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    orderList: jspb.Message.toObjectList(msg.getOrderList(),
    trading_model_pb.Order.toObject, includeInstance),
    secretKey: jspb.Message.getFieldWithDefault(msg, 2, ""),
    sessionId: jspb.Message.getFieldWithDefault(msg, 3, ""),
    randomNumber: jspb.Message.getFieldWithDefault(msg, 4, ""),
    hashPassword: jspb.Message.getFieldWithDefault(msg, 5, ""),
    hashPasswordOnly8: jspb.Message.getFieldWithDefault(msg, 6, ""),
    hiddenConfirmFlg: jspb.Message.getBooleanFieldWithDefault(msg, 7, false),
    teamCode: jspb.Message.getFieldWithDefault(msg, 8, ""),
    teamPassword: jspb.Message.getFieldWithDefault(msg, 9, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.CancelOrderRequest}
 */
proto.CancelOrderRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.CancelOrderRequest;
  return proto.CancelOrderRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.CancelOrderRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.CancelOrderRequest}
 */
proto.CancelOrderRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new trading_model_pb.Order;
      reader.readMessage(value,trading_model_pb.Order.deserializeBinaryFromReader);
      msg.addOrder(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSecretKey(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setSessionId(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setRandomNumber(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setHashPassword(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setHashPasswordOnly8(value);
      break;
    case 7:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setHiddenConfirmFlg(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setTeamCode(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setTeamPassword(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.CancelOrderRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.CancelOrderRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.CancelOrderRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CancelOrderRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOrderList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      trading_model_pb.Order.serializeBinaryToWriter
    );
  }
  f = message.getSecretKey();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSessionId();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getRandomNumber();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getHashPassword();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getHashPasswordOnly8();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getHiddenConfirmFlg();
  if (f) {
    writer.writeBool(
      7,
      f
    );
  }
  f = message.getTeamCode();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getTeamPassword();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
};


/**
 * repeated Order order = 1;
 * @return {!Array<!proto.Order>}
 */
proto.CancelOrderRequest.prototype.getOrderList = function() {
  return /** @type{!Array<!proto.Order>} */ (
    jspb.Message.getRepeatedWrapperField(this, trading_model_pb.Order, 1));
};


/**
 * @param {!Array<!proto.Order>} value
 * @return {!proto.CancelOrderRequest} returns this
*/
proto.CancelOrderRequest.prototype.setOrderList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.Order=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Order}
 */
proto.CancelOrderRequest.prototype.addOrder = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.Order, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.CancelOrderRequest} returns this
 */
proto.CancelOrderRequest.prototype.clearOrderList = function() {
  return this.setOrderList([]);
};


/**
 * optional string secret_key = 2;
 * @return {string}
 */
proto.CancelOrderRequest.prototype.getSecretKey = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.CancelOrderRequest} returns this
 */
proto.CancelOrderRequest.prototype.setSecretKey = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string session_id = 3;
 * @return {string}
 */
proto.CancelOrderRequest.prototype.getSessionId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.CancelOrderRequest} returns this
 */
proto.CancelOrderRequest.prototype.setSessionId = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string random_number = 4;
 * @return {string}
 */
proto.CancelOrderRequest.prototype.getRandomNumber = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.CancelOrderRequest} returns this
 */
proto.CancelOrderRequest.prototype.setRandomNumber = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string hash_password = 5;
 * @return {string}
 */
proto.CancelOrderRequest.prototype.getHashPassword = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.CancelOrderRequest} returns this
 */
proto.CancelOrderRequest.prototype.setHashPassword = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string hash_password_only8 = 6;
 * @return {string}
 */
proto.CancelOrderRequest.prototype.getHashPasswordOnly8 = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.CancelOrderRequest} returns this
 */
proto.CancelOrderRequest.prototype.setHashPasswordOnly8 = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional bool hidden_confirm_flg = 7;
 * @return {boolean}
 */
proto.CancelOrderRequest.prototype.getHiddenConfirmFlg = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 7, false));
};


/**
 * @param {boolean} value
 * @return {!proto.CancelOrderRequest} returns this
 */
proto.CancelOrderRequest.prototype.setHiddenConfirmFlg = function(value) {
  return jspb.Message.setProto3BooleanField(this, 7, value);
};


/**
 * optional string team_code = 8;
 * @return {string}
 */
proto.CancelOrderRequest.prototype.getTeamCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/**
 * @param {string} value
 * @return {!proto.CancelOrderRequest} returns this
 */
proto.CancelOrderRequest.prototype.setTeamCode = function(value) {
  return jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional string team_password = 9;
 * @return {string}
 */
proto.CancelOrderRequest.prototype.getTeamPassword = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/**
 * @param {string} value
 * @return {!proto.CancelOrderRequest} returns this
 */
proto.CancelOrderRequest.prototype.setTeamPassword = function(value) {
  return jspb.Message.setProto3StringField(this, 9, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.CancelOrderResponse.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.CancelOrderResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.CancelOrderResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.CancelOrderResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CancelOrderResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    msgCode: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msgText: jspb.Message.getFieldWithDefault(msg, 2, ""),
    orderList: jspb.Message.toObjectList(msg.getOrderList(),
    trading_model_pb.Order.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.CancelOrderResponse}
 */
proto.CancelOrderResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.CancelOrderResponse;
  return proto.CancelOrderResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.CancelOrderResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.CancelOrderResponse}
 */
proto.CancelOrderResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.MsgCode} */ (reader.readEnum());
      msg.setMsgCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsgText(value);
      break;
    case 3:
      var value = new trading_model_pb.Order;
      reader.readMessage(value,trading_model_pb.Order.deserializeBinaryFromReader);
      msg.addOrder(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.CancelOrderResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.CancelOrderResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.CancelOrderResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CancelOrderResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMsgCode();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getMsgText();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getOrderList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      trading_model_pb.Order.serializeBinaryToWriter
    );
  }
};


/**
 * optional MsgCode msg_code = 1;
 * @return {!proto.MsgCode}
 */
proto.CancelOrderResponse.prototype.getMsgCode = function() {
  return /** @type {!proto.MsgCode} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.MsgCode} value
 * @return {!proto.CancelOrderResponse} returns this
 */
proto.CancelOrderResponse.prototype.setMsgCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional string msg_text = 2;
 * @return {string}
 */
proto.CancelOrderResponse.prototype.getMsgText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.CancelOrderResponse} returns this
 */
proto.CancelOrderResponse.prototype.setMsgText = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * repeated Order order = 3;
 * @return {!Array<!proto.Order>}
 */
proto.CancelOrderResponse.prototype.getOrderList = function() {
  return /** @type{!Array<!proto.Order>} */ (
    jspb.Message.getRepeatedWrapperField(this, trading_model_pb.Order, 3));
};


/**
 * @param {!Array<!proto.Order>} value
 * @return {!proto.CancelOrderResponse} returns this
*/
proto.CancelOrderResponse.prototype.setOrderList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.Order=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Order}
 */
proto.CancelOrderResponse.prototype.addOrder = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.Order, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.CancelOrderResponse} returns this
 */
proto.CancelOrderResponse.prototype.clearOrderList = function() {
  return this.setOrderList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.OrderEvent.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.OrderEvent.prototype.toObject = function(opt_includeInstance) {
  return proto.OrderEvent.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.OrderEvent} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.OrderEvent.toObject = function(includeInstance, msg) {
  var f, obj = {
    orderList: jspb.Message.toObjectList(msg.getOrderList(),
    trading_model_pb.Order.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.OrderEvent}
 */
proto.OrderEvent.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.OrderEvent;
  return proto.OrderEvent.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.OrderEvent} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.OrderEvent}
 */
proto.OrderEvent.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new trading_model_pb.Order;
      reader.readMessage(value,trading_model_pb.Order.deserializeBinaryFromReader);
      msg.addOrder(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.OrderEvent.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.OrderEvent.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.OrderEvent} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.OrderEvent.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOrderList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      trading_model_pb.Order.serializeBinaryToWriter
    );
  }
};


/**
 * repeated Order order = 1;
 * @return {!Array<!proto.Order>}
 */
proto.OrderEvent.prototype.getOrderList = function() {
  return /** @type{!Array<!proto.Order>} */ (
    jspb.Message.getRepeatedWrapperField(this, trading_model_pb.Order, 1));
};


/**
 * @param {!Array<!proto.Order>} value
 * @return {!proto.OrderEvent} returns this
*/
proto.OrderEvent.prototype.setOrderList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.Order=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Order}
 */
proto.OrderEvent.prototype.addOrder = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.Order, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.OrderEvent} returns this
 */
proto.OrderEvent.prototype.clearOrderList = function() {
  return this.setOrderList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.TradeEvent.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.TradeEvent.prototype.toObject = function(opt_includeInstance) {
  return proto.TradeEvent.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.TradeEvent} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.TradeEvent.toObject = function(includeInstance, msg) {
  var f, obj = {
    tradeList: jspb.Message.toObjectList(msg.getTradeList(),
    trading_model_pb.Trade.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.TradeEvent}
 */
proto.TradeEvent.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.TradeEvent;
  return proto.TradeEvent.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.TradeEvent} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.TradeEvent}
 */
proto.TradeEvent.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new trading_model_pb.Trade;
      reader.readMessage(value,trading_model_pb.Trade.deserializeBinaryFromReader);
      msg.addTrade(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.TradeEvent.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.TradeEvent.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.TradeEvent} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.TradeEvent.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTradeList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      trading_model_pb.Trade.serializeBinaryToWriter
    );
  }
};


/**
 * repeated Trade trade = 1;
 * @return {!Array<!proto.Trade>}
 */
proto.TradeEvent.prototype.getTradeList = function() {
  return /** @type{!Array<!proto.Trade>} */ (
    jspb.Message.getRepeatedWrapperField(this, trading_model_pb.Trade, 1));
};


/**
 * @param {!Array<!proto.Trade>} value
 * @return {!proto.TradeEvent} returns this
*/
proto.TradeEvent.prototype.setTradeList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.Trade=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Trade}
 */
proto.TradeEvent.prototype.addTrade = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.Trade, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.TradeEvent} returns this
 */
proto.TradeEvent.prototype.clearTradeList = function() {
  return this.setTradeList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.SubscribeTradeEventRequest.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.SubscribeTradeEventRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.SubscribeTradeEventRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.SubscribeTradeEventRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.SubscribeTradeEventRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    symbolCodeList: (f = jspb.Message.getRepeatedField(msg, 1)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.SubscribeTradeEventRequest}
 */
proto.SubscribeTradeEventRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.SubscribeTradeEventRequest;
  return proto.SubscribeTradeEventRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.SubscribeTradeEventRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.SubscribeTradeEventRequest}
 */
proto.SubscribeTradeEventRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.addSymbolCode(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.SubscribeTradeEventRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.SubscribeTradeEventRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.SubscribeTradeEventRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.SubscribeTradeEventRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSymbolCodeList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      1,
      f
    );
  }
};


/**
 * repeated string symbol_code = 1;
 * @return {!Array<string>}
 */
proto.SubscribeTradeEventRequest.prototype.getSymbolCodeList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 1));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.SubscribeTradeEventRequest} returns this
 */
proto.SubscribeTradeEventRequest.prototype.setSymbolCodeList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.SubscribeTradeEventRequest} returns this
 */
proto.SubscribeTradeEventRequest.prototype.addSymbolCode = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.SubscribeTradeEventRequest} returns this
 */
proto.SubscribeTradeEventRequest.prototype.clearSymbolCodeList = function() {
  return this.setSymbolCodeList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.SubscribeTradeEventResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.SubscribeTradeEventResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.SubscribeTradeEventResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.SubscribeTradeEventResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    msgCode: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msgText: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.SubscribeTradeEventResponse}
 */
proto.SubscribeTradeEventResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.SubscribeTradeEventResponse;
  return proto.SubscribeTradeEventResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.SubscribeTradeEventResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.SubscribeTradeEventResponse}
 */
proto.SubscribeTradeEventResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.MsgCode} */ (reader.readEnum());
      msg.setMsgCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsgText(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.SubscribeTradeEventResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.SubscribeTradeEventResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.SubscribeTradeEventResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.SubscribeTradeEventResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMsgCode();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getMsgText();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional MsgCode msg_code = 1;
 * @return {!proto.MsgCode}
 */
proto.SubscribeTradeEventResponse.prototype.getMsgCode = function() {
  return /** @type {!proto.MsgCode} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.MsgCode} value
 * @return {!proto.SubscribeTradeEventResponse} returns this
 */
proto.SubscribeTradeEventResponse.prototype.setMsgCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional string msg_text = 2;
 * @return {string}
 */
proto.SubscribeTradeEventResponse.prototype.getMsgText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.SubscribeTradeEventResponse} returns this
 */
proto.SubscribeTradeEventResponse.prototype.setMsgText = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.UnsubscribeTradeEventRequest.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.UnsubscribeTradeEventRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.UnsubscribeTradeEventRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.UnsubscribeTradeEventRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.UnsubscribeTradeEventRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    symbolCodeList: (f = jspb.Message.getRepeatedField(msg, 1)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.UnsubscribeTradeEventRequest}
 */
proto.UnsubscribeTradeEventRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.UnsubscribeTradeEventRequest;
  return proto.UnsubscribeTradeEventRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.UnsubscribeTradeEventRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.UnsubscribeTradeEventRequest}
 */
proto.UnsubscribeTradeEventRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.addSymbolCode(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.UnsubscribeTradeEventRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.UnsubscribeTradeEventRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.UnsubscribeTradeEventRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.UnsubscribeTradeEventRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSymbolCodeList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      1,
      f
    );
  }
};


/**
 * repeated string symbol_code = 1;
 * @return {!Array<string>}
 */
proto.UnsubscribeTradeEventRequest.prototype.getSymbolCodeList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 1));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.UnsubscribeTradeEventRequest} returns this
 */
proto.UnsubscribeTradeEventRequest.prototype.setSymbolCodeList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.UnsubscribeTradeEventRequest} returns this
 */
proto.UnsubscribeTradeEventRequest.prototype.addSymbolCode = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.UnsubscribeTradeEventRequest} returns this
 */
proto.UnsubscribeTradeEventRequest.prototype.clearSymbolCodeList = function() {
  return this.setSymbolCodeList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.UnsubscribeTradeEventResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.UnsubscribeTradeEventResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.UnsubscribeTradeEventResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.UnsubscribeTradeEventResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    msgCode: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msgText: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.UnsubscribeTradeEventResponse}
 */
proto.UnsubscribeTradeEventResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.UnsubscribeTradeEventResponse;
  return proto.UnsubscribeTradeEventResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.UnsubscribeTradeEventResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.UnsubscribeTradeEventResponse}
 */
proto.UnsubscribeTradeEventResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.MsgCode} */ (reader.readEnum());
      msg.setMsgCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsgText(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.UnsubscribeTradeEventResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.UnsubscribeTradeEventResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.UnsubscribeTradeEventResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.UnsubscribeTradeEventResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMsgCode();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getMsgText();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional MsgCode msg_code = 1;
 * @return {!proto.MsgCode}
 */
proto.UnsubscribeTradeEventResponse.prototype.getMsgCode = function() {
  return /** @type {!proto.MsgCode} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.MsgCode} value
 * @return {!proto.UnsubscribeTradeEventResponse} returns this
 */
proto.UnsubscribeTradeEventResponse.prototype.setMsgCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional string msg_text = 2;
 * @return {string}
 */
proto.UnsubscribeTradeEventResponse.prototype.getMsgText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.UnsubscribeTradeEventResponse} returns this
 */
proto.UnsubscribeTradeEventResponse.prototype.setMsgText = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.UpdateOrderStatusRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.UpdateOrderStatusRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.UpdateOrderStatusRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.UpdateOrderStatusRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    order: (f = msg.getOrder()) && trading_model_pb.Order.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.UpdateOrderStatusRequest}
 */
proto.UpdateOrderStatusRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.UpdateOrderStatusRequest;
  return proto.UpdateOrderStatusRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.UpdateOrderStatusRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.UpdateOrderStatusRequest}
 */
proto.UpdateOrderStatusRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new trading_model_pb.Order;
      reader.readMessage(value,trading_model_pb.Order.deserializeBinaryFromReader);
      msg.setOrder(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.UpdateOrderStatusRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.UpdateOrderStatusRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.UpdateOrderStatusRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.UpdateOrderStatusRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOrder();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      trading_model_pb.Order.serializeBinaryToWriter
    );
  }
};


/**
 * optional Order order = 1;
 * @return {?proto.Order}
 */
proto.UpdateOrderStatusRequest.prototype.getOrder = function() {
  return /** @type{?proto.Order} */ (
    jspb.Message.getWrapperField(this, trading_model_pb.Order, 1));
};


/**
 * @param {?proto.Order|undefined} value
 * @return {!proto.UpdateOrderStatusRequest} returns this
*/
proto.UpdateOrderStatusRequest.prototype.setOrder = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.UpdateOrderStatusRequest} returns this
 */
proto.UpdateOrderStatusRequest.prototype.clearOrder = function() {
  return this.setOrder(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.UpdateOrderStatusRequest.prototype.hasOrder = function() {
  return jspb.Message.getField(this, 1) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.UpdateOrderStatusResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.UpdateOrderStatusResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.UpdateOrderStatusResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.UpdateOrderStatusResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    msgCode: jspb.Message.getFieldWithDefault(msg, 2, 0),
    msgText: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.UpdateOrderStatusResponse}
 */
proto.UpdateOrderStatusResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.UpdateOrderStatusResponse;
  return proto.UpdateOrderStatusResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.UpdateOrderStatusResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.UpdateOrderStatusResponse}
 */
proto.UpdateOrderStatusResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 2:
      var value = /** @type {!proto.MsgCode} */ (reader.readEnum());
      msg.setMsgCode(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsgText(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.UpdateOrderStatusResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.UpdateOrderStatusResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.UpdateOrderStatusResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.UpdateOrderStatusResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMsgCode();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
  f = message.getMsgText();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional MsgCode msg_code = 2;
 * @return {!proto.MsgCode}
 */
proto.UpdateOrderStatusResponse.prototype.getMsgCode = function() {
  return /** @type {!proto.MsgCode} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.MsgCode} value
 * @return {!proto.UpdateOrderStatusResponse} returns this
 */
proto.UpdateOrderStatusResponse.prototype.setMsgCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};


/**
 * optional string msg_text = 3;
 * @return {string}
 */
proto.UpdateOrderStatusResponse.prototype.getMsgText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.UpdateOrderStatusResponse} returns this
 */
proto.UpdateOrderStatusResponse.prototype.setMsgText = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


goog.object.extend(exports, proto);
