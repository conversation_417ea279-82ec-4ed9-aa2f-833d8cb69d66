﻿import { e2ee_rng_seed_time } from "./rng_jso";

function fnIsLoaded(rsa) {
    try {
        if (typeof (rsa.encryptPIN1) == 'undefined') {
            return false;
        } else {
            return true;
        }
    } catch (error) {
        return false;
    }
}

export function fnGetRPIN(spin, rsa) {
    e2ee_rng_seed_time();
    var pin = spin.value

    var browser_ver = navigator.userAgent
    var foundflg = browser_ver.search("Mozilla/4.7")
    spin.value = "";

    if (navigator.appName == "Netscape" && foundflg != -1) {
        return rsa.encryptPIN1(pin);
    } else {
        return fnIEGetRPIN(pin, rsa)
    }
}

function fnIEGetRPIN(pin,rsa) {
    try {
        return rsa.encryptPIN1(pin);
    } catch (error) {
        alert(error);
        return false
    }
}

function fnGetRPINChg(sold_pin, snew_pin, scon_pin, rsa) {
    e2ee_rng_seed_time();
    var old_pin = sold_pin.value
    var new_pin = snew_pin.value
    var con_pin = scon_pin.value
    var browser_ver = navigator.userAgent
    var foundflg = browser_ver.search("Mozilla/4.7")

    if (navigator.appName == "Netscape" && foundflg != -1) {
        return rsa.encryptPIN2(old_pin, new_pin, rsa);
    } else {
        return fnIEGetRPINChg(old_pin, new_pin, rsa)
    }
}

function fnIEGetRPINChg(old_pin, new_pin, rsa) {
    try {
        return rsa.encryptPIN2(old_pin, new_pin);
    } catch (error) {
        alert(error.description);
        return false
    }
}