syntax = "proto3";

message Band {
  string price = 1;
  string volume = 2; // total volume
  bool tradable = 3;
  int64 num_orders = 4; // total num orders

  int64 retail_volume = 5; // Retail volume
  int64 retail_num_orders = 6; // Retail num orders

  int64 lp_volume = 7; // LP volume
  int64 lp_num_orders = 8; // LP num orders
}

message Quote {
  int64 id = 1;
  int32 symbol_id = 2;
  string symbol_code = 3;
  repeated Band asks = 4;
  repeated Band bids = 5;
  string low = 6; // lp lowest price 
  string high = 7; // lp highest price 
  string open = 8; // lp open price 
  string close = 9; // lp Last price
  string retail_low = 10; // retail lowest price 
  string retail_high = 11; // retail highest price 
  string retail_open = 12; // retail open price 
  string retail_close = 13; // retail Last price
  string net_change = 14;
  string pct_change = 15;
  int32 scale = 16;
  int64 quote_time = 17; // Timestamp in millisecond
  int32 tick_per_day = 18;
  string volume_per_day = 19;
  string current_price = 20;   // LP market price
  string retail_price = 21; // Retail market price
  RouteQuote route = 22; // Quote from Refinitiv or ICE or USAH
}


message Chart {
  int64 id = 1;
  int32 symbol_id = 2;
  string symbol_code = 3;

  //TODO
}

enum RouteQuote {
	QUOTE_NONE      = 0;
	QUOTE_USAH      = 1;
	QUOTE_REFINITIV = 2;
	QUOTE_ICE		= 3;
}
