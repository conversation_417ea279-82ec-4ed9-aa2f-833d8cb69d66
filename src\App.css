#root {
  -webkit-touch-callout: none;
}

.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.site-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 99;
}

.site-main {
  margin-top: 74px;
}

.fix-line-css {
  display: block !important;
  display: -webkit-box;
  max-width: 400px;
  margin: 0 auto;
  font-size: 16px;
  line-height: 1.3;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--cus-text-primary);
  max-height: 20px;
}

/* width */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/* Track */
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px transparent;
  border-radius: 20px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: var(--cus-webkit) !important;
  border: 1px solid rgba(255, 255, 255, 0.13);
  border-radius: 10px;
}

::-webkit-scrollbar-corner {
  background: var(--cus-webkit) !important;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: var(--cus-webkit) !important;
}

.mw-all {
  max-width: calc(100% - 115px);
}

.fs-12 {
  font-size: 12px;
}

.left-280 {
  left: 280px;
}

.top-2 {
  top: 2px;
}
.post-active {
  background: rgba(108,117,125,0.5) !important;
}

.heigh_header {
  height: 74px;
}

.header_logo {
  position: absolute;
  width: 98px;
  height: 17px;
  left: 14px;
  top: 29px;

  font-family: 'Times New Roman';
  font-style: normal;
  font-weight: 700;
  font-size: 15px;
  line-height: 17px;

  color: #FFA13E;
}

@media (max-width: 800px) {
  .header_logo{
    display: none;
  }
}

.detail-news {
  max-height: 590px;
  border-radius: 4px;
}
.mh-news {
  max-height: 650px;
  overflow: auto;
}

.mh-detail {
  max-height: 562px;
  overflow: auto;
}

.post-header .btn-close {
  position: absolute;
  top: 16px !important;
  right: 16px !important;
}
.text-color-blue {
  color: #0066FF !important;
}
.modal-error .modal-dialog {
  max-width: 15rem;
  margin: auto;
}

.count {
  background-color: #f08122;
  border-radius: 2rem;
  color: #fff;
  font-size: .625rem;
  line-height: 1;
  margin-left: -.4rem;
  padding: 0 .25rem;
  top: -10px;
  left: 10px;
}

.menu-accounce-mobile {
  display: -webkit-box !important;
}

.count-mobile {
  vertical-align: bottom;
  padding: 0.1rem 0.25rem;
  top: -5px;
  left: -8px;
}

.w-80 {
  width: 80px !important;
}

.popup_login .modal .modal-dialog .modal-content {
  background-color: transparent ;
  align-items: center;
}

.popup_login .modal.show .modal-dialog {
  transform: translateY(-50%);
  top: 50%;
  left: 0;
}


.popup_container {
  top: 50%;
  left: 50%;
  width: 241px;
  height: 162px;

  background-color: var(--cus-bg-primary);
  border-radius: 10px;

  display: flex;
  flex-direction: column;
  justify-content: space-between;
  text-align: center;
  z-index: 1;
}

.popup_title {
  font-weight: 400;
  font-size: 16px;
  line-height: 20px;
  margin: auto;
  padding: 0 8px;
}

.popup_btn {
  background-color: var(--cus-bg-primary);
  color: var(--cus-text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 0;
  border: none;
  border-top: 1px solid #ccc;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}
.modal-notify {
  width: 21cm;
  margin: 1cm auto;
  border-radius: 5px;
  background: #1F2538;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}
@modal-notify {
  size: A4;
  margin: 0;
}

@media print {
  .modal-notify {
    margin: 0;
    border: initial;
    border-radius: initial;
    width: initial;
    min-height: initial;
    box-shadow: initial;
    background: initial;
    page-break-after: always;
  }
}
.notify-mobile {
  width: 100% !important;
}

.style-popup {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 17px;
  text-align: justify;

  color: var(--cus-txt-acknown);
}
.br-25 {
  border-radius: 5rem !important;
}

.ml-px-100 {
  margin-left: 100px !important;
}

.div-freeze {
  max-height: 81vh;
  width: 100%;
  overflow: scroll;
}

.div-freeze th {
  width: 250px;
  position: sticky !important;
  top: 0 !important;
}

.div-freeze td {
  width: 250px;
}

.div-freeze th:nth-child(1),
.div-freeze td:nth-child(1) {
    position: sticky;
    left: -1px;
    width: 250px;
    min-width: 190px;
}

.div-freeze th:nth-child(2),
.div-freeze td:nth-child(2) {
  position: sticky;
  min-width: 80px;
}

.div-freeze th:nth-child(2),
.div-freeze td:nth-child(1) {
  z-index: 2;
}

.div-freeze td:nth-child(2) {
  z-index: 1;
}

.div-freeze th:nth-child(1):hover,
.div-freeze th:nth-child(2):hover {
  color: #D27621;
}

.text-orange {
  color: #D27621;
}

.div-freeze td:nth-child(1) {
    background: var(--cus-bg-table);
}

.div-freeze th:nth-child(1) {
    z-index: 3;
}

.div-freeze-mobile {
  width: 100%;
  overflow: scroll;
}

.div-freeze-mobile th {
  width: 250px;
  position: sticky !important;
  top: 0 !important;
}

.div-freeze-mobile td {
  width: 250px;
}

.div-freeze-mobile th:nth-child(1),
.div-freeze-mobile td:nth-child(1) {
    position: sticky;
    left: -1px;
    width: 250px;
    min-width: 190px;
}

.div-freeze-mobile th:nth-child(2),
.div-freeze-mobile td:nth-child(2) {
  position: sticky;
  min-width: 80px;
}

.div-freeze-mobile th:nth-child(2),
.div-freeze-mobile td:nth-child(1) {
  z-index: 2;
}

.div-freeze-mobile td:nth-child(2) {
  z-index: 1;
}

.div-freeze-mobile td:nth-child(1) {
    background: var(--cus-bg-table);
}

.div-freeze-mobile th:nth-child(1) {
    z-index: 3;
}

.color_mobile {
  color: #000 !important;
}

.active_mobile {
  color: #1fa2ff !important; 
  border-radius: 2px;
}
.link_announcement {
  display: flex;
  align-items: center;
}
.password {
  -webkit-text-security: disc;
}
.ml-rem {
  margin-left: -1.5rem;
}
.w-50-pre {
  width: 50%;
}

.scroll-none {
  overflow: hidden !important;
}

input::-ms-clear, input::-ms-reveal {
  display: none;
}
.switch-button-custom{
    background: rgba(0, 0, 0, 0.5);
    background-color: var(--cus-bg-panel);
    border-radius: 2rem;
    border: 1px solid var(--cus-bg-secondary);
    overflow: hidden;
    width: 100%;
    height: 32px;
    color: #fff;
    position: relative;
    padding-right: 50%;
}
.buy-btn{
    height: 100%;
    width: 100%;
    font-size: 16px;
    color: #fff;
    border: none;
    border-radius: 2rem;
    transform: translateX(0);
    
}
.sell-btn{
  height: 100%;
  width: 50%;
  font-size: 16px;
  color: #fff;
  border: none;
  border-radius: 2rem;
  transform: translateX(100%);
  position: absolute;
  left: 0;
  top: 0;
}

.bg-main {
  background-color: var(--cus-bg-panel);
}

.cursor-pointer-header {
  cursor: pointer;
  transition: ease-in-out .2s;
}

.cursor-pointer-header:hover {
  color: #F08122;
}

.fz-10 {
  font-size: 10px !important;
}

.fs-px-11{
  font-size: 11px;
}

.annoucement-notice *{
  font-size: 1rem !important;
}

.link-btn {
  font-weight: bolder;
  color: var(--toastify-color-info);
  text-decoration: underline;
  cursor: pointer;
}

.pointer:hover {
  font-weight: bolder;
  color: var(--toastify-color-info);
  text-decoration: underline;
  cursor: pointer;
}

.part-done {
  color: #00D796 !important;
}

.done {
  color: #66B55D !important;
}

.button-amend {
  width: 95px;
  height: 41px;
  border-radius: 35px;
  padding: 10px, 20px, 10px, 20px;
  gap: 10px;
  background-color: #D27621;
  color: #fff;
  border: none;
}

.button-withdraw {
  width: 125px;
  height: 41px;
  border-radius: 35px;
  padding: 10px, 20px, 10px, 20px;
  gap: 10px;
  background-color: #B71010;
  color: #fff;
  border: none;
}

.h-px-380 {
  height: 380px;
}

.text-amend {
  color: #D27621 !important;
}

.btn-multiCalcel {
  background: inherit;
  color: var(--cus-normal-text);
  border: none;
}

.mw-px-100 {
  min-width: 100px;
}

.text-status{
  color: #F78F20;
  cursor: pointer;
  transition: ease-in-out .2s;
}
.text-status:hover {
  color: #468efa;
}

.selectdiv select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  
  display: block;
  width: 100%;
  padding: 0px 24px !important;
  font-size: 16px;
  line-height: 1.75;
  -ms-word-break: normal;
  word-break: normal;
  border-radius: 0% !important;
}

.selectdiv select option {
  color: var(--cus-normal-text);
  background: var(--cus-bg-secondary);
}

.pl-px-8 {
  padding-left: 8px !important;
}

.theme-text{
  color: var(--cus-text-primary);
}
.theme-text:hover, .theme-text:focus{
  color: var(--cus-text-primary);
}

.card{
  background-color: var(--cus-bg-primary);
}

.cus-border{
  border-left: 1px solid var(--cus-border);
  border-right: 1px solid var(--cus-border);
}
.cus-border thead {
  border-bottom: 1px solid var(--cus-border);
}

.input-search-order input::placeholder{
  font-weight: 600;
  color: var(--cus-text-primary);
}
.input-search-order input:focus{
  font-weight: 600;
  color: var(--cus-text-primary);
}
.header-order-status{
  background-color: var(--cus-bg-header);
}

.form-select option{
  padding: 20px !important;
}
.form-select select option{
  padding: 20px !important;
  margin: 20px !important;
}
.bi-list{
  color: var(--cus-text-primary);
}
.offcanvas-body .nav-item{
  border-bottom: 1px solid red;
}

.ml-5 {
  margin-left: 5px !important;
}

.hyper-link {
  cursor: pointer;
}


.box-amend {
  width: 70px;
  height: 33px;
  margin-bottom: 15px;
}

.box-withdraw {
  width: 90px;
  height: 33px;
  margin-bottom: 15px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-15 {
  margin-top: 15px;
}

.mt-14 {
  margin-top: -14px;
}

.ml-30 {
  margin-left: -30px;
}

.mt-5px { 
  margin-top: 5px;
}

.border-top {
  border-top: 1px solid rgb(204, 204, 204);
}

.l-90 {
  left: 90%;
}

.ticker-search .MuiAutocomplete-input {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.ticker-search .MuiAutocomplete-inputRoot,
.ticker-search .MuiButtonBase-root  {
  color: var(--cus-text-primary);
}

.ticker-search .MuiInputBase-root{
  border-radius: 0;
  border-bottom: 1px solid gray;
  border-top: none !important;
  padding: 0;
  font-weight: bold;
}

.ticker-search .MuiInputBase-root input::placeholder{
  color: var(--cus-text-primary) !important;
  opacity: 1 !important;
}

.ticker-search .MuiOutlinedInput-notchedOutline{
  border: none !important;
}

.ticker-search .MuiOutlinedInput-root{
  padding-top: 0 !important;
  padding-bottom: 3.5px !important;
}

.MuiAutocomplete-popper .MuiAutocomplete-option:hover,
.MuiAutocomplete-popper .MuiAutocomplete-option[aria-selected='true']{
  background-color: #0B67BE !important;
  color: #fff !important;
}

.MuiAutocomplete-option.Mui-focusVisible,.MuiAutocomplete-option.Mui-focused {
  background-color: #0B67BE !important;
  color: #fff !important;
}

.paper{
  color: var(--cus-text-primary) !important;
  background: var(--cus-bg-secondary) !important;
  font-weight: 500;
}