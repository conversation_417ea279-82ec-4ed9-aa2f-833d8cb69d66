/* Include Fonts */
@font-face {
  font-family: 'Roboto';
  src: url('../fonts/Roboto-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Roboto';
  src: url('../fonts/Roboto-Italic.ttf') format('truetype');
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'Roboto';
  src: url('../fonts/Roboto-Bold.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
}

/*-------------------
BODY 
--------------------*/
body {
  font-family: 'Roboto', sans-serif;
  font-size: 0.875rem;
  background-color: var(--cus-bg-primary);
  color: var(--cus-text-primary);
}

strong {
  font-weight: 700;
}

.table {
  color: var(--cus-text-primary);
  background-color: var(--cus-bg-table);
}

.table thead tr th {
  color: var(--cus-text-primary);
  background-color: var(--cus-bg-head-table);
}

.table> :not(:first-child) {
  border-top: 0;
}

.table-striped > tbody > tr:nth-of-type(odd) > * {
  /* --bs-table-accent-bg: var(--bs-table-striped-bg); */
  background-color: var(--cus-bg-primary);
  color: var(--cus-text-primary);
}

/* Switch Button */
.switch-button {
  background: rgba(0, 0, 0, 0.5);
  border-radius: 2rem;
  overflow: hidden;
  width: 100%;
  text-align: center;
  letter-spacing: 0;
  color: #fff;
  position: relative;
  padding-right: 50%;
  position: relative;
  text-transform: uppercase;
}

.switch-button:before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  width: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  z-index: 3;
  pointer-events: none;
}

.switch-button.order-for {
  font-size: 1rem;
}

.switch-button.order-for:before {
  content: 'Sell';
}

.switch-button-checkbox {
  cursor: pointer;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  z-index: 2;
}

.switch-button-checkbox:checked+.switch-button-label:before {
  -webkit-transform: translateX(100%);
  transform: translateX(100%);
  -webkit-transition: -webkit-transform 300ms linear;
  transition: -webkit-transform 300ms linear;
  transition: transform 300ms linear;
  transition: transform 300ms linear, -webkit-transform 300ms linear;
  background: #EF2828;
}

.switch-button-checkbox+.switch-button-label {
  position: relative;
  padding: 0.25rem 0;
  display: block;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  pointer-events: none;
}

.switch-button-checkbox+.switch-button-label:before {
  content: "";
  background: #009368;
  height: 100%;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
  border-radius: 2rem;
  -webkit-transform: translateX(0);
  transform: translateX(0);
  -webkit-transition: -webkit-transform 300ms;
  transition: -webkit-transform 300ms;
  transition: transform 300ms;
  transition: transform 300ms, -webkit-transform 300ms;
}

.switch-button-checkbox+.switch-button-label .switch-button-label-span {
  position: relative;
}

.btn-group.btn-group-switch {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 2rem;
}

.btn-group.btn-group-switch .btn-outline-info {
  border-color: rgba(0, 0, 0, 0);
  background-color: rgba(0, 0, 0, 0);
  color: rgba(255, 255, 255, 0.75);
}

.btn-group.btn-group-switch .btn-check:checked+.btn-outline-info {
  background-color: #0B67BE;
  border-color: #0B67BE;
  border-radius: 2rem;
  color: #fff;
}

@media (min-width: 576px) {
  .modal-tiny {
    max-width: 260px;
  }
}

/*-------------------
LAYOUT 
--------------------*/
.site {
  /* min-height: 100vh; */
}

/*-------------------
GLOBAL 
--------------------*/
.rounded-xl {
  border-radius: 2rem;
}

.bg-dark-light {
  background-color: var(--cus-bg-table);
}

.text-one-line {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.text-info-light {
  color: #1AACFF;
}

.text-success-light {
  color: #00D796;
}

.order-vol-bar {
  position: relative;
  padding: 0 0.35rem;
}

.order-vol-bar .value-bar {
  position: absolute;
  height: 100%;
  width: 0;
  left: 0;
  top: 0;
  z-index: -1;
  opacity: 0.9;
}

.order-vol-bar.buy .value-bar {
  background-color: #009368;
}

.order-vol-bar.sell .value-bar {
  background-color: #B71010;
}

.order-vol-bar.text-end .value-bar {
  right: 0;
  left: auto;
}

/*-------------------
SITE HEADER 
--------------------*/
.site-header {
  background-color: var(--cus-bg-secondary);
  /* Mobile Toggle */
  /* Site Brand */
  /* Header Nav */
  /* Topbar Nav */
}

.site-header .btn-offcanvas-toggle {
  font-size: 1.75rem;
  padding: 0;
}

.site-header .site-brand .site-title {
  font-size: 2rem;
}

.site-header .header-nav {
  border-bottom: 0;
}

.site-header .header-nav .nav-item .nav-link {
  border: 0;
  border-top: 2px solid rgba(0, 0, 0, 0);
  background-color: rgba(0, 0, 0, 0);
  border-radius: 0;
  text-transform: uppercase;
  color: var(--cus-text-primary);
}

.site-header .header-nav .nav-item .nav-link:hover {
  background-color: rgba(0, 0, 0, 0.15);
  font-weight: 500;
}

.site-header .header-nav .nav-item .nav-link.active {
  background-color: var(--cus-bg-primary);
  border-top-color: #F78F20;
  color: var(--cus-text-primary);
}

.site-header .topbar-nav .nav-item:last-child .nav-link {
  padding-right: 0;
}

/*-------------------
Notification 
--------------------*/
.post-list .post-item {
  padding: 1rem 1rem 1rem 3rem;
  position: relative;
  border-bottom: 1px solid var(--cus-bg-secondary);
  cursor: pointer;
}

.post-list .post-item:last-child {
  border-bottom: none;
}

.post-list .post-item .item-icon {
  position: absolute;
  left: 0.5rem;
  top: 0.75rem;
  font-size: 1.5rem;
  color: #0B67BE;
}

.post-list .post-item .item-meta {
  position: absolute;
  right: 1rem;
  top: 1rem;
  opacity: 0.5;
}

.post-list .post-item.unread {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.post-detail .post-header {
  position: relative;
  padding-right: 2rem;
}

.post-detail .post-header .btn-close {
  position: absolute;
  right: 0;
  top: 0;
}

.form-line .form-control,
.form-line .form-select {
  background-color: rgba(255, 255, 255, 0);
  padding-left: 0;
  padding-right: 0;
  border-top: 0;
  border-left: 0;
  border-right: 0;
  border-bottom: 1px solid grey;
  border-radius: 0;
  padding-top: 0;
  color: var(--cus-text-primary);
}

/*# sourceMappingURL=main.css.map */

.input_number {
  font-size: 16px !important;
  padding-right: 10px !important;
  font-family: sans-serif, 'Roboto';
}

.unread {
  background-color: #f0f7f5 !important;
}

.unread .item-icon {
  position: relative;
}

.unread .item-icon :after {
  content: '';
  right: 0;
  top: 0.35rem;
  background-color: red;
  position: absolute;
  width: 0.35rem;
  height: 0.35rem;
  border-radius: 50%;
}

.width-100-percent {
  width: 100%;
}

.margin-top-12 {
  margin-top: 12%;
}