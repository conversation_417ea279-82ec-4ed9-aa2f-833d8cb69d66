// source: admin_service.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = (function() {
  if (this) { return this; }
  if (typeof window !== 'undefined') { return window; }
  if (typeof global !== 'undefined') { return global; }
  if (typeof self !== 'undefined') { return self; }
  return Function('return this')();
}.call(null));

var trading_model_pb = require('./trading_model_pb.js');
goog.object.extend(proto, trading_model_pb);
var system_model_pb = require('./system_model_pb.js');
goog.object.extend(proto, system_model_pb);
var query_model_pb = require('./query_model_pb.js');
goog.object.extend(proto, query_model_pb);
var pricing_model_pb = require('./pricing_model_pb.js');
goog.object.extend(proto, pricing_model_pb);
var admin_model_pb = require('./admin_model_pb.js');
goog.object.extend(proto, admin_model_pb);
goog.exportSymbol('proto.AdminGetPendingOrderListRequest', null, global);
goog.exportSymbol('proto.AdminGetPendingOrderListResponse', null, global);
goog.exportSymbol('proto.AdminOrderManualRequest', null, global);
goog.exportSymbol('proto.AdminOrderManualResponse', null, global);
goog.exportSymbol('proto.AdminOrderUpdateStatusRequest', null, global);
goog.exportSymbol('proto.AdminOrderUpdateStatusResponse', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.AdminGetPendingOrderListRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.AdminGetPendingOrderListRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.AdminGetPendingOrderListRequest.displayName = 'proto.AdminGetPendingOrderListRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.AdminGetPendingOrderListResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.AdminGetPendingOrderListResponse.repeatedFields_, null);
};
goog.inherits(proto.AdminGetPendingOrderListResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.AdminGetPendingOrderListResponse.displayName = 'proto.AdminGetPendingOrderListResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.AdminOrderUpdateStatusRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.AdminOrderUpdateStatusRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.AdminOrderUpdateStatusRequest.displayName = 'proto.AdminOrderUpdateStatusRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.AdminOrderUpdateStatusResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.AdminOrderUpdateStatusResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.AdminOrderUpdateStatusResponse.displayName = 'proto.AdminOrderUpdateStatusResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.AdminOrderManualRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.AdminOrderManualRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.AdminOrderManualRequest.displayName = 'proto.AdminOrderManualRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.AdminOrderManualResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.AdminOrderManualResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.AdminOrderManualResponse.displayName = 'proto.AdminOrderManualResponse';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.AdminGetPendingOrderListRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.AdminGetPendingOrderListRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.AdminGetPendingOrderListRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.AdminGetPendingOrderListRequest.toObject = function(includeInstance, msg) {
  var f, obj = {

  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.AdminGetPendingOrderListRequest}
 */
proto.AdminGetPendingOrderListRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.AdminGetPendingOrderListRequest;
  return proto.AdminGetPendingOrderListRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.AdminGetPendingOrderListRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.AdminGetPendingOrderListRequest}
 */
proto.AdminGetPendingOrderListRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.AdminGetPendingOrderListRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.AdminGetPendingOrderListRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.AdminGetPendingOrderListRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.AdminGetPendingOrderListRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.AdminGetPendingOrderListResponse.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.AdminGetPendingOrderListResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.AdminGetPendingOrderListResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.AdminGetPendingOrderListResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.AdminGetPendingOrderListResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    msgCode: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msgText: jspb.Message.getFieldWithDefault(msg, 2, ""),
    orderList: jspb.Message.toObjectList(msg.getOrderList(),
    trading_model_pb.Order.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.AdminGetPendingOrderListResponse}
 */
proto.AdminGetPendingOrderListResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.AdminGetPendingOrderListResponse;
  return proto.AdminGetPendingOrderListResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.AdminGetPendingOrderListResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.AdminGetPendingOrderListResponse}
 */
proto.AdminGetPendingOrderListResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.MsgCode} */ (reader.readEnum());
      msg.setMsgCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsgText(value);
      break;
    case 3:
      var value = new trading_model_pb.Order;
      reader.readMessage(value,trading_model_pb.Order.deserializeBinaryFromReader);
      msg.addOrder(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.AdminGetPendingOrderListResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.AdminGetPendingOrderListResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.AdminGetPendingOrderListResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.AdminGetPendingOrderListResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMsgCode();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getMsgText();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getOrderList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      trading_model_pb.Order.serializeBinaryToWriter
    );
  }
};


/**
 * optional MsgCode msg_code = 1;
 * @return {!proto.MsgCode}
 */
proto.AdminGetPendingOrderListResponse.prototype.getMsgCode = function() {
  return /** @type {!proto.MsgCode} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.MsgCode} value
 * @return {!proto.AdminGetPendingOrderListResponse} returns this
 */
proto.AdminGetPendingOrderListResponse.prototype.setMsgCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional string msg_text = 2;
 * @return {string}
 */
proto.AdminGetPendingOrderListResponse.prototype.getMsgText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.AdminGetPendingOrderListResponse} returns this
 */
proto.AdminGetPendingOrderListResponse.prototype.setMsgText = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * repeated Order order = 3;
 * @return {!Array<!proto.Order>}
 */
proto.AdminGetPendingOrderListResponse.prototype.getOrderList = function() {
  return /** @type{!Array<!proto.Order>} */ (
    jspb.Message.getRepeatedWrapperField(this, trading_model_pb.Order, 3));
};


/**
 * @param {!Array<!proto.Order>} value
 * @return {!proto.AdminGetPendingOrderListResponse} returns this
*/
proto.AdminGetPendingOrderListResponse.prototype.setOrderList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.Order=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Order}
 */
proto.AdminGetPendingOrderListResponse.prototype.addOrder = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.Order, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.AdminGetPendingOrderListResponse} returns this
 */
proto.AdminGetPendingOrderListResponse.prototype.clearOrderList = function() {
  return this.setOrderList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.AdminOrderUpdateStatusRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.AdminOrderUpdateStatusRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.AdminOrderUpdateStatusRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.AdminOrderUpdateStatusRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    order: (f = msg.getOrder()) && trading_model_pb.Order.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.AdminOrderUpdateStatusRequest}
 */
proto.AdminOrderUpdateStatusRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.AdminOrderUpdateStatusRequest;
  return proto.AdminOrderUpdateStatusRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.AdminOrderUpdateStatusRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.AdminOrderUpdateStatusRequest}
 */
proto.AdminOrderUpdateStatusRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new trading_model_pb.Order;
      reader.readMessage(value,trading_model_pb.Order.deserializeBinaryFromReader);
      msg.setOrder(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.AdminOrderUpdateStatusRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.AdminOrderUpdateStatusRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.AdminOrderUpdateStatusRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.AdminOrderUpdateStatusRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOrder();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      trading_model_pb.Order.serializeBinaryToWriter
    );
  }
};


/**
 * optional Order order = 1;
 * @return {?proto.Order}
 */
proto.AdminOrderUpdateStatusRequest.prototype.getOrder = function() {
  return /** @type{?proto.Order} */ (
    jspb.Message.getWrapperField(this, trading_model_pb.Order, 1));
};


/**
 * @param {?proto.Order|undefined} value
 * @return {!proto.AdminOrderUpdateStatusRequest} returns this
*/
proto.AdminOrderUpdateStatusRequest.prototype.setOrder = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.AdminOrderUpdateStatusRequest} returns this
 */
proto.AdminOrderUpdateStatusRequest.prototype.clearOrder = function() {
  return this.setOrder(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.AdminOrderUpdateStatusRequest.prototype.hasOrder = function() {
  return jspb.Message.getField(this, 1) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.AdminOrderUpdateStatusResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.AdminOrderUpdateStatusResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.AdminOrderUpdateStatusResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.AdminOrderUpdateStatusResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    msgCode: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msgText: jspb.Message.getFieldWithDefault(msg, 2, ""),
    order: (f = msg.getOrder()) && trading_model_pb.Order.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.AdminOrderUpdateStatusResponse}
 */
proto.AdminOrderUpdateStatusResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.AdminOrderUpdateStatusResponse;
  return proto.AdminOrderUpdateStatusResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.AdminOrderUpdateStatusResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.AdminOrderUpdateStatusResponse}
 */
proto.AdminOrderUpdateStatusResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.MsgCode} */ (reader.readEnum());
      msg.setMsgCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsgText(value);
      break;
    case 3:
      var value = new trading_model_pb.Order;
      reader.readMessage(value,trading_model_pb.Order.deserializeBinaryFromReader);
      msg.setOrder(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.AdminOrderUpdateStatusResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.AdminOrderUpdateStatusResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.AdminOrderUpdateStatusResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.AdminOrderUpdateStatusResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMsgCode();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getMsgText();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getOrder();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      trading_model_pb.Order.serializeBinaryToWriter
    );
  }
};


/**
 * optional MsgCode msg_code = 1;
 * @return {!proto.MsgCode}
 */
proto.AdminOrderUpdateStatusResponse.prototype.getMsgCode = function() {
  return /** @type {!proto.MsgCode} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.MsgCode} value
 * @return {!proto.AdminOrderUpdateStatusResponse} returns this
 */
proto.AdminOrderUpdateStatusResponse.prototype.setMsgCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional string msg_text = 2;
 * @return {string}
 */
proto.AdminOrderUpdateStatusResponse.prototype.getMsgText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.AdminOrderUpdateStatusResponse} returns this
 */
proto.AdminOrderUpdateStatusResponse.prototype.setMsgText = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional Order order = 3;
 * @return {?proto.Order}
 */
proto.AdminOrderUpdateStatusResponse.prototype.getOrder = function() {
  return /** @type{?proto.Order} */ (
    jspb.Message.getWrapperField(this, trading_model_pb.Order, 3));
};


/**
 * @param {?proto.Order|undefined} value
 * @return {!proto.AdminOrderUpdateStatusResponse} returns this
*/
proto.AdminOrderUpdateStatusResponse.prototype.setOrder = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.AdminOrderUpdateStatusResponse} returns this
 */
proto.AdminOrderUpdateStatusResponse.prototype.clearOrder = function() {
  return this.setOrder(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.AdminOrderUpdateStatusResponse.prototype.hasOrder = function() {
  return jspb.Message.getField(this, 3) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.AdminOrderManualRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.AdminOrderManualRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.AdminOrderManualRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.AdminOrderManualRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    order: (f = msg.getOrder()) && trading_model_pb.Order.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.AdminOrderManualRequest}
 */
proto.AdminOrderManualRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.AdminOrderManualRequest;
  return proto.AdminOrderManualRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.AdminOrderManualRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.AdminOrderManualRequest}
 */
proto.AdminOrderManualRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new trading_model_pb.Order;
      reader.readMessage(value,trading_model_pb.Order.deserializeBinaryFromReader);
      msg.setOrder(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.AdminOrderManualRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.AdminOrderManualRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.AdminOrderManualRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.AdminOrderManualRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOrder();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      trading_model_pb.Order.serializeBinaryToWriter
    );
  }
};


/**
 * optional Order order = 1;
 * @return {?proto.Order}
 */
proto.AdminOrderManualRequest.prototype.getOrder = function() {
  return /** @type{?proto.Order} */ (
    jspb.Message.getWrapperField(this, trading_model_pb.Order, 1));
};


/**
 * @param {?proto.Order|undefined} value
 * @return {!proto.AdminOrderManualRequest} returns this
*/
proto.AdminOrderManualRequest.prototype.setOrder = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.AdminOrderManualRequest} returns this
 */
proto.AdminOrderManualRequest.prototype.clearOrder = function() {
  return this.setOrder(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.AdminOrderManualRequest.prototype.hasOrder = function() {
  return jspb.Message.getField(this, 1) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.AdminOrderManualResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.AdminOrderManualResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.AdminOrderManualResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.AdminOrderManualResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    msgCode: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msgText: jspb.Message.getFieldWithDefault(msg, 2, ""),
    order: (f = msg.getOrder()) && trading_model_pb.Order.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.AdminOrderManualResponse}
 */
proto.AdminOrderManualResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.AdminOrderManualResponse;
  return proto.AdminOrderManualResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.AdminOrderManualResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.AdminOrderManualResponse}
 */
proto.AdminOrderManualResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.MsgCode} */ (reader.readEnum());
      msg.setMsgCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsgText(value);
      break;
    case 3:
      var value = new trading_model_pb.Order;
      reader.readMessage(value,trading_model_pb.Order.deserializeBinaryFromReader);
      msg.setOrder(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.AdminOrderManualResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.AdminOrderManualResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.AdminOrderManualResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.AdminOrderManualResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMsgCode();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getMsgText();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getOrder();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      trading_model_pb.Order.serializeBinaryToWriter
    );
  }
};


/**
 * optional MsgCode msg_code = 1;
 * @return {!proto.MsgCode}
 */
proto.AdminOrderManualResponse.prototype.getMsgCode = function() {
  return /** @type {!proto.MsgCode} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.MsgCode} value
 * @return {!proto.AdminOrderManualResponse} returns this
 */
proto.AdminOrderManualResponse.prototype.setMsgCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional string msg_text = 2;
 * @return {string}
 */
proto.AdminOrderManualResponse.prototype.getMsgText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.AdminOrderManualResponse} returns this
 */
proto.AdminOrderManualResponse.prototype.setMsgText = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional Order order = 3;
 * @return {?proto.Order}
 */
proto.AdminOrderManualResponse.prototype.getOrder = function() {
  return /** @type{?proto.Order} */ (
    jspb.Message.getWrapperField(this, trading_model_pb.Order, 3));
};


/**
 * @param {?proto.Order|undefined} value
 * @return {!proto.AdminOrderManualResponse} returns this
*/
proto.AdminOrderManualResponse.prototype.setOrder = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.AdminOrderManualResponse} returns this
 */
proto.AdminOrderManualResponse.prototype.clearOrder = function() {
  return this.setOrder(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.AdminOrderManualResponse.prototype.hasOrder = function() {
  return jspb.Message.getField(this, 3) != null;
};


goog.object.extend(exports, proto);
