import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Modal } from 'react-bootstrap';
import NumberFormat from 'react-number-format';
import { ToastContainer } from 'react-toastify';
import axios from 'axios';
import moment from "moment";
import $ from 'jquery';
import { toast } from 'react-toastify';
import Decimal from 'decimal.js';

import { wsService } from '../services/websocket-service';
import rpc from '../model/proto/rpc_pb';
import { Order, OrderType, ExecutionMode, OrderMode, OrderRoute, Side } from '../model/proto/trading_model_pb';
import { NewOrderSingleRequest } from '../model/proto/trading_service_pb';
import { ProductType } from '../model/proto/query_model_pb';
import { MsgCode } from '../model/proto/system_model_pb';

import { defindConfigPost } from '../model/utils';
import { MARKET } from '../model/constant';
import { _renderMessageErrorMinPriceValue } from '../model/utils';
import { checkMessageError } from '../model/utils';
import { calcChange, formatNumber, checkValue, calcPctChange, getLastAsksElement, getLastBidsElement, getPriceAfterSpread, convertNumber, getLowerValue, getHigherValue, renderNumber, formatCurrency, checkVolumeLotSize, checkPriceTickSize, calcCeilFloor } from '../model/utils';
import { CURRENCY, CURRENCY_DISPLAY, MAX_LENGTH_VOLUME, LocalStorageKey, OrderTypeName, ORDER_SIDE, PROGRESS, PASSWORD_REQ_SUBMIT, MESSAGE_ERROR, UNSTABLE_CONNECTION, DIGIT_DEFAULT, EXISTING_LIMIT_ORDER, NO_CHANGE_IN_BENEFICIARY_DISPLAY, NO_CHANGE_IN_BENEFICIARY_RESPONSE, PRODUCT_TYPE, HANDLE_NEW_ORDER_REQUEST } from '../model/constant';
import { API_E2EE_PRESESSION, API_POST_CLIENT_INFO } from '../constants/api.constant';
import { success } from "../constants/announcement";
import { PSPL_ACCOUNT_TYPE, PSPL_COUNTER_GRADE } from '../constants/general';
import { paramOrderDefault, CounterGradeValueM, CounterGradeValueV } from '../mocks';

import LazyLoad from '../components/lazy-load';
import PopupTimeout from '../components/popup-timeout';

const _fn = require("../assets/scripts/E2EE/fn");
const _rsa = require("../assets/scripts/E2EE/rsa_jso");

const api_url = window.apiUrl;
const flagRmsApi = window.flagRmsApi;
const wsResponseTimeout = window.wsResponseTimeout;
const urlPostClienInfo = `${api_url}${API_POST_CLIENT_INFO}`;
const urlGetE2EEPresession = `${api_url}${API_E2EE_PRESESSION}`;

const OrderPannel = (props) => {
    const { symbolInfo, symbolMap } = props;
    let rsa = null;
    const minOrderValue = localStorage.getItem(LocalStorageKey.MIN_ORDER_VALUE) || '';
    const maxOrderVolume = localStorage.getItem(LocalStorageKey.MAX_ORDER_VOLUME) || Number.MAX_SAFE_INTEGER;
    const maxOrderValue = localStorage.getItem(LocalStorageKey.MAX_ORDER_VALUE) || Number.MAX_SAFE_INTEGER;

    const { t, i18n } = useTranslation();

    const symbolCodeRef = useRef(symbolInfo?.symbolCode);
    const symbolInfoRef = useRef(symbolInfo);
    const [floorPrice, setFloorPrice] = useState(0);
    const [ceilingPrice, setCeilingPrice] = useState(0);
    
    const [invalidVolume, setInvalidVolume] = useState(false);
    const [invalidLimitPrice, setInvalidLimitPrice] = useState(false);
    
    const [baseSGD, setBaseSGD] = useState(false);
    const [baseUSD, setBaseUSD] = useState(true);
  
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const [showSuccessModal, setShowSuccessModal] = useState(false);
    const [showBidPercent, setShowBidPercent] = useState(false);
    const [showAskPercent, setShowAskPercent] = useState(true);
    const [showErrorModal, setShowErrorModal] = useState(false);
    
    const [titleError, setTitleError] = useState('');
    const [preSessionInfo, setPreSessionInfo] = useState();
    const [password, setPassword] = useState('');
    const [pinR, setPinR] = useState('');
    const [pinROnly8, setPinROnly8] = useState('');
    const [psplAcctType, setPsplAcctType] = useState('');
    const [pwdReqSubmit, setPwdReqSubmit] = useState('');
    
    const [isShowlazyLoad, setIsShowLazyLoad] = useState(false);
    const [isMaxQty, setIsMaxQty] = useState(false);
    const [isAllowed, setIsAllow] = useState(false);
    const [isShowError, setIsShowError] = useState(false);
    const [isShowPassword, setIsShowPassword] = useState(false);

    const [minLot, setMinLot] = useState(0);
    const [lotSize, setLotSize] = useState(0);
    const [tickSize, setTickSize] = useState(0);
    const [limitPrice, setLimitPrice] = useState(0);
    const [orderType, setOrderType] = useState(OrderType.OP_LIMIT);
    const [currency, setCurrency] = useState(CURRENCY_DISPLAY.USD);
    const [sideBtn, setSideBtn] = useState(ORDER_SIDE.BUY);
    
    const [dataCashWithoutMargin, setDataCashWithoutMargin] = useState([]);
    const [quoteEvent, setQuoteEvent] = useState([]);
    const [symbol, setSymbol] = useState({});
    const [order, setOrder] = useState(paramOrderDefault)
    const [isValidPassword, setIsValidPassword] = useState(false);
    const [isProcessed, setIsProcessed] = useState(false);

    const getSymbolDetail = useCallback(() => {
        const symbol = symbolMap.get(symbolInfo?.symbolCode);
        return symbol;
    }, [symbolInfoRef?.current])


    const symbolDetail = getSymbolDetail();

    useEffect(() => {
        const timer = setTimeout(() => {
            const temp = sessionStorage.getItem(LocalStorageKey.PWD_REQ_SUBMIT) || PASSWORD_REQ_SUBMIT.OFF;
            setPwdReqSubmit(temp);
        }, 2000)

        return () => {
            clearTimeout(timer);
        }
    }, [])

    useEffect(() => {
        const timer = setTimeout(() => {
            checkLazyLoadStatus()
        }, convertNumber(wsResponseTimeout))

        return () => {
            clearTimeout(timer);
        }
    }, [isShowlazyLoad])

    useEffect(() => {
        //Failed to validate password when client submit new order
        if(pwdReqSubmit === PASSWORD_REQ_SUBMIT.ON && !isValidPassword && isProcessed) {
            setTitleError(i18n.t('message.handleFail'));
            setIsShowError(true);
            setShowConfirmModal(false);
            setPassword('');
            setOrder((prev) => ({
                ...prev,
                quantity: 0
            }));
        }
    }, [isProcessed, isValidPassword])

    const checkLazyLoadStatus = () => {
        const lazyLoadStatus = localStorage.getItem(LocalStorageKey.LAZY_LOAD_STATUS);
        if (lazyLoadStatus === 'true') {
            setIsShowLazyLoad(false);
            localStorage.setItem(LocalStorageKey.LAZY_LOAD_STATUS, false);
            toast.error(UNSTABLE_CONNECTION)
        }
    }

    useEffect(() => {
        const symbol = symbolMap.get(symbolInfoRef?.current.symbolCode);
        setSymbol(symbolInfoRef?.current);
        if (symbol) {
            getClientInfo(symbol);
            setLotSize(convertNumber(symbol.lotSize));
            setMinLot(convertNumber(symbol.minLot));
            setTickSize(convertNumber(symbol.tickSize));
            setPassword('');
            setOrder(prev => ({
                ...prev,
                quantity: 0
            }));
        }
        const orderResp = wsService.getSingleOrderResponse().subscribe(resp => {
            console.log(`Received single order response at: ${moment().format('YYYY-MM-DD HH:mm:ss')}.${moment().millisecond()}`)
            setPassword('');
            setOrder(prevState => {
                let ord = Object.assign({}, prevState);
                ord.quantity = 0;
                return ord;
            });
            setIsShowLazyLoad(false);
            localStorage.setItem(LocalStorageKey.LAZY_LOAD_STATUS, false)
            const respCode = resp?.msgCode;
            const respText = resp?.msgText;
            if (respCode === MsgCode.MT_RET_OK) {
                if (flagRmsApi) {
                    getClientInfo(symbolInfo);
                }
                setShowSuccessModal(true);
            } else if (respCode === MsgCode.MT_RET_FORWARD_EXT_SYSTEM) {
                toast.success(HANDLE_NEW_ORDER_REQUEST);
            } else {
                if (respCode === MsgCode.MT_RET_NOT_ENOUGH_MIN_ORDER_VALUE) {
                    setShowErrorModal(true);
                    return;
                }
                if (respCode === MsgCode.MT_RET_EXIST_LIMIT_ORDER_IN_QUEUE) {
                    setTitleError(EXISTING_LIMIT_ORDER);
                    setIsShowError(true);
                    return;
                }
                if (respText === NO_CHANGE_IN_BENEFICIARY_RESPONSE) {
                    setTitleError(NO_CHANGE_IN_BENEFICIARY_DISPLAY);
                    setIsShowError(true);
                    return;
                }
                const messageDisplay = checkMessageError(respText, respCode);
                setTitleError(messageDisplay ? messageDisplay : i18n.t('message.orderFail'));
                setIsShowError(true);
            }
            console.log(`Process finished single order response at: ${moment().format('YYYY-MM-DD HH:mm:ss')}.${moment().millisecond()}`)
        });

        setSideBtn(ORDER_SIDE.BUY);
        setOrderType(OrderType.OP_LIMIT);
        if (symbolInfo) {
            const tmpPrice = convertNumber(symbolInfo.currentPrice) > 0 ? convertNumber(symbolInfo.currentPrice) : convertNumber(symbolInfo.prevClosePrice);
            const rounding = new Decimal(tmpPrice).toFixed(DIGIT_DEFAULT).toString();
            setLimitPrice(rounding);
            setFloorPrice(convertNumber(symbolInfo.floor));
            setCeilingPrice(convertNumber(symbolInfo.ceiling));
        }

        return () => {
            orderResp.unsubscribe();
        }
    }, [symbolInfoRef?.current]);

    useEffect(() => {
        symbolCodeRef.current = symbolInfo?.symbolCode;
    },[symbolInfo?.symbolCode])

    useEffect(() => {
        symbolInfoRef.current = symbolInfo;
    },[symbolInfo])

    useEffect(() => {
        const quoteEvent = wsService.getQuoteEventResponse().subscribe(quotes => {
            if (quotes && quotes.quoteList) {
                const idx = quotes?.quoteList?.findIndex(o => o?.symbolCode === symbolCodeRef?.current);
                if (idx >= 0) {
                    setQuoteEvent(quotes.quoteList);
                }
            }
        });

        return () => {
            quoteEvent.unsubscribe();
        }
    }, [])

    useEffect(() => {
        processQuote(quoteEvent);
        updateFloorCeilingPrice(quoteEvent);
    }, [quoteEvent, symbolInfo])

    useEffect(() => {
        resetOrderForm();
        if (!convertNumber(bidVolume) && !convertNumber(sellVolume)) {
            setShowBidPercent(false)
            setShowAskPercent(false)
        } else {
            setShowBidPercent(true)
            setShowAskPercent(true)
        }
    }, [symbol])

    const updateFloorCeilingPrice = (quoteList) => {
        const quote = quoteList.find(o => o?.symbolCode === symbolInfo?.symbolCode);
        const { ceiling, floor } = calcCeilFloor(convertNumber(quote?.retailPrice), symbolInfo, symbolMap);
        setFloorPrice(floor);
        setCeilingPrice(ceiling);
    }

    const getE2EEPresessionInfo = async () => {
        const param = {
            appType: 1
        };
        try {
            const resp = await axios.post(urlGetE2EEPresession, param, defindConfigPost());
            if (resp?.data?.meta?.code === success) {
                setIsValidPassword(true);
                const dataResp = resp?.data?.data;
                if (dataResp) {
                    setPreSessionInfo(dataResp);
                    encryptPasswordConfirm(dataResp);
                    setShowConfirmModal(true);
                }
            }
        } catch (error) {
            setIsValidPassword(false);
            setShowConfirmModal(false);
        } finally {
            setIsProcessed(true);
        }
    }

    const encryptPasswordConfirm = (preSession) => {
        rsa = new _rsa.RSAEngine();
        $("#E2EE_PUBLIC_KEY").val(preSession.publicKey);
        $("#E2EE_SESSION_ID").val(preSession.sessionId);
        $("#E2EE_RANDOM_NUMBER").val(preSession.randomNumber);

        let _publicKey = $("#E2EE_PUBLIC_KEY").val();
        let _sessionID = $("#E2EE_SESSION_ID").val();
        let _randomNumber = $("#E2EE_RANDOM_NUMBER").val();
        rsa.init(_publicKey, _sessionID, _randomNumber);

        let _txtPassword = $('#txtPassword')[0];

        let pwdOnly8 = _txtPassword.value.trim().substring(0, 8);
        let pwd = _txtPassword.value.trim().substring(0, 50);

        // encrypt password PinR
        _txtPassword.value = pwd;
        const pinR = _fn.fnGetRPIN(_txtPassword, rsa);
        setPinR(pinR)

        // encrypt password PinR_only8
        _txtPassword.value = pwdOnly8;
        const pinROnly8 = _fn.fnGetRPIN(_txtPassword, rsa);
        setPinROnly8(pinROnly8)
    }

    const getClientInfo = (itemSymbol) => {
        // TODO
        // When symbol has symbolSuffix !== "", symbolCode has format 'symbolCode.suffix' (eg: BRK.B) => input symbolCode and symbolSuffix correctly:
        // Example with symbol BRK.B: {
        //     symbol: "BRK",
        //     symbolsfx: "B"
        // }
        let symbolSfx = '';
        if (itemSymbol?.symbolSuffix) {
            symbolSfx = itemSymbol?.symbolSuffix;
        } else if (itemSymbol?.symbolCode?.split('.')[1]) {
            symbolSfx = itemSymbol?.symbolCode?.split('.')[1];
        }
        const symbCode = itemSymbol?.symbolCode?.split('.')[0];
        if (symbCode) {
            const paramPostClientInfo = {
                market: MARKET.US,
                symbol: symbCode,
                symbolsfx: symbolSfx
            }
            axios.post(urlPostClienInfo, paramPostClientInfo, defindConfigPost()).then((resp) => {
                if (resp?.data?.meta?.code === success) {
                    const data = resp?.data?.data;
                    const sellLimit = data?.sellLimit || paramOrderDefault.sellLimit;
                    setPsplAcctType(data?.acctType ? data?.acctType : '');
                    const symbol = symbolMap.get(symbolInfo?.symbolCode);
                    if (symbol) {
                        setOrder((prev) => ({
                            ...prev,
                            cashBalance: data.cashBalance,
                            maxPurchase: data?.acctType === PSPL_ACCOUNT_TYPE.M || data?.acctType === PSPL_ACCOUNT_TYPE.V ? calcMaxPurchase(data) : data?.buyLimit,
                            sellLimit: sellLimit,
                        }));
                    }
                    setDataCashWithoutMargin({
                        buyLimit: data?.buyLimit,
                        creditLimit: data?.creditLimit
                    })
                }
            });
        }
    }

    const getCounterGradeValueM = (counterGrade) => {
        switch (counterGrade) {
            case PSPL_COUNTER_GRADE.A: {
                return CounterGradeValueM.A;
            }
            case PSPL_COUNTER_GRADE.B: {
                return CounterGradeValueM.B;
            }
            case PSPL_COUNTER_GRADE.C: {
                return CounterGradeValueM.C;
            }
            case PSPL_COUNTER_GRADE.E: {
                return CounterGradeValueM.E;
            }
            case PSPL_COUNTER_GRADE.S: {
                return CounterGradeValueM.S;
            }
            default: {
                return 1;
            }
        }
    }

    const getCounterGradeValueV = (counterGrade) => {
        switch (counterGrade) {
            case PSPL_COUNTER_GRADE.A: {
                return CounterGradeValueV.A;
            }
            case PSPL_COUNTER_GRADE.B: {
                return CounterGradeValueV.B;
            }
            case PSPL_COUNTER_GRADE.C: {
                return CounterGradeValueV.C;
            }
            case PSPL_COUNTER_GRADE.E: {
                return CounterGradeValueV.E;
            }
            case PSPL_COUNTER_GRADE.S: {
                return CounterGradeValueV.S;
            }
            default: {
                return 1;
            }
        }
    }

    const calcCashBalance = (clientInfoData) => {
        if (clientInfoData) {
            const cash = clientInfoData.cashBalance;
            const acctType = clientInfoData.acctType;
            const counterGrade = clientInfoData.counterGrade;
            let counterGradeValue = 0;
            if (acctType === PSPL_ACCOUNT_TYPE.M) {
                counterGradeValue = getCounterGradeValueM(counterGrade);
            } else {
                counterGradeValue = getCounterGradeValueV(counterGrade);
            }

            return cash * counterGradeValue;
        }
        return 0;
    }

    const calcMaxPurchase = (clientInfoData) => {
        if (clientInfoData) {
            const accountType = clientInfoData.acctType;
            const cashBalance = calcCashBalance(clientInfoData);
            let maxPurchaseBeforeRate = 0;
            if (accountType === PSPL_ACCOUNT_TYPE.V) {
                maxPurchaseBeforeRate = getHigherValue(cashBalance, clientInfoData.buyLimit);
            } else {
                maxPurchaseBeforeRate = getLowerValue(cashBalance, clientInfoData.buyLimit);
            }
            if (convertNumber(clientInfoData.rate) > 0) {
                return maxPurchaseBeforeRate * convertNumber(clientInfoData.rate);
            }
            return maxPurchaseBeforeRate * 100;
        }
        return 0;
    }

    const displayCurrency = (currency) => {
        if (currency === CURRENCY.SGD) {
            return CURRENCY_DISPLAY.SGD;
        }
        return CURRENCY_DISPLAY.USD;
    }

    const processQuote = (quotes) => {
        if (quotes && quotes.length > 0) {
            
            const temp = {
                ...symbolInfo,
                asksList: quotes[0].asksList,
                bidsList: quotes[0].bidsList,
                close: quotes[0].retailClose,
                currentPrice: quotes[0].retailPrice,
                high: quotes[0].retailHigh,
                low: quotes[0].retailLow,
                open: quotes[0].retailOpen,
                tickPerDay: checkValue(symbolInfo.tickPerDay, quotes[0].tickPerDay),
                volumePerDay: checkValue(symbolInfo.volumePerDay, quotes[0].volumePerDay)
            }
            setSymbol(temp);
        }
    }

    //Todo: Available Cash Without Margin = BuyImtbal - creditLimit
    const calcCashWithoutMargin = () => {
        const { creditLimit, buyLimit } = dataCashWithoutMargin;
        if (isNaN(buyLimit) || isNaN(creditLimit)) return 0;
        return buyLimit - creditLimit;
    }

    const changeCcy = (ccy) => {
        setOrder((prev) => ({
            ...prev,
            ccy: ccy
        }))

        if (ccy === CURRENCY.SGD) {
            setBaseSGD(true);
            setBaseUSD(false);
            setCurrency(CURRENCY_DISPLAY.SGD);
        } else {
            setBaseSGD(false);
            setBaseUSD(true);
            setCurrency(CURRENCY_DISPLAY.USD);
        }
    }

    const placeOrder = () => {
        setIsProcessed(false);
        if (pwdReqSubmit === PASSWORD_REQ_SUBMIT.ON) {
            getE2EEPresessionInfo();
        } else {
            setShowConfirmModal(true);
        }

        const grossValueCurrent = calcGrossValue(order.quantity);
        if (order.quantity <= 0) {
            setTitleError(i18n.t('message.qtyInvalid'));
            setIsShowError(true);
            return;
        }

        if (invalidVolume) {
            setTitleError(i18n.t('message.qtyInvalid'));
            setIsShowError(true);
            return;
        }

        if (order.quantity > 0 && grossValueCurrent <= 0) {
            setTitleError(i18n.t('message.priceInvalid'));
            setIsShowError(true);
            return;
        }

        // TODO: #58855 - No check and no compare Gross Value and Buy Limit
        // if (sideBtn === ORDER_SIDE.BUY && flagRmsApi) {
        //     const isValidGrossValue = grossValueCurrent > 0 && grossValueCurrent <= order.cashBalance && grossValueCurrent <= order.maxPurchase;
        //     if (!isValidGrossValue) {
        //         setTitleError(i18n.t('message.grossBuyInvalid'));
        //         setIsShowError(true);
        //         return;
        //     }
        // }

        // TODO: #57326 - No check and no compare Gross Value and Sell Limit
        // if (sideBtn === ORDER_SIDE.SELL) {
        //     const isValidGrossValue = grossValueCurrent > 0 && grossValueCurrent <= order.sellLimit;
        //     if (!isValidGrossValue) {
        //         setTitleError(i18n.t('message.grossSellInvalid'));
        //         setIsShowError(true);
        //         return;
        //     }
        // }
    }

    const sentOrder = () => {
        callOrderRequest();
        setShowConfirmModal(false);
    }

    const getSideValue = (strSide) => {
        switch (strSide.toUpperCase()) {
            case OrderTypeName.buy: {
                return Side.BUY;
            }
            default: {
                return Side.SELL;
            }
        }
    }

    const callOrderRequest = () => {
        console.log(`Start send single order request at: ${moment().format('YYYY-MM-DD HH:mm:ss')}.${moment().millisecond()}`);
        const accountId = sessionStorage.getItem(LocalStorageKey.ACCOUNT_ID);
        const maxOrderVolume = localStorage.getItem(LocalStorageKey.MAX_ORDER_VOLUME);
        if (convertNumber(maxOrderVolume) < convertNumber(order.quantity)) {
            const errMess = MESSAGE_ERROR.get(MsgCode.MT_RET_EXCEED_MAX_ORDER_VOLUME)
            setTitleError(`${errMess}: ${renderNumber(maxOrderVolume)}`);
            setIsShowError(true);
            return;
        }
        setIsShowLazyLoad(true);
        const currentDate = moment();
        const wsConnect = wsService.getWsConnected();

        if (wsConnect) {
            let price = '0';
            if (orderType === OrderType.OP_MARKET) {
                if (sideBtn === ORDER_SIDE.BUY) {
                    price = getPriceAfterSpread(getLastAsksElement(symbol)?.price, ORDER_SIDE.SELL, symbolDetail);
                } else {
                    price = getPriceAfterSpread(getLastBidsElement(symbol)?.price, ORDER_SIDE.BUY, symbolDetail);
                }
                if (isNaN(Number(price))) {
                    toast.error("Insufficient liquidity for this trade");
                    localStorage.setItem(LocalStorageKey.LAZY_LOAD_STATUS, false);
                    setIsShowLazyLoad(false);
                    return;
                }
            }
            const singleOrder = new NewOrderSingleRequest();
            let orderParam = new Order();
            orderParam.setPrice(orderType === OrderType.OP_MARKET ? price : limitPrice.toString());
            orderParam.setAmount(order.quantity.toString());
            orderParam.setSymbolCode(symbol.symbolCode);
            orderParam.setUid(Number(accountId));
            orderParam.setSide(getSideValue(sideBtn));
            orderParam.setOrderType(orderType)
            orderParam.setExecuteMode(ExecutionMode.MARKET);
            orderParam.setOrderMode(OrderMode.REGULAR);
            orderParam.setRoute(OrderRoute.ROUTE_WEB);
            orderParam.setCurrencyCode(currency);
            orderParam.setSubmittedId(accountId);

            singleOrder.setOrder(orderParam);
            if (pwdReqSubmit === PASSWORD_REQ_SUBMIT.ON) {
                singleOrder.setSessionId(preSessionInfo.sessionId);
                singleOrder.setRandomNumber(preSessionInfo.randomNumber);
                singleOrder.setHashPassword(pinR);
                singleOrder.setHashPasswordOnly8(pinROnly8);
            }
            const rpcMsg = new rpc.RpcMessage();
            rpcMsg.setPayloadClass(rpc.RpcMessage.Payload.NEW_ORDER_SINGLE_REQ);
            rpcMsg.setPayloadData(singleOrder.serializeBinary());
            rpcMsg.setContextId(currentDate.valueOf());
            wsService.sendMessage(rpcMsg.serializeBinary());
            console.log(`Send single order request at: ${moment().format('YYYY-MM-DD HH:mm:ss')}.${moment().millisecond()}`);
        }
        localStorage.setItem(LocalStorageKey.LAZY_LOAD_STATUS, true)
    }

    const calcGrossValue = (volume) => {
        if (orderType === OrderType.OP_MARKET) {
            let priceExecute = getMarketPrice();
            return priceExecute * convertNumber(volume);
        }
        return limitPrice * convertNumber(volume);
    }

    const validPrice = () => {
        if (convertNumber(limitPrice) > ceilingPrice || convertNumber(limitPrice) < floorPrice) {
            return true;
        }
        return false;
    }

    const getMarketPrice = () => {
        return sideBtn === ORDER_SIDE.BUY
            ? convertNumber(getPriceAfterSpread(getLastAsksElement(symbol)?.price, ORDER_SIDE.SELL, symbolDetail))
            : convertNumber(getPriceAfterSpread(getLastBidsElement(symbol)?.price, ORDER_SIDE.BUY, symbolDetail))
    }

    const _renderConfirmModal = () => {
        return <Modal show={showConfirmModal} size="sm" centered="true">
            <div className="modal-header">
                <h5 className="modal-title" id="orderConfirmLabel">{t('order.confirm')}</h5>
                <button type="button" className="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close" onClick={() => setShowConfirmModal(false)}></button>
            </div>
            <div className="modal-body p-0">
                <div className="d-flex justify-content-between align-items-center px-3 mb-3">
                    <div>
                        <div>{symbolDetail?.symbolName}</div>
                        <div>
                            {symbol?.productType !== ProductType.PRODUCT_TYPE_ETF && <span className="badge bg-info">{PRODUCT_TYPE[symbol?.productType || ProductType.PRODUCT_TYPE_NONE]}</span>}
                            {symbol?.productType === ProductType.PRODUCT_TYPE_ETF && <span className="badge bg-violet">{PRODUCT_TYPE[symbol?.productType || ProductType.PRODUCT_TYPE_NONE]}</span>}
                            <span className="opacity-50 ml-5">{symbolDetail?.symbolCode}</span></div>
                    </div>
                    <div className="fs-3 text-success text-uppercase">
                        {sideBtn === ORDER_SIDE.BUY ? <p className='text-success'>{t('market.buy')}</p> : <p className='text-danger'>{t('market.sell')}</p>}
                    </div>
                </div>
                <div className="p-3 bg-dark-light table-responsive mb-3">
                    <table className="table table-borderless mb-0">
                        <tbody>
                            <tr>
                                {orderType === OrderType.OP_MARKET &&
                                    <td className="p-0">
                                        <div className="opacity-50">{t('order.marketPrice')}</div>
                                        <div><strong>{formatCurrency(getMarketPrice())}</strong></div>
                                    </td>
                                }
                                {orderType === OrderType.OP_LIMIT &&
                                    <td className="p-0">
                                        <div className="opacity-50">{t('order.limitPrice')}</div>
                                        <div><strong>{formatCurrency(limitPrice)}</strong></div>
                                    </td>
                                }
                                <td className="p-0">
                                    <div className="opacity-50">{t('order.qty')}</div>
                                    <div><strong>{order.quantity}</strong></div>
                                </td>
                                <td className="p-0">
                                    <div className="opacity-50">{t('order.type')}</div>
                                    <div>
                                        {orderType === OrderType.OP_MARKET && <strong>{t('order.market')}</strong>}
                                        {orderType === OrderType.OP_LIMIT && <strong>{t('order.limit')}</strong>}
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td className="pt-3 p-0">
                                    <div className="opacity-50">{t('order.paymentType')}</div>
                                    <div><strong>{t('order.cash')}</strong></div>
                                </td>
                                <td className="pt-3 p-0">
                                    <div className="opacity-50">{t('order.settlementCcy')}</div>
                                    <div><strong className="text-info">{displayCurrency(order.ccy)}</strong></div>
                                </td>

                            </tr>
                        </tbody>
                    </table>
                </div>
                <div className="d-flex justify-content-between align-items-center px-3">
                    <div>
                        <div>{t('order.indicativeGrossValue')}</div>
                        {orderType === OrderType.OP_MARKET && <div style={{ fontSize: '10px' }}>{t('order.grossValueDescription')}</div>}
                    </div>
                    <div style={{ marginTop: '-15px' }}>{formatNumber(calcGrossValue(order.quantity), DIGIT_DEFAULT)} {CURRENCY_DISPLAY.USD}</div>
                </div>
            </div>
            <div className="modal-footer">
                <button type="button"
                    disabled={calcGrossValue(order.quantity) <= 0 || validPrice()}
                    className="btn btn-primary rounded-xl text-transform px-3 mx-auto" onClick={() => sentOrder()}>{t('order.confirmBtn')}</button>
            </div>
        </Modal>
    }

    const _renderSuccessModal = useMemo(() => {
        return <Modal show={showSuccessModal} size="sm" centered="true">
            <div className="modal-header text-end pb-0">
                <button type="button" className="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close" onClick={() => setShowSuccessModal(false)}></button>
            </div>
            <div className="modal-body">
                <div className="mb-3 text-center">
                    <p><img src={`${process.env.PUBLIC_URL}/img/icon-confirm-success.svg`} alt="Confirm Success" /></p>
                    <p className="text-uppercase mb-0">{t('order.placed')}</p>
                    <h4 className="text-success text-uppercase mb-0"><strong>{t('order.successfully')}</strong></h4>
                </div>
                <p className="text-center opacity-75">{t('order.visitPage')}</p>
                {orderType === OrderType.OP_LIMIT && <p className='text-center opacity-75'>{t('order.noteLimitOrder')}</p>}
            </div>
        </Modal>
    }, [showSuccessModal])

    const _renderErrorModal = useMemo(() => {
        return <Modal show={showErrorModal} className="modal-error" centered="true">
            {_renderMessageErrorMinPriceValue()}
            <div className='d-flex'>
                <button type="button" className="border w-50-pre text-center btn btn-default text-color-blue" onClick={() => setShowErrorModal(false)}>Cancel</button>
                <button type="button" className="border w-50-pre text-center btn btn-default text-color-blue" onClick={() => setShowErrorModal(false)}>Ok</button>
            </div>
        </Modal>
    }, [showErrorModal])

    const handleChangeVolume = (event) => {
        const newVol = event.value ? convertNumber(event.value) : null;
        const isGreaterMaxOrderVol = convertNumber(event.value) > convertNumber(maxOrderVolume);
        setOrder((prev) => ({
            ...prev,
            quantity: newVol
        }))
        setInvalidVolume(newVol > 0 && newVol < minLot);
        setIsMaxQty(isGreaterMaxOrderVol)
    }

    const handleChangeLimitPrice = (event) => {
        const price = convertNumber(event.value);
        setInvalidLimitPrice(price < floorPrice || price > ceilingPrice);
        setLimitPrice(price);
    }

    const checkValidNumber = (values) => {
        const { value, floatValue } = values;
        if (typeof floatValue === 'undefined' || typeof value === 'undefined') {
            return true;
        }
        if (isAllowed && (value.charAt(0) === '0' || value.charAt(0) === '-')) {
            return false;
        }
        if (props.maximum) {
            return floatValue <= props.maximum;
        } else {
            return true;
        }
    }

    const btnDisabled = () => {
        const isQuantity = order.quantity <= 0 || invalidVolume;
        const isGrossValue = calcGrossValue(order.quantity) <= 0;
        const isPasswordReqSubmit = pwdReqSubmit !== PASSWORD_REQ_SUBMIT.ON;
        const isPasswordValue = password === '';
        const minBuyQtyMarket = calcMinBuyQtyForMarketOrder();
        const minBuyQtyLimit = calcMinBuyQtyForLimitOrder();
        if (convertNumber(order.quantity) < convertNumber(minBuyQtyMarket) && sideBtn === ORDER_SIDE.BUY && orderType === OrderType.OP_MARKET) {
            return true;
        }
        if (convertNumber(order.quantity) < convertNumber(minBuyQtyLimit) && sideBtn === ORDER_SIDE.BUY && orderType === OrderType.OP_LIMIT) {
            return true;
        }
        if (!sideBtn) {
            return true;
        }
        if (isQuantity || isGrossValue || isMaxQty) {
            return true;
        }

        if (!checkVolumeLotSize(convertNumber(order?.quantity), convertNumber(symbolDetail?.lotSize))) {
            return true;
        }

        if (orderType === OrderType.OP_LIMIT && (convertNumber(limitPrice) < floorPrice || convertNumber(limitPrice) > ceilingPrice)) {
            return true;
        }
        if (orderType === OrderType.OP_LIMIT && !checkPriceTickSize(limitPrice, tickSize)) {
            return true;
        }
        if (limitPrice !== 0 && convertNumber(order?.quantity) !== 0 && calcGrossValue(convertNumber(order?.quantity)) > convertNumber(maxOrderValue)) {
            return true;
        }

        return isPasswordReqSubmit ? false : isPasswordValue;
    }

    const resetOrderForm = () => {
        setInvalidLimitPrice(false);
        setShowErrorModal(false)
    }

    const lastPriceCss = calcChange(symbol?.currentPrice, symbol?.prevClosePrice) >= 0 ? "fs-2 ls text-success me-2" : "fs-2 ls text-danger me-2";
    const chgCss = calcChange(symbol?.currentPrice, symbol?.prevClosePrice) >= 0 ? "text-success lh-sm" : "text-danger lh-sm";

    const placeBtn = () => {
        return sideBtn === ORDER_SIDE.BUY ? <button disabled={btnDisabled()} className="btn w-100 btn-success btn-place rounded-xl text-white text-uppercase" onClick={() => placeOrder()}>
            {orderType === OrderType.OP_MARKET && <strong>{t('order.placeBuyMarket')}</strong>}
            {orderType === OrderType.OP_LIMIT && <strong>{t('order.placeBuyLimit')}</strong>}
        </button>
            : <button disabled={btnDisabled()} className="btn w-100 btn-danger btn-place rounded-xl text-white text-uppercase" onClick={() => placeOrder()}>
                {orderType === OrderType.OP_MARKET && <strong>{t('order.placeSellMarket')}</strong>}
                {orderType === OrderType.OP_LIMIT && <strong>{t('order.placeSellLimit')}</strong>}
            </button>;
    }

    const bidVolume = useMemo(() => +getLastBidsElement(symbol)?.volume, [symbol])
    const sellVolume = useMemo(() => +getLastAsksElement(symbol)?.volume, [symbol])

    const calcPercentBid = () => {
        if (bidVolume + sellVolume !== 0) {
            if (!convertNumber(bidVolume) && convertNumber(sellVolume)) {
                return PROGRESS.none
            } else if (!convertNumber(sellVolume) && convertNumber(bidVolume)) {
                return PROGRESS.done
            } else {
                return (bidVolume * 100 / (bidVolume + sellVolume)).toString()
            }
        }
        return PROGRESS.none
    }

    const calcPercentAsk = () => {
        if (bidVolume + sellVolume !== 0) {
            if (!convertNumber(bidVolume) && convertNumber(sellVolume)) {
                return PROGRESS.done
            } else if (!convertNumber(sellVolume) && convertNumber(bidVolume)) {
                return PROGRESS.none
            } else {
                return (sellVolume * 100 / (bidVolume + sellVolume)).toString()
            }
        }
        return PROGRESS.none;
    }

    const calcMinBuyQtyForOrder = (price) => {
        if (convertNumber(minOrderValue) === 0 || convertNumber(price) === 0) return 0;
        // calc lotQty to gross value bigger than minOrdervalue
        let lotQty = new Decimal(convertNumber(minOrderValue)).div(Decimal(convertNumber(price))).toNumber();
        // check lotQty > minLot
        const temp = Math.max(lotQty, convertNumber(minLot));
        // minBuyQty is multiple of lotSize
        const result = new Decimal(temp).dividedBy(lotSize).ceil().mul(lotSize).toString();
        return result;
    }

    const calcMinBuyQtyForMarketOrder = () => {
        const marketPrice = getPriceAfterSpread(getLastAsksElement(symbol)?.price, 'sell', symbolDetail);
        return calcMinBuyQtyForOrder(marketPrice);
    }

    const calcMinBuyQtyForLimitOrder = () => {
        return calcMinBuyQtyForOrder(limitPrice);
    }

    const handleKeyDown = (e) => {
        e.key !== 'Delete' ? setIsAllow(true) : setIsAllow(false);
    }


    const renderClientInfoAccT = () => (
        <>
            <div className="justify-content-between align-items-center mb-2">
                <div className="opacity-50">
                    {t('order.buyLimit')}
                </div>
                <div className='text-end'>{formatNumber(order.maxPurchase, DIGIT_DEFAULT)} {CURRENCY_DISPLAY.SGD}</div>
            </div>
            <div className="justify-content-between align-items-center mb-2">
                <div className="opacity-50">
                    {t('order.sellLimit')}
                </div>
                <div className='text-end'>{formatNumber(order.sellLimit, DIGIT_DEFAULT)} {CURRENCY_DISPLAY.SGD}</div>
            </div>
        </>
    )

    const renderClientInfoAccC = () => (
        <>
            <div className="justify-content-between align-items-center mb-2">
                <div className="opacity-50">
                    {t('order.availableCash')}
                </div>
                <div className='text-end'>{formatNumber(order.cashBalance, DIGIT_DEFAULT)} {CURRENCY_DISPLAY.SGD}</div>
            </div>
            <div className="justify-content-between align-items-center mb-2">
                <div className="opacity-50">
                    {t('order.buyLimit')}
                </div>
                <div className='text-end'>{formatNumber(order.maxPurchase, DIGIT_DEFAULT)} {CURRENCY_DISPLAY.SGD}</div>
            </div>
            <div className="justify-content-between align-items-center mb-2">
                <div className="opacity-50">
                    {t('order.sellLimit')}
                </div>
                <div className='text-end'>{formatNumber(order.sellLimit, DIGIT_DEFAULT)} {CURRENCY_DISPLAY.SGD}</div>
            </div>
        </>
    )

    const renderClientInfoAccM = () => (
        <>
            <div className="justify-content-between align-items-center mb-2">
                <div className="opacity-50">
                    {t('order.availableCashWithoutMargin')}
                </div>
                <div className='text-end'>{formatNumber(calcCashWithoutMargin(), DIGIT_DEFAULT)} {CURRENCY_DISPLAY.SGD}</div>
            </div>
            <div className="justify-content-between align-items-center mb-2">
                <div className="opacity-50">
                    {t('order.maxPurchaseWithMargin')}
                </div>
                <div className='text-end'>{formatNumber(order.maxPurchase, DIGIT_DEFAULT)} {CURRENCY_DISPLAY.SGD}</div>
            </div>
            <div className="justify-content-between align-items-center mb-2">
                <div className="opacity-50">
                    {t('order.sellLimit')}
                </div>
                <div className='text-end'>{formatNumber(order.sellLimit, DIGIT_DEFAULT)} {CURRENCY_DISPLAY.SGD}</div>
            </div>
        </>
    )

    const renderClientInfoAccV = () => (
        <>
            <div className="justify-content-between align-items-center mb-2">
                <div className="opacity-50">
                    {t('order.availableCash')}
                </div>
                <div className='text-end'>{formatNumber(order.cashBalance, DIGIT_DEFAULT)} {CURRENCY_DISPLAY.SGD}</div>
            </div>
            <div className="justify-content-between align-items-center mb-2">
                <div className="opacity-50">
                    {t('order.maxPurchase')}
                </div>
                <div className='text-end'>{formatNumber(order.maxPurchase, DIGIT_DEFAULT)} {CURRENCY_DISPLAY.SGD}</div>
            </div>
            <div className="justify-content-between align-items-center mb-2">
                <div className="opacity-50">
                    {t('order.sellLimit')}
                </div>
                <div className='text-end'>{formatNumber(order.sellLimit, DIGIT_DEFAULT)} {CURRENCY_DISPLAY.SGD}</div>
            </div>
        </>
    )

    const _renderLimitPriceControl = () => {
        return (
            <>
            <div className="d-flex justify-content-between align-items-center mb-3">
                <div className="opacity-50" style={{ width: "80%" }}>
                    {t('order.limitPrice')}
                </div>
                <div onKeyDown={handleKeyDown}>
                    <NumberFormat thousandSeparator={true} decimalScale={2}
                        value={limitPrice !== 0 ? formatCurrency(limitPrice) : ''}
                        className="form-control text-end border-0 p-1 fs-5 input_number"
                        onValueChange={handleChangeLimitPrice}
                        isAllowed={(values) => checkValidNumber(values)}
                        maxLength={MAX_LENGTH_VOLUME}
                    />
                    {(convertNumber(limitPrice) < floorPrice || convertNumber(limitPrice) > ceilingPrice) && <span className='text-danger'>{t('message.outOfDailyPriceLimits')}</span>}
                    {!invalidLimitPrice && orderType === OrderType.OP_LIMIT && !checkPriceTickSize(limitPrice, tickSize) && <span className='text-danger text-nowrap'>{t('message.priceInvalid')}</span>}
                </div>
            </div>
        </>
        )
    }

    const _renderMaxVolumeMsg = useMemo(() => {
        return (
            isMaxQty && !invalidVolume && <div className='text-danger text-end'>Quantity exceeded max order <br />quantity: {renderNumber(maxOrderVolume)}</div>
        )
    }, [isMaxQty, invalidVolume])
    const getMarketAskBid = (side) => {
        const lastBids = getLastBidsElement(symbol);
        const lastAsks = getLastAsksElement(symbol);
        if (side === ORDER_SIDE.BUY) {
            return lastBids ? formatNumber(getPriceAfterSpread(lastBids.price, ORDER_SIDE.BUY, symbolDetail), DIGIT_DEFAULT) : '-'
        }
        return lastAsks ? formatNumber(getPriceAfterSpread(lastAsks.price, ORDER_SIDE.SELL, symbolDetail), DIGIT_DEFAULT) : '-'
    }
    return (
        <>
            <div>
                <input type="hidden" id="E2EE_PUBLIC_KEY" />
                <input type="hidden" id="E2EE_SESSION_ID" />
                <input type="hidden" id="E2EE_RANDOM_NUMBER" />
            </div>
            <div className="order-form border w-100" style={{ height: 'fit-content' }}>
                <div className="px-3 py-2 border-bottom bg-dark-light">
                    <div className="text-center fs-5"><strong>{symbolDetail?.symbolName}</strong></div>
                    <div className="text-center mb-2">
                        {symbol?.productType === ProductType.PRODUCT_TYPE_EQ && <span className="badge bg-info">{PRODUCT_TYPE[symbol?.productType || ProductType.PRODUCT_TYPE_NONE]}</span>}
                        {symbol?.productType === ProductType.PRODUCT_TYPE_ETF && <span className="badge bg-violet">{PRODUCT_TYPE[symbol?.productType || ProductType.PRODUCT_TYPE_NONE]}</span>}
                        <span className="opacity-50 ml-5">{symbolDetail?.symbolCode}</span></div>
                    <div className="d-flex align-items-center">
                        <div className="flex-grow-1">
                            <div className="d-flex align-items-center">
                                <div className={convertNumber(symbol?.currentPrice) !== 0 ? lastPriceCss : "fs-2 ls me-2"}>{formatNumber(symbol?.currentPrice, DIGIT_DEFAULT)}</div>
                                {convertNumber(symbol?.currentPrice) !== 0 && <div>
                                    <div className={chgCss}>{calcChange(symbol?.currentPrice, symbol?.prevClosePrice) > 0 ? "+" : ""}{formatNumber(calcChange(symbol?.currentPrice, symbol?.prevClosePrice), DIGIT_DEFAULT)}</div>
                                    <div className={chgCss}>{calcPctChange(symbol?.currentPrice, symbol?.prevClosePrice) > 0 ? "+" : ""}{formatNumber(calcPctChange(symbol?.currentPrice, symbol?.prevClosePrice), DIGIT_DEFAULT)}%</div>
                                </div>}
                                {convertNumber(symbol.currentPrice) === 0 && <div>
                                    <div>-</div>
                                    <div>-</div>
                                </div>}
                            </div>
                        </div>
                        <div className="ms-3">
                            <div className="opacity-50 lh-sm">{t('market.bid')}</div>
                            <div className="lh-sm">{getMarketAskBid(ORDER_SIDE.BUY)}</div>
                        </div>
                        <div className="ms-3">
                            <div className="opacity-50 lh-sm">{t('market.ask')}</div>
                            <div className="lh-sm">{getMarketAskBid(ORDER_SIDE.SELL)}</div>
                        </div>
                    </div>
                    <div className="row g-2 mb-2 small">
                        <div className="col-4">
                            <div className="d-flex justify-content-between align-items-center">
                                <div className="opacity-50">{t('market.vol')}</div>
                                <div>{formatNumber(symbol.volumePerDay)}</div>
                            </div>
                        </div>
                        <div className="col-4">
                            <div className="d-flex justify-content-between align-items-center">
                                <div className="opacity-50">{t('market.bvol')}</div>
                                <div>{getLastBidsElement(symbol) ? formatNumber(getLastBidsElement(symbol).lpVolume) : '-'}</div>
                            </div>
                        </div>
                        <div className="col-4">
                            <div className="d-flex justify-content-between align-items-center">
                                <div className="opacity-50">{t('market.svol')}</div>
                                <div>{getLastAsksElement(symbol) ? formatNumber(getLastAsksElement(symbol).lpVolume) : '-'}</div>
                            </div>
                        </div>
                    </div>
                    <div className="d-flex align-items-stretch" style={{ height: "0.25rem" }}>
                        {showBidPercent && <div className="flex-grow-1 bg-danger" style={{ width: calcPercentBid() + "%" }}>&nbsp;</div>}
                        {showAskPercent && <div className="flex-grow-1 bg-success" style={{ width: calcPercentAsk() + "%" }}>&nbsp;</div>}
                    </div>
                </div>
                <div className="px-3 py-2 bg-mid-order-panel">
                    <div className='row d-flex align-items-stretch mb-2'>
                        <div className={orderType === OrderType.OP_LIMIT ?
                            'col-6 text-end link-btn pointer' : 'col-6 text-end pointer'}
                            onClick={() => setOrderType(OrderType.OP_LIMIT)}>
                            Limit
                        </div>
                        <div className={orderType === OrderType.OP_MARKET ?
                            'col-6 link-btn pointer' : 'col-6 pointer'}
                            onClick={() => {
                                setOrderType(OrderType.OP_MARKET);
                            }} >
                            Market</div>
                    </div>
                    <div className="d-flex justify-content-center px-4 mb-2">
                        <div className="switch-button-custom order-for">
                            <button className={`buy-btn ${sideBtn === ORDER_SIDE.BUY ? 'bg-success' : 'bg-main'}`} onClick={() => setSideBtn(ORDER_SIDE.BUY)}> BUY</button>
                            <button className={`sell-btn ${sideBtn === ORDER_SIDE.SELL ? 'bg-danger' : 'bg-main'}`} onClick={() => setSideBtn(ORDER_SIDE.SELL)}>SELL</button>
                        </div>
                    </div>
                    {orderType === OrderType.OP_LIMIT && _renderLimitPriceControl()}
                    <div className="d-flex justify-content-between align-items-center">
                        <div className="opacity-50" style={{ width: "80%" }}>
                            {`${t('order.quantity')} (${t('order.lotSize')} ${symbolDetail?.lotSize ? symbolDetail.lotSize : 0})`}
                        </div>
                        <div onKeyDown={handleKeyDown}>
                            <NumberFormat thousandSeparator={true} decimalScale={0} value={convertNumber(order.quantity) !== 0 ? convertNumber(order.quantity) : ''}
                                className="form-control text-end border-0 p-1 fs-5 input_number"
                                onValueChange={handleChangeVolume}
                                isAllowed={(values) => checkValidNumber(values)}
                                maxLength={MAX_LENGTH_VOLUME}
                            />
                        </div>
                    </div>
                    {(invalidVolume || !checkVolumeLotSize(convertNumber(order?.quantity), convertNumber(symbolDetail?.lotSize))) && <div className='text-danger text-end'>Invalid quantity</div>}
                    {_renderMaxVolumeMsg}
                    {limitPrice !== 0 && convertNumber(order?.quantity) !== 0 && calcGrossValue(convertNumber(order?.quantity)) > convertNumber(maxOrderValue) &&
                        <div className='text-danger text-end'>Gross value is exceed max order value: {formatNumber(maxOrderValue?.toString())}</div>
                    }
                    {orderType === OrderType.OP_MARKET && sideBtn === ORDER_SIDE.BUY && !isNaN(minOrderValue) && getLastAsksElement(symbol)?.price && (
                        <div className='text-end opacity-50 fs-px-11'>Minimum Buy Qty: {calcMinBuyQtyForMarketOrder()}</div>
                    )}
                    {orderType === OrderType.OP_LIMIT && sideBtn === ORDER_SIDE.BUY && !isNaN(minOrderValue) && (
                        <div className='text-end opacity-50 fs-px-11'>Minimum Buy Qty: {calcMinBuyQtyForLimitOrder()}</div>
                    )}
                    <hr className="my-3" />
                    <div className="d-flex justify-content-between align-items-center">
                        <div className="opacity-50">
                            {t('order.paymentType')}
                        </div>
                        <div>
                            <button className="btn rounded-xl bg-info px-2 py-0">{t('order.cash')}</button>
                        </div>
                    </div>
                    <hr className="my-2" />
                    <div className="d-flex justify-content-between align-items-end">
                        <div className="opacity-50">
                            {t('order.settlement')}<br />{t('order.ccy')}
                        </div>
                        <div>
                            <div className="btn-group btn-group-switch" role="group" aria-label="">
                                <input type="radio" className="btn-check" name="settlement_currency" id="settlement_currency_sgd" checked={baseSGD} onChange={() => changeCcy(CURRENCY.SGD)} />
                                <label className="btn btn-sm py-0 btn-outline-info" htmlFor="settlement_currency_sgd">SGD</label>

                                <input type="radio" className="btn-check" name="settlement_currency" id="settlement_currency_usd" checked={baseUSD} onChange={() => changeCcy(CURRENCY.USD)} />
                                <label className="btn btn-sm py-0 btn-outline-info" htmlFor="settlement_currency_usd">USD</label>
                            </div>
                        </div>
                    </div>
                    <hr className="my-2" />

                    {pwdReqSubmit === PASSWORD_REQ_SUBMIT.ON && <div className='d-flex justify-content-between align-items-center'>
                        <div className='opacity-50 me-3'>
                            {t('order.confirmPassword')}
                        </div>
                        <div className='d-flex justify-content-between'>
                            <input autoComplete='off' type={!isShowPassword ? 'password' : ''}
                                value={password} id="txtPassword"
                                className="form-control"
                                onChange={(e) => setPassword(e.target.value)}
                                required="required" title="" />
                            {password.trim() && <span className='mt-2 ml-rem' onClick={(e) => setIsShowPassword(!isShowPassword)}>
                                <i className={`ml-rem bi ${isShowPassword ? 'bi-eye-fill' : 'bi-eye-slash-fill'}`}></i></span>}
                        </div>
                    </div>}

                    {pwdReqSubmit === PASSWORD_REQ_SUBMIT.ON && <hr className='my-2' />}

                    {psplAcctType === PSPL_ACCOUNT_TYPE.T && renderClientInfoAccT()}
                    {psplAcctType === PSPL_ACCOUNT_TYPE.C && renderClientInfoAccC()}
                    {psplAcctType === PSPL_ACCOUNT_TYPE.M && renderClientInfoAccM()}
                    {psplAcctType === PSPL_ACCOUNT_TYPE.V && renderClientInfoAccV()}
                </div>
                <div className="px-3 py-2 bg-dark-light">
                    <div className="d-flex justify-content-between align-items-center">
                        <div className="opacity-50 text-uppercase">
                            {t('order.grossValue')}
                        </div>
                        <div>{formatNumber(calcGrossValue(order?.quantity), DIGIT_DEFAULT)} {CURRENCY_DISPLAY.USD}</div>
                    </div>
                    <div className="mt-2">
                        {placeBtn()}
                    </div>
                </div>

                <ToastContainer theme="colored" />
                {isShowlazyLoad && LazyLoad()}
                {_renderConfirmModal()}
                {_renderSuccessModal}
                {_renderErrorModal}
                {isShowError && <PopupTimeout title={titleError} setErrorFunc={setIsShowError} />}
            </div>
        </>
    )
}

export default memo(OrderPannel);