import { Subject } from 'rxjs';
import { LocalStorage<PERSON>ey, SocketKey } from '../model/constant';
import { convertNumber } from '../model/utils'
import rpc from '../model/proto/rpc_pb';
import queryService from '../model/proto/query_service_pb';
import pricingService from '../model/proto/pricing_service_pb';
import tradingService from '../model/proto/trading_service_pb';
import systemService from '../model/proto/system_service_pb';
import queryString from 'query-string';
import jwt_decode from 'jwt-decode';
import { onSessionTimeout } from './trigger-mobile';
import moment from 'moment';

const paramStr = window.location.search;
const queryParam = queryString.parse(paramStr);
var socket = null;
var wsConnected = false;
var numberReconnect = 0;

const socketSubject = new Subject();
const symbolListSubject = new Subject();
const lastQuoteSubject = new Subject();
const unsubscribeQuoteSubject = new Subject();
const subscribeQuoteSubject = new Subject();
const quoteSubject = new Subject();
const orderSubject = new Subject();
const orderHistorySubject = new Subject();
const tradeHistorySubject = new Subject();
const modifySubject = new Subject();
const cancelSubject = new Subject();
const orderEventSubject = new Subject();
const warningMessageSubject = new Subject();
let isRender = true;

const startWs = () => {
    let token = sessionStorage.getItem(LocalStorageKey.TOKEN);
    if (queryParam && queryParam?.token) {
        token = queryParam?.token;
        sessionStorage.setItem(LocalStorageKey.TOKEN, token);
        const decoded = jwt_decode(token);
        if (decoded && decoded?.account_id) {
            sessionStorage.setItem(LocalStorageKey.ACCOUNT_ID, decoded?.account_id);
            sessionStorage.setItem(LocalStorageKey.POEM_ID, decoded?.poem_id)
            sessionStorage.setItem(LocalStorageKey.PWD_REQ_SUBMIT, decoded?.pwd_req_submit);
            sessionStorage.setItem(LocalStorageKey.PWD_REQ_AMEND, decoded?.pwd_req_amend);
            sessionStorage.setItem(LocalStorageKey.PWD_REQ_WITHDRAW, decoded?.pwd_req_withdraw);
            sessionStorage.setItem(LocalStorageKey.TOKEN_EXPIRED, decoded?.exp);
            localStorage.setItem(LocalStorageKey.MIN_ORDER_VALUE, decoded?.min_order_value);
            localStorage.setItem(LocalStorageKey.ACK_FLAG, decoded?.ack_flag);
            localStorage.setItem(LocalStorageKey.MAX_ORDER_VOLUME, decoded?.max_order_volume);
            localStorage.setItem(LocalStorageKey.MAX_ORDER_VALUE, decoded?.max_order_value);
            sessionStorage.setItem(LocalStorageKey.THEME, decoded?.theme);
        }
    }

    if (!token) {
        return;
    }
    const socket_url = `${window.wsUrl}?token=${token}&account_type=retail`;

    socket = new WebSocket(socket_url);
    socket.binaryType = "arraybuffer";

    socket.onopen = (e) => {
        console.log(moment().format('YYYY-MM-DD HH:mm:ss'), "ON OPEN", e);
        wsConnected = true;
        numberReconnect = 0;
        if (isRender) {
            socketSubject.next(SocketKey.SOCKET_CONNECTED);
        } else {
            socketSubject.next(SocketKey.SOCKET_RECONNECTED);
        }
    }

    socket.onerror = (e) => {
        console.log(moment().format('YYYY-MM-DD HH:mm:ss'), "ON ERROR", e);
        socket.close();
        wsConnected = false;
        const tokenExpTime = sessionStorage.getItem(LocalStorageKey.TOKEN_EXPIRED);
        const currentTime = moment().utc().valueOf();
        if (currentTime >= convertNumber(tokenExpTime)) {
            socketSubject.next(SocketKey.SOCKET_EXPIRE_TOKEN);
            // Trigger for mobile
            onSessionTimeout();
        }
    }

    socket.onclose = (e) => {
        console.log(moment().format('YYYY-MM-DD HH:mm:ss'), "ON CLOSE", e);
        wsConnected = false;
        isRender = false;
        setTimeout(function () { startWs(token) }, 5000);
        numberReconnect = numberReconnect + 1;
        if (numberReconnect >= 3) socketSubject.next(SocketKey.MAX_RETRY_WS);
    }

    socket.onmessage = (e) => {
        const msg = rpc.RpcMessage.deserializeBinary(e.data);
        const payloadClass = msg.getPayloadClass();
        // eslint-disable-next-line default-case
        switch(payloadClass) {
            case rpc.RpcMessage.Payload.SYMBOL_LIST_RES: {
                const symbolListRes = queryService.SymbolListResponse.deserializeBinary(msg.getPayloadData());
                symbolListSubject.next(symbolListRes.toObject());
                break;
            }
            case rpc.RpcMessage.Payload.LAST_QUOTE_RES: {
                const lastQuoteRes = pricingService.GetLastQuotesResponse.deserializeBinary(msg.getPayloadData());
                lastQuoteSubject.next(lastQuoteRes.toObject());
                break;
            }
            case rpc.RpcMessage.Payload.UNSUBSCRIBE_QUOTE_RES: {
                const unsubscribeQuoteRes = pricingService.UnsubscribeQuoteEventResponse.deserializeBinary(msg.getPayloadData());
                unsubscribeQuoteSubject.next(unsubscribeQuoteRes.toObject());
                break;
            }
            case rpc.RpcMessage.Payload.SUBSCRIBE_QUOTE_RES: {
                const subscribeQuoteRes = pricingService.SubscribeQuoteEventResponse.deserializeBinary(msg.getPayloadData());
                subscribeQuoteSubject.next(subscribeQuoteRes.toObject());
                break;
            }
            case rpc.RpcMessage.Payload.QUOTE_EVENT: {
                const quoteEvent = pricingService.QuoteEvent.deserializeBinary(msg.getPayloadData());     
                quoteSubject.next(quoteEvent.toObject());
                break;
            }
            case rpc.RpcMessage.Payload.NEW_ORDER_SINGLE_RES: {
                const singleOrderRes = tradingService.NewOrderSingleResponse.deserializeBinary(msg.getPayloadData());
                orderSubject.next(singleOrderRes.toObject());
                break;
            }
            case rpc.RpcMessage.Payload.ORDER_HISTORY_RES: {
                const orderHistory = queryService.GetOrderHistoryResponse.deserializeBinary(msg.getPayloadData());
                orderHistorySubject.next(orderHistory.toObject());
                break;
            }
            case rpc.RpcMessage.Payload.TRADE_HISTORY_RES: {
                const tradeHistory = queryService.GetTradeHistoryResponse.deserializeBinary(msg.getPayloadData());
                tradeHistorySubject.next(tradeHistory.toObject());
                break;
            }
            // eslint-disable-next-line no-fallthrough
            case rpc.RpcMessage.Payload.MODIFY_ORDER_RES: {
                const modifyRes = tradingService.ModifyOrderResponse.deserializeBinary(msg.getPayloadData());
                modifySubject.next(modifyRes.toObject());
                break;
            }
            // eslint-disable-next-line no-fallthrough
            case rpc.RpcMessage.Payload.CANCEL_ORDER_RES: {
                const cancelRes = tradingService.CancelOrderResponse.deserializeBinary(msg.getPayloadData());
                cancelSubject.next(cancelRes.toObject());
                break;
            }
            // eslint-disable-next-line no-fallthrough
            case rpc.RpcMessage.Payload.ORDER_EVENT: {
                const orderEvent = tradingService.OrderEvent.deserializeBinary(msg.getPayloadData());
                orderEventSubject.next(orderEvent.toObject());
                break;
            }
            case rpc.RpcMessage.Payload.WARNING_MESSAGE_REQ: {
                const warningMessageReq = systemService.WarningMessageRequest.deserializeBinary(msg.getPayloadData());
                warningMessageSubject.next(warningMessageReq.toObject());
                break;
            }
        }
    }

    setInterval(() => {
        console.log(moment().format('YYYY-MM-DD HH:mm:ss'), "PING");
        socket.send("PING")
    }, 30000)

}

export const wsService = {
    getWsConnected: () => wsConnected,
    startWebsocket: () => startWs(),
    getSocketSubject: () => socketSubject.asObservable(),
    sendMessage: message => socket.send(message),
    getSymbolListResponse: () => symbolListSubject.asObservable(),
    getLastquoteResponse: () => lastQuoteSubject.asObservable(),
    unsubscribeQuoteResponse: () => unsubscribeQuoteSubject.asObservable(),
    subscribeQuoteResponse: () => subscribeQuoteSubject.asObservable(),
    getQuoteEventResponse: () => quoteSubject.asObservable(),
    getSingleOrderResponse: () => orderSubject.asObservable(),
    getOrderHistoryResponse: () => orderHistorySubject.asObservable(),
    getTradeHistoryResponse: () => tradeHistorySubject.asObservable(),
    getModifySubject: () => modifySubject.asObservable(),
    getCancelSubject: () => cancelSubject.asObservable(),
    getOrderEventResponse: () => orderEventSubject.asObservable(),
    getWarningMessage: () => warningMessageSubject.asObservable(),
}