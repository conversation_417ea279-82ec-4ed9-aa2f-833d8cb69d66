syntax = "proto3";
import "system_model.proto";

//using for single application such as Pi-X
message LoginRequest {
	string login		= 1;
	string password		= 2;
	string mac_address 	= 3;
	string local_ip 	= 4;
}

//using for single application such as Pi-X
message LoginResponse {
	MsgCode returnCode								= 1;
  	string returnMsg 						= 2;
	int64 partner_id						= 3;
	string token									= 4;

	//return data
}

//Heartbeat request
message HeartbeatRequest {
	int64 sending_time = 1;
}

message HeartbeatResponse {
	int64 sending_time = 1;
}

//Check session
message CheckSessionResponse {
	int64 account_id            = 1;
	SessionStatusCode res_code  = 2;
}

//Balance update
message BalanceUpdateRequest {
	int64 account_id 	= 1;
	DealAction type		= 2;
	string amount		= 3;	//minus -> withdraw
	string comment		= 4;
}

message BalanceUpdateResponse {
	MsgCode msg_code				= 1;
	string msg_text		= 2;
	int64 ticket			= 3;	//transaction id
}

//Account create
message AccountCreateRequest {
	Account account = 1;
}

message AccountCreateResponse {
	MsgCode msg_code				= 1;
	string msg_text		= 2;
	Account account 				= 3;
}

//Account update
message AccountUpdateRequest {
	int64 account_id 					= 1;
	string name 				= 2;
	int64 group_id 			= 3;
	bool enable_flg 			= 4;
	TradingRights trading_rights			= 5;
	bool api_flg				= 6;
	string password			= 7;
	string comment 			= 8;
	string api_key 			= 9;
	string secret_key			= 10;	//trading pin

	string new_password = 11;
	string new_secret_key = 12; // New Trading PIN
	
	// Setting notifications
	BoolFlag recv_admin_news_flg = 13;
	BoolFlag recv_match_noti_flg = 14;
	
	BoolFlag enable_secret_key_flg = 15; // Setting enable trading PIN

	// Have to use enum with NONE value for bool flag because:
	// There's no way of telling whether a field was explicitly set to
	// the default value (for example whether a boolean was set to false) or just not set at all
	enum BoolFlag {BOOL_FLAG_NONE = 0; BOOL_FLAG_ON = 1; BOOL_FLAG_OFF = 2;}
	enum TradingRights { TRADING_RIGHTS_NONE = 0; TRADING_OPEN_ONLY = 1; TRADING_CLOSE_ONLY = 2; TRADING_NORMAL = 10; }
}

message AccountUpdateResponse {
	MsgCode msg_code					= 1;
	string msg_text			= 2;
	Account account 					= 3;
}

//Multiple Account create
message AccountCreateMultiRequest {
	repeated Account account = 1;
}
message AccountCreateMultiResponse {
	MsgCode msg_code				= 1;
	string msg_text		= 2;
	repeated Account account 				= 3;
}


//Get detail information of Account
message AccountDetailRequest {
	int64 account_id 					= 1;
}

message AccountDetailResponse {
	MsgCode msg_code					= 1;
	string msg_text			= 2;
	Account account 					= 3;
}

//Get account balance info
message AccountBalanceRequest {
	int64 account_id 					= 1;
}

message AccountBalanceResponse {
	MsgCode msg_code 					= 1;
	string  msg_text					= 2;
	AccountBalance account_balance		= 3;
}

//Get account portfolio info
message AccountPortfolioRequest {
	repeated int64 account_id 					= 1;
}

message AccountPortfolioResponse {
	MsgCode msg_code 					= 1;
	string  msg_text					= 2;
	repeated int64 account_id 					= 3;
	string currency_code 				= 4;
	repeated AccountPortfolio account_portfolio	= 5;
}

// Update admin settings
message UpdateAdminSettingsRequest {
	AdminSettings admin_settings = 1;
}

message UpdateAdminSettingsResponse {
	MsgCode msg_code 					= 1;
	string  msg_text					= 2;
}

//Multiple group create
message GroupCreateRequest {
	repeated GroupAccount group_account = 1;
}
message GroupCreateResponse {
	MsgCode msg_code				= 1;
	string msg_text		= 2;
	repeated GroupAccount group_account 				= 3;
}

// Update group info
message GroupUpdateRequest {
	repeated GroupAccount group_account 				= 1;
}

message GroupUpdateResponse {
	MsgCode msg_code					= 1;
	string msg_text			= 2;
}

// Open Trading Session
message OpenTradingSessionRequest {
}

// Open Trading Session
message OpenTradingSessionResponse {
	MsgCode msg_code		= 1;
	string msg_text			= 2;
}

// Close Trading Session
message CloseTradingSessionRequest {
}

// Close Trading Session
message CloseTradingSessionResponse {
	MsgCode msg_code		= 1;
	string msg_text			= 2;
}

// Pre Trading Session
message PreTradingSessionRequest {
}

// Pre Trading Session
message PreTradingSessionResponse {
	MsgCode msg_code		= 1;
	string msg_text			= 2;
}

// Close External Trading Session
message CloseExternalTradingSessionRequest {		//US Summer Time in Blue Ocean, cancel all unexecuted leave orders in BO
}

// Close External Trading Session
message CloseExternalTradingSessionResponse {
	MsgCode msg_code		= 1;
	string msg_text			= 2;
}

// Unlock After Close External Trading
message UnlockAfterCloseExternalTradingRequest {	//BO is closed, get all orders in waitinglist and send to start matching process
}

// Unlock After Close External Trading
message UnlockAfterCloseExternalTradingResponse {
	MsgCode msg_code		= 1;
	string msg_text			= 2;
}


// Reset Dashboard End Of Trading Day
message ResetOHLCRequest {						//Market closed, reset some fields: OHLC, Volume, Change, Change%
}

// Reset Dashboard End Of Trading Day
message ResetOHLCResponse {
	MsgCode msg_code		= 1;
	string msg_text			= 2;
}

// Market data configuration
message MarketDataConfigRequest {
	repeated MarketDataSource md_source	= 1;
}

message MarketDataConfigResponse {
	MsgCode msg_code		= 1;
	string msg_text			= 2;
}

message WarningMessageRequest {
	string	content			= 1;
	bool	enable_flg		= 2;
}

message WarningMessageResponse {
	MsgCode msg_code		= 1;
	string msg_text			= 2;
}

message CloseLastMarketRequest {
	string market_code		= 1;
}

message CloseLastMarketResponse {
	MsgCode msg_code		= 1;
	string msg_text			= 2;
}

message OpenNextTradingSessionRequest {
	string market_code		= 1;
}

message OpenNextTradingSessionResponse {
	MsgCode msg_code		= 1;
	string msg_text			= 2;
}

message MarketSettingsRequest {
	repeated MarketSettings market_settings	= 1;
}

message MarketSettingsResponse {
	MsgCode msg_code		= 1;
	string msg_text			= 2;
}

// Reset OHLC & clear order book for US24
message ResetMarketDataRequest {
}

message ResetMarketDataResponse {
	MsgCode msg_code		= 1;
	string msg_text			= 2;
}