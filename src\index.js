import React from 'react';
import ReactDOM from 'react-dom';
import './index.css';
import App from './App';
import reportWebVitals from './reportWebVitals';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from './redux/store/configureStore';
import { LocalStorageKey } from './model/constant';
import jwt_decode from 'jwt-decode';
import queryString from 'query-string';
import { wsService } from './services/websocket-service';

const paramStr = window.location.search;
const queryParam = queryString.parse(paramStr);

ReactDOM.render(
  <Provider store={store}>
    <PersistGate persistor={persistor}>
      <React.StrictMode>
        <App />
      </React.StrictMode>
    </PersistGate>
  </Provider>,
  document.getElementById('root')
);

window.addEventListener("message", (event) => {
  console.log(`Received message event with origin=${event.origin}`);
  if (typeof(event.data) === 'string') {
    const token = event.data;
    if (token) {
      // Bug #83148 prevent start WS before receive token from POEM
      sessionStorage.setItem(LocalStorageKey.TOKEN, token);
      wsService.startWebsocket()
      const decoded = jwt_decode(token);
      console.log("theme: ", decoded?.theme);
      if (decoded && decoded?.account_id) {
          sessionStorage.setItem(LocalStorageKey.ACCOUNT_ID, decoded?.account_id);
          sessionStorage.setItem(LocalStorageKey.POEM_ID, decoded?.poem_id)
          sessionStorage.setItem(LocalStorageKey.PWD_REQ_SUBMIT, decoded?.pwd_req_submit);
          sessionStorage.setItem(LocalStorageKey.PWD_REQ_AMEND, decoded?.pwd_req_amend);
          sessionStorage.setItem(LocalStorageKey.PWD_REQ_WITHDRAW, decoded?.pwd_req_withdraw);
          sessionStorage.setItem(LocalStorageKey.TOKEN_EXPIRED, decoded?.exp);
          localStorage.setItem(LocalStorageKey.MIN_ORDER_VALUE, decoded?.min_order_value);
          localStorage.setItem(LocalStorageKey.ACK_FLAG, decoded?.ack_flag);
          localStorage.setItem(LocalStorageKey.MAX_ORDER_VOLUME, decoded?.max_order_volume);
          localStorage.setItem(LocalStorageKey.MAX_ORDER_VALUE, decoded?.max_order_value);
          sessionStorage.setItem(LocalStorageKey.THEME, decoded?.theme);
      }
    }
  }
})

if(queryParam && queryParam?.token) {
  const token = queryParam.token;
  sessionStorage.setItem(LocalStorageKey.TOKEN, token);
  wsService.startWebsocket()
}

window.addEventListener("load", () => {
  const flag_local = sessionStorage.getItem(LocalStorageKey.DETECT_SIDE_NOT_EMBED)
  if(flag_local === 'true') {
    wsService.startWebsocket()
  }
})
// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
