import { useEffect, useRef, useState } from "react";
import Header from "../components/header";
import Pagination from "react-js-pagination";
import { API_GET_NEWS, API_MARK_READ_NEWS } from "./../constants/api.constant";
import axios from 'axios';
import { defindConfigGet, formatDate, defindConfigPost, stripHtmlTagsFromString } from "../model/utils";
import { success } from "../constants/announcement";
import parse from "html-react-parser";
import { useDispatch } from 'react-redux';
import { updateNews } from '../redux/actions/news'

const Announcement = () => {
    const api_url = window.apiUrl;
    const [notify, setNotify] = useState([]);
    const [notifyDetail, setNotifyDetail] = useState();
    const [isUnread, setIsUnread] = useState(false);
    const [elActive, setElActive] = useState();
    const dispatch = useDispatch();
    const [paging, setPaging] = useState({
        pageIndex: 1,
        pageSize: 5,
        totalRecord: 0
    });


    const urlGet = `${api_url}${API_GET_NEWS}`;
    const urlPMarkReadNews = `${api_url}${API_MARK_READ_NEWS}`

    useEffect(() => {
        getDataNews(paging.pageIndex);
    }, [paging.pageSize, paging.totalRecord, isUnread])

    const handleNewsReaded = (idNews) => {
        const url = `${urlPMarkReadNews}/${idNews}/read-flag`
        axios.post(url, '', defindConfigPost()).then((resp) => {
            if (resp?.data?.meta?.code === success) {
                getDataNews(paging.pageIndex);
                dispatch(updateNews());
            }
        },
            (error) => {
                console.log(error);
            });
    }

    const onPageChange = (pageIndexCurrent) => {
        getDataNews(pageIndexCurrent);
    }

    const getParams = (page) => {
        return isUnread ? {
            page_size: paging.pageSize,
            page: page,
            read_flag: false // read_flag = false  --> news unread
        } : {
            page_size: paging.pageSize,
            page: page,
        }
    }

    const getDataNews = (page) => {
        const paramNews = getParams(page);
        axios.get(urlGet, defindConfigGet(paramNews)).then((resp) => {
            if (resp.status === success) {
                setPaging({
                    ...paging,
                    pageIndex: page,
                    totalRecord: resp.data.data.count
                })
                setNotify(resp.data.data.results);
            }
        },
            (error) => {
                console.log(error);
            });
    }

    const onChangePageSize = (event) => {
        let pageSize = parseInt(event.target.value);
        setPaging({
            ...paging,
            pageSize: pageSize,
            pageIndex: 1
        })
    }

    const handleNotiDetail = (itemNew, index) => {
        setElActive(index);
        if (itemNew) {
            setNotifyDetail(itemNew);
            if (!itemNew.read_flag) {
                handleNewsReaded(itemNew?.id);
            }
        }
    }

    useEffect(() => {
        if(!notifyDetail) return;
        
        const element = document.getElementById("noti-detail");
        element.scrollIntoView({behavior: "smooth", block: "end", inline: "end"});
    }, [notifyDetail])

    const getStyleItemPost = (isReadFlag, elActive, index) => {
        const el = !isReadFlag ? "post-item unread" : "post-item";
        const isActive = elActive === index ? 'post-active' : '';
        return `${el} ${isActive}`;
    }
    const _renderNotificationList = (listDataNewCurr) => {
        return <div className="col-xl-5 col-xxl-4 col-lg-6">
            <div className="post-list border mb-3 mh-news">
                {listDataNewCurr?.map((notify, idx) => {
                    return <div className={getStyleItemPost(notify.read_flag, elActive, idx)} key={idx} onClick={() => handleNotiDetail(notify, idx)}>
                        <div className="item-header">
                            <div className="item-icon"><i className="bi bi-bell"></i></div>
                            <div className="item-meta"><span className="item-datetime fs-12">{formatDate(notify?.publish_date)}</span></div>
                            <h5 title={notify?.news_title} className="item-title text-truncate mw-all">{notify?.news_title}</h5>
                            <div className="annoucement-notice text-truncate" style={{maxWidth: '600px'}}>{stripHtmlTagsFromString(notify?.news_content)}</div>
                        </div>
                    </div>
                })}
            </div>
            {_renderPaging()}
        </div>
    }
    const _renderPaging = () => {
        return <div className="row">
            <div className="col-md-5 col-7 mt-md-0 mt-1" style={{ marginTop: "-14px", marginBottom: "10px" }}>
                <div className="d-flex justify-content-between align-items-center">
                    <span className="me-2">Show</span>
                    <select className="form-select me-2 ps-2" value={paging.pageSize} onChange={e => onChangePageSize(e)}>
                        <option value="5">5</option>
                        <option value="10">10</option>
                        <option value="20">20</option>
                    </select>
                    <span>entries</span>
                </div>
            </div>
            <div className="col-md-7 col-12">
                <Pagination
                    activePage={paging.pageIndex}
                    itemsCountPerPage={paging.pageSize}
                    totalItemsCount={paging.totalRecord}
                    pageRangeDisplayed={3}
                    onChange={(e) => onPageChange(e)}
                    itemClass="page-item"
                    linkClass="page-link"
                />
            </div>
        </div>
    }

    const _renderNotificationDetail = () => {
        return <div className="col-xxl-8 col-xl-7 col-lg-6" id="noti-detail">
            <div className="post-detail border ">
                <div className="post-header mb-2 p-3">
                    <h4 className="post-title mb-0 pe-3">{notifyDetail?.news_title}</h4>
                    <div className="post-meta opacity-50"><span className="post-datetime">{notifyDetail?.publish_date ? formatDate(notifyDetail?.publish_date) : ''}</span></div>
                    <button className="btn btn-close btn-close-white" onClick={(e) => setNotifyDetail('')} >&nbsp;</button>
                </div>
                <div className="item-content lead p-3 mh-detail">
                    {notifyDetail ? parse(notifyDetail?.news_content) : ''}
                </div>
            </div>
        </div>
    }

    const btnChangeShowUnread = (event) => {
        setPaging(paging => ({
            ...paging,
            pageIndex: 1
        }))
        setIsUnread(event.target.checked);
        setNotifyDetail('');
    }

    return (
        <div className="site">
            <Header page="announcement" />

            <div className="site-main py-3">
                <div className="container">
                    <div className="card">
                        <div className="card-header">
                            <h5 className="card-title mb-0">Announcement</h5>
                        </div>
                        <div className="card-body">
                            <div className="d-flex justify-content-between align-items-center pb-3 border-bottom mb-3">
                                <h6 className="text-info-light mb-0">US (Asian Hrs)</h6>
                                <div>
                                    <div className="form-check form-switch">
                                        <input className="form-check-input" checked={isUnread} type="checkbox" onChange={btnChangeShowUnread} role="switch" id="unread" />
                                        <label className="form-check-label" htmlFor="unread">Only show unread notifications</label>
                                    </div>
                                </div>
                            </div>
                            <div className="row g-3">
                                {_renderNotificationList(notify)}
                                {notifyDetail && _renderNotificationDetail()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default Announcement;