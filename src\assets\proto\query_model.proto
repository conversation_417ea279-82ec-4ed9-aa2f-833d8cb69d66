syntax = "proto3";

message Symbol {
  int32 symbol_id = 1;
  string symbol_code = 2;
  string symbol_name = 3;
  CalculationMode calculation_mode = 4;
  int64 contract_size = 5;
  int64 digits = 6;
  string exchange					= 7;
  string currency_code			= 8;
  string description				= 9;
  string tick_size					= 10;
  string lot_size					= 11;
  string min_lot				= 12;
  string ceiling = 13;
  string floor        = 14;
  string limit_rate   = 15;
  string spread = 16;
  string prev_close_price = 17; // LP Previous closing price
  string retail_prev_close_price = 18; // Retail Previous closing price
  SymbolStatus symbol_status = 19;
  string symbol_suffix = 20;
  string gbo_sec_code = 21; //Security Code from GBO
  ProductType product_type = 22;
  MatchingMethod matching_method = 23;
  bool cowen_flg = 24; //True -> Send order to <PERSON><PERSON>, False -> Don't send order to Cowen
  MatchingRule matching_rule = 25;
}

enum CalculationMode {
	CAL_NONE = 0;
	CAL_CFD = 4;
	CAL_SPAN = 90;
	CAL_FOREX = 100;
}

enum SymbolStatus {
	SYMBOL_NONE = 0;
	SYMBOL_ACTIVE = 1;
	SYMBOL_DEACTIVE = 2;
}

enum ProductType {
	PRODUCT_TYPE_NONE = 0;
	PRODUCT_TYPE_EQ = 1;
	PRODUCT_TYPE_ETF = 2;
}

enum MatchingMethod {
	MATCHING_METHOD_NONE = 0;
	MATCHING_METHOD_EQ = 1; //Ticker is matched following to Equity matching process
	MATCHING_METHOD_ETF = 2; //Ticker is matched following to ETF matching process
}

enum MatchingRule {
	MATCHING_RULE_NONE = 0;
	MATCHING_RULE_USAH = 1; //Ticker is matched only in USAH
	MATCHING_RULE_BO = 2; //Ticker is matched only in BO
	MATCHING_RULE_USAH_BO = 3; //Ticker is matched in USAH and BO, priority in USAH
}
