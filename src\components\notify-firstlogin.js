import axios from "axios";
import { useState } from "react";
import { useEffect } from "react";
import { success } from "../constants/announcement";
import { API_CONFIRM_ACKNOWLEDGEMENT } from "../constants/api.constant";
import { SCREEN_SIZE_WEB_MIN } from "../constants/general";
import { LocalStorageKey, UNSTABLE_CONNECTION } from "../model/constant";
import { defindConfigPost } from "../model/utils";
import LazyLoad from "./lazy-load";
import { toast } from 'react-toastify';
import { _renderChangePrice, _renderLowerLiquidity, _renderLowerVolatility, _renderNewsAnnouncements, _renderNoteAndRiskDisctosure, _renderUnlinkedMarket, _renderWiderSpreads } from "../constants/notifyFirstLogin";
const NotifyFirstLogin = (props) => {
     const { isCloseNotifirstLogin } = props
     const [sizeScreen, setSizeScreen] = useState(window.innerWidth);
     const [isChecked, setIsChecked] = useState(false);
     const [isShowlazyLoad, setIsShowLazyLoad] = useState(false);
     useEffect(() => {
          window.addEventListener('resize', () => setSizeScreen(window.innerWidth));
          const path = window.location.pathname;
          if (path.includes('/login')) {
               isCloseNotifirstLogin(false)
          }
          return () => {
               window.removeEventListener('resize', () => setSizeScreen(window.innerWidth));
          };
     }, [])

     const handleConfirm = () => {
          setIsShowLazyLoad(true);
          axios.post(`${window.apiUrl}${API_CONFIRM_ACKNOWLEDGEMENT}`, '', defindConfigPost()).then(resp => {
               if (resp?.data?.meta?.code === success) {
                    localStorage.setItem(LocalStorageKey.ACK_FLAG, JSON.stringify(true));
                    isCloseNotifirstLogin(false);
               }
               setIsShowLazyLoad(false);
          },
          err => {
               console.log("exception: ", err)
               setIsShowLazyLoad(false);
               toast.error(UNSTABLE_CONNECTION);
          })
     }
    
     return <>
     <div>
         <div className="fade modal-backdrop opacity-100 show"></div>
          <div role="dialog" aria-modal="true" className="fade modal show d-block" tabIndex="-1">
               <div className={sizeScreen >= SCREEN_SIZE_WEB_MIN ? 'modal-notify' : 'notify-mobile'}>
                    <div className="modal-content">
                         <div className="m-3">
                              <h4>US (Asian Hrs Trading) </h4>
                              <br />
                              <div className="m-1 style-popup fs-6 lh-base">
                                   <p>Dear Valued Customer,</p>
                                   <p>To trade on US (Asian Hrs), you need to acknowledge the <b>Important Note and Risk Disclosure for US (Asian Hrs) Trading</b> first before you can place orders.</p>
                                   <h5><b>Important Note and Risk Disclosure for US (Asian Hrs) Trading</b></h5>
                                   {_renderNoteAndRiskDisctosure()}
                              </div>
                         </div>
                         <div className="m-3 border style-popup">
                              <div className="m-3 fs-6 lh-base">
                                   <h5><b>Risks of Extended Hours Trading </b>(outside of regular US market trading hours)</h5>
                                   <div>(i) Risk of Lower Liquidity </div>
                                   {_renderLowerLiquidity()}
                                   <div className="mt-2">(ii) Risk of Higher Volatility </div>
                                   {_renderLowerVolatility()}
                                   <div className="mt-2">(iii) Risk of Changing Prices </div>
                                   {_renderChangePrice()}
                                   <div className="mt-2">(iv) Risk of Unlinked Markets </div>
                                   {_renderUnlinkedMarket()}
                                   <div className="mt-2">(v) Risk of News Announcements </div>
                                   {_renderNewsAnnouncements()}
                                   <div className="mt-2">(vi) Risk of Wider Spreads </div>
                                   {_renderWiderSpreads()}
                              </div>
                         </div>
                         <div className="m-3 style-popup">
                              <div className="checkbox fs-6 lh-base">
                                   <label>
                                        <input type="checkbox" checked={isChecked} onChange={(event) => setIsChecked(event.target.checked)} value="" className="me-2" />
                                        To trade on US (Asian Hrs), you need to acknowledge the <b>Important Note and Risk Disclosure for US (Asian Hrs) Trading</b> first before you can place orders.
                                   </label>
                              </div>
                         </div>
                         <div className='row mb-3'>
                              <div className="d-flex justify-content-center">

                                   <div className="col-md-1 col-lg-1">
                                        <button disabled={!isChecked} type="button" className="btn btn-sm btn-info br-25" onClick={handleConfirm}>SUBMIT</button>
                                   </div>

                              </div>
                         </div>
                    </div>
               </div>
          </div>
     </div>
     {isShowlazyLoad && LazyLoad()}
     </>
}

export default NotifyFirstLogin;