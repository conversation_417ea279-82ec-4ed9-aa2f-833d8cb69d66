// source: pricing_model.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = (function() {
  if (this) { return this; }
  if (typeof window !== 'undefined') { return window; }
  if (typeof global !== 'undefined') { return global; }
  if (typeof self !== 'undefined') { return self; }
  return Function('return this')();
}.call(null));

goog.exportSymbol('proto.Band', null, global);
goog.exportSymbol('proto.Chart', null, global);
goog.exportSymbol('proto.Quote', null, global);
goog.exportSymbol('proto.RouteQuote', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.Band = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.Band, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.Band.displayName = 'proto.Band';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.Quote = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.Quote.repeatedFields_, null);
};
goog.inherits(proto.Quote, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.Quote.displayName = 'proto.Quote';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.Chart = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.Chart, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.Chart.displayName = 'proto.Chart';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.Band.prototype.toObject = function(opt_includeInstance) {
  return proto.Band.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.Band} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Band.toObject = function(includeInstance, msg) {
  var f, obj = {
    price: jspb.Message.getFieldWithDefault(msg, 1, ""),
    volume: jspb.Message.getFieldWithDefault(msg, 2, ""),
    tradable: jspb.Message.getBooleanFieldWithDefault(msg, 3, false),
    numOrders: jspb.Message.getFieldWithDefault(msg, 4, 0),
    retailVolume: jspb.Message.getFieldWithDefault(msg, 5, 0),
    retailNumOrders: jspb.Message.getFieldWithDefault(msg, 6, 0),
    lpVolume: jspb.Message.getFieldWithDefault(msg, 7, 0),
    lpNumOrders: jspb.Message.getFieldWithDefault(msg, 8, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.Band}
 */
proto.Band.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.Band;
  return proto.Band.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.Band} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.Band}
 */
proto.Band.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setPrice(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setVolume(value);
      break;
    case 3:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setTradable(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setNumOrders(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setRetailVolume(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setRetailNumOrders(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setLpVolume(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setLpNumOrders(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.Band.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.Band.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.Band} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Band.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPrice();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getVolume();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getTradable();
  if (f) {
    writer.writeBool(
      3,
      f
    );
  }
  f = message.getNumOrders();
  if (f !== 0) {
    writer.writeInt64(
      4,
      f
    );
  }
  f = message.getRetailVolume();
  if (f !== 0) {
    writer.writeInt64(
      5,
      f
    );
  }
  f = message.getRetailNumOrders();
  if (f !== 0) {
    writer.writeInt64(
      6,
      f
    );
  }
  f = message.getLpVolume();
  if (f !== 0) {
    writer.writeInt64(
      7,
      f
    );
  }
  f = message.getLpNumOrders();
  if (f !== 0) {
    writer.writeInt64(
      8,
      f
    );
  }
};


/**
 * optional string price = 1;
 * @return {string}
 */
proto.Band.prototype.getPrice = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.Band} returns this
 */
proto.Band.prototype.setPrice = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string volume = 2;
 * @return {string}
 */
proto.Band.prototype.getVolume = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.Band} returns this
 */
proto.Band.prototype.setVolume = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional bool tradable = 3;
 * @return {boolean}
 */
proto.Band.prototype.getTradable = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 3, false));
};


/**
 * @param {boolean} value
 * @return {!proto.Band} returns this
 */
proto.Band.prototype.setTradable = function(value) {
  return jspb.Message.setProto3BooleanField(this, 3, value);
};


/**
 * optional int64 num_orders = 4;
 * @return {number}
 */
proto.Band.prototype.getNumOrders = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.Band} returns this
 */
proto.Band.prototype.setNumOrders = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional int64 retail_volume = 5;
 * @return {number}
 */
proto.Band.prototype.getRetailVolume = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.Band} returns this
 */
proto.Band.prototype.setRetailVolume = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional int64 retail_num_orders = 6;
 * @return {number}
 */
proto.Band.prototype.getRetailNumOrders = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.Band} returns this
 */
proto.Band.prototype.setRetailNumOrders = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional int64 lp_volume = 7;
 * @return {number}
 */
proto.Band.prototype.getLpVolume = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {number} value
 * @return {!proto.Band} returns this
 */
proto.Band.prototype.setLpVolume = function(value) {
  return jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional int64 lp_num_orders = 8;
 * @return {number}
 */
proto.Band.prototype.getLpNumOrders = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/**
 * @param {number} value
 * @return {!proto.Band} returns this
 */
proto.Band.prototype.setLpNumOrders = function(value) {
  return jspb.Message.setProto3IntField(this, 8, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.Quote.repeatedFields_ = [4,5];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.Quote.prototype.toObject = function(opt_includeInstance) {
  return proto.Quote.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.Quote} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Quote.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, 0),
    symbolId: jspb.Message.getFieldWithDefault(msg, 2, 0),
    symbolCode: jspb.Message.getFieldWithDefault(msg, 3, ""),
    asksList: jspb.Message.toObjectList(msg.getAsksList(),
    proto.Band.toObject, includeInstance),
    bidsList: jspb.Message.toObjectList(msg.getBidsList(),
    proto.Band.toObject, includeInstance),
    low: jspb.Message.getFieldWithDefault(msg, 6, ""),
    high: jspb.Message.getFieldWithDefault(msg, 7, ""),
    open: jspb.Message.getFieldWithDefault(msg, 8, ""),
    close: jspb.Message.getFieldWithDefault(msg, 9, ""),
    retailLow: jspb.Message.getFieldWithDefault(msg, 10, ""),
    retailHigh: jspb.Message.getFieldWithDefault(msg, 11, ""),
    retailOpen: jspb.Message.getFieldWithDefault(msg, 12, ""),
    retailClose: jspb.Message.getFieldWithDefault(msg, 13, ""),
    netChange: jspb.Message.getFieldWithDefault(msg, 14, ""),
    pctChange: jspb.Message.getFieldWithDefault(msg, 15, ""),
    scale: jspb.Message.getFieldWithDefault(msg, 16, 0),
    quoteTime: jspb.Message.getFieldWithDefault(msg, 17, 0),
    tickPerDay: jspb.Message.getFieldWithDefault(msg, 18, 0),
    volumePerDay: jspb.Message.getFieldWithDefault(msg, 19, ""),
    currentPrice: jspb.Message.getFieldWithDefault(msg, 20, ""),
    retailPrice: jspb.Message.getFieldWithDefault(msg, 21, ""),
    route: jspb.Message.getFieldWithDefault(msg, 22, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.Quote}
 */
proto.Quote.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.Quote;
  return proto.Quote.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.Quote} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.Quote}
 */
proto.Quote.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSymbolId(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setSymbolCode(value);
      break;
    case 4:
      var value = new proto.Band;
      reader.readMessage(value,proto.Band.deserializeBinaryFromReader);
      msg.addAsks(value);
      break;
    case 5:
      var value = new proto.Band;
      reader.readMessage(value,proto.Band.deserializeBinaryFromReader);
      msg.addBids(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setLow(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setHigh(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setOpen(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setClose(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setRetailLow(value);
      break;
    case 11:
      var value = /** @type {string} */ (reader.readString());
      msg.setRetailHigh(value);
      break;
    case 12:
      var value = /** @type {string} */ (reader.readString());
      msg.setRetailOpen(value);
      break;
    case 13:
      var value = /** @type {string} */ (reader.readString());
      msg.setRetailClose(value);
      break;
    case 14:
      var value = /** @type {string} */ (reader.readString());
      msg.setNetChange(value);
      break;
    case 15:
      var value = /** @type {string} */ (reader.readString());
      msg.setPctChange(value);
      break;
    case 16:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setScale(value);
      break;
    case 17:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setQuoteTime(value);
      break;
    case 18:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setTickPerDay(value);
      break;
    case 19:
      var value = /** @type {string} */ (reader.readString());
      msg.setVolumePerDay(value);
      break;
    case 20:
      var value = /** @type {string} */ (reader.readString());
      msg.setCurrentPrice(value);
      break;
    case 21:
      var value = /** @type {string} */ (reader.readString());
      msg.setRetailPrice(value);
      break;
    case 22:
      var value = /** @type {!proto.RouteQuote} */ (reader.readEnum());
      msg.setRoute(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.Quote.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.Quote.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.Quote} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Quote.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f !== 0) {
    writer.writeInt64(
      1,
      f
    );
  }
  f = message.getSymbolId();
  if (f !== 0) {
    writer.writeInt32(
      2,
      f
    );
  }
  f = message.getSymbolCode();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getAsksList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      4,
      f,
      proto.Band.serializeBinaryToWriter
    );
  }
  f = message.getBidsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      5,
      f,
      proto.Band.serializeBinaryToWriter
    );
  }
  f = message.getLow();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getHigh();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getOpen();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getClose();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getRetailLow();
  if (f.length > 0) {
    writer.writeString(
      10,
      f
    );
  }
  f = message.getRetailHigh();
  if (f.length > 0) {
    writer.writeString(
      11,
      f
    );
  }
  f = message.getRetailOpen();
  if (f.length > 0) {
    writer.writeString(
      12,
      f
    );
  }
  f = message.getRetailClose();
  if (f.length > 0) {
    writer.writeString(
      13,
      f
    );
  }
  f = message.getNetChange();
  if (f.length > 0) {
    writer.writeString(
      14,
      f
    );
  }
  f = message.getPctChange();
  if (f.length > 0) {
    writer.writeString(
      15,
      f
    );
  }
  f = message.getScale();
  if (f !== 0) {
    writer.writeInt32(
      16,
      f
    );
  }
  f = message.getQuoteTime();
  if (f !== 0) {
    writer.writeInt64(
      17,
      f
    );
  }
  f = message.getTickPerDay();
  if (f !== 0) {
    writer.writeInt32(
      18,
      f
    );
  }
  f = message.getVolumePerDay();
  if (f.length > 0) {
    writer.writeString(
      19,
      f
    );
  }
  f = message.getCurrentPrice();
  if (f.length > 0) {
    writer.writeString(
      20,
      f
    );
  }
  f = message.getRetailPrice();
  if (f.length > 0) {
    writer.writeString(
      21,
      f
    );
  }
  f = message.getRoute();
  if (f !== 0.0) {
    writer.writeEnum(
      22,
      f
    );
  }
};


/**
 * optional int64 id = 1;
 * @return {number}
 */
proto.Quote.prototype.getId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.Quote} returns this
 */
proto.Quote.prototype.setId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional int32 symbol_id = 2;
 * @return {number}
 */
proto.Quote.prototype.getSymbolId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.Quote} returns this
 */
proto.Quote.prototype.setSymbolId = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional string symbol_code = 3;
 * @return {string}
 */
proto.Quote.prototype.getSymbolCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.Quote} returns this
 */
proto.Quote.prototype.setSymbolCode = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * repeated Band asks = 4;
 * @return {!Array<!proto.Band>}
 */
proto.Quote.prototype.getAsksList = function() {
  return /** @type{!Array<!proto.Band>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.Band, 4));
};


/**
 * @param {!Array<!proto.Band>} value
 * @return {!proto.Quote} returns this
*/
proto.Quote.prototype.setAsksList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 4, value);
};


/**
 * @param {!proto.Band=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Band}
 */
proto.Quote.prototype.addAsks = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 4, opt_value, proto.Band, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.Quote} returns this
 */
proto.Quote.prototype.clearAsksList = function() {
  return this.setAsksList([]);
};


/**
 * repeated Band bids = 5;
 * @return {!Array<!proto.Band>}
 */
proto.Quote.prototype.getBidsList = function() {
  return /** @type{!Array<!proto.Band>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.Band, 5));
};


/**
 * @param {!Array<!proto.Band>} value
 * @return {!proto.Quote} returns this
*/
proto.Quote.prototype.setBidsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 5, value);
};


/**
 * @param {!proto.Band=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Band}
 */
proto.Quote.prototype.addBids = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 5, opt_value, proto.Band, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.Quote} returns this
 */
proto.Quote.prototype.clearBidsList = function() {
  return this.setBidsList([]);
};


/**
 * optional string low = 6;
 * @return {string}
 */
proto.Quote.prototype.getLow = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.Quote} returns this
 */
proto.Quote.prototype.setLow = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string high = 7;
 * @return {string}
 */
proto.Quote.prototype.getHigh = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.Quote} returns this
 */
proto.Quote.prototype.setHigh = function(value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string open = 8;
 * @return {string}
 */
proto.Quote.prototype.getOpen = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/**
 * @param {string} value
 * @return {!proto.Quote} returns this
 */
proto.Quote.prototype.setOpen = function(value) {
  return jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional string close = 9;
 * @return {string}
 */
proto.Quote.prototype.getClose = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/**
 * @param {string} value
 * @return {!proto.Quote} returns this
 */
proto.Quote.prototype.setClose = function(value) {
  return jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional string retail_low = 10;
 * @return {string}
 */
proto.Quote.prototype.getRetailLow = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/**
 * @param {string} value
 * @return {!proto.Quote} returns this
 */
proto.Quote.prototype.setRetailLow = function(value) {
  return jspb.Message.setProto3StringField(this, 10, value);
};


/**
 * optional string retail_high = 11;
 * @return {string}
 */
proto.Quote.prototype.getRetailHigh = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};


/**
 * @param {string} value
 * @return {!proto.Quote} returns this
 */
proto.Quote.prototype.setRetailHigh = function(value) {
  return jspb.Message.setProto3StringField(this, 11, value);
};


/**
 * optional string retail_open = 12;
 * @return {string}
 */
proto.Quote.prototype.getRetailOpen = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 12, ""));
};


/**
 * @param {string} value
 * @return {!proto.Quote} returns this
 */
proto.Quote.prototype.setRetailOpen = function(value) {
  return jspb.Message.setProto3StringField(this, 12, value);
};


/**
 * optional string retail_close = 13;
 * @return {string}
 */
proto.Quote.prototype.getRetailClose = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 13, ""));
};


/**
 * @param {string} value
 * @return {!proto.Quote} returns this
 */
proto.Quote.prototype.setRetailClose = function(value) {
  return jspb.Message.setProto3StringField(this, 13, value);
};


/**
 * optional string net_change = 14;
 * @return {string}
 */
proto.Quote.prototype.getNetChange = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 14, ""));
};


/**
 * @param {string} value
 * @return {!proto.Quote} returns this
 */
proto.Quote.prototype.setNetChange = function(value) {
  return jspb.Message.setProto3StringField(this, 14, value);
};


/**
 * optional string pct_change = 15;
 * @return {string}
 */
proto.Quote.prototype.getPctChange = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 15, ""));
};


/**
 * @param {string} value
 * @return {!proto.Quote} returns this
 */
proto.Quote.prototype.setPctChange = function(value) {
  return jspb.Message.setProto3StringField(this, 15, value);
};


/**
 * optional int32 scale = 16;
 * @return {number}
 */
proto.Quote.prototype.getScale = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 16, 0));
};


/**
 * @param {number} value
 * @return {!proto.Quote} returns this
 */
proto.Quote.prototype.setScale = function(value) {
  return jspb.Message.setProto3IntField(this, 16, value);
};


/**
 * optional int64 quote_time = 17;
 * @return {number}
 */
proto.Quote.prototype.getQuoteTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 17, 0));
};


/**
 * @param {number} value
 * @return {!proto.Quote} returns this
 */
proto.Quote.prototype.setQuoteTime = function(value) {
  return jspb.Message.setProto3IntField(this, 17, value);
};


/**
 * optional int32 tick_per_day = 18;
 * @return {number}
 */
proto.Quote.prototype.getTickPerDay = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 18, 0));
};


/**
 * @param {number} value
 * @return {!proto.Quote} returns this
 */
proto.Quote.prototype.setTickPerDay = function(value) {
  return jspb.Message.setProto3IntField(this, 18, value);
};


/**
 * optional string volume_per_day = 19;
 * @return {string}
 */
proto.Quote.prototype.getVolumePerDay = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 19, ""));
};


/**
 * @param {string} value
 * @return {!proto.Quote} returns this
 */
proto.Quote.prototype.setVolumePerDay = function(value) {
  return jspb.Message.setProto3StringField(this, 19, value);
};


/**
 * optional string current_price = 20;
 * @return {string}
 */
proto.Quote.prototype.getCurrentPrice = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 20, ""));
};


/**
 * @param {string} value
 * @return {!proto.Quote} returns this
 */
proto.Quote.prototype.setCurrentPrice = function(value) {
  return jspb.Message.setProto3StringField(this, 20, value);
};


/**
 * optional string retail_price = 21;
 * @return {string}
 */
proto.Quote.prototype.getRetailPrice = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 21, ""));
};


/**
 * @param {string} value
 * @return {!proto.Quote} returns this
 */
proto.Quote.prototype.setRetailPrice = function(value) {
  return jspb.Message.setProto3StringField(this, 21, value);
};


/**
 * optional RouteQuote route = 22;
 * @return {!proto.RouteQuote}
 */
proto.Quote.prototype.getRoute = function() {
  return /** @type {!proto.RouteQuote} */ (jspb.Message.getFieldWithDefault(this, 22, 0));
};


/**
 * @param {!proto.RouteQuote} value
 * @return {!proto.Quote} returns this
 */
proto.Quote.prototype.setRoute = function(value) {
  return jspb.Message.setProto3EnumField(this, 22, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.Chart.prototype.toObject = function(opt_includeInstance) {
  return proto.Chart.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.Chart} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Chart.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, 0),
    symbolId: jspb.Message.getFieldWithDefault(msg, 2, 0),
    symbolCode: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.Chart}
 */
proto.Chart.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.Chart;
  return proto.Chart.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.Chart} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.Chart}
 */
proto.Chart.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSymbolId(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setSymbolCode(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.Chart.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.Chart.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.Chart} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Chart.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f !== 0) {
    writer.writeInt64(
      1,
      f
    );
  }
  f = message.getSymbolId();
  if (f !== 0) {
    writer.writeInt32(
      2,
      f
    );
  }
  f = message.getSymbolCode();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional int64 id = 1;
 * @return {number}
 */
proto.Chart.prototype.getId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.Chart} returns this
 */
proto.Chart.prototype.setId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional int32 symbol_id = 2;
 * @return {number}
 */
proto.Chart.prototype.getSymbolId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.Chart} returns this
 */
proto.Chart.prototype.setSymbolId = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional string symbol_code = 3;
 * @return {string}
 */
proto.Chart.prototype.getSymbolCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.Chart} returns this
 */
proto.Chart.prototype.setSymbolCode = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * @enum {number}
 */
proto.RouteQuote = {
  QUOTE_NONE: 0,
  QUOTE_USAH: 1,
  QUOTE_REFINITIV: 2,
  QUOTE_ICE: 3
};

goog.object.extend(exports, proto);
