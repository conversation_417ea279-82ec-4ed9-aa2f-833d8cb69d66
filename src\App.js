import React, { useEffect, useState } from 'react';
import { Route, Routes,BrowserRouter } from 'react-router-dom';
import './styles/index.scss';
import './assets/css/custom.bootstrap.css';
import './assets/css/main.css';
import 'react-toastify/dist/ReactToastify.css';
import 'bootstrap-icons/font/bootstrap-icons.css';
import './App.css';
import './i18n';
import Login from './pages/login';
import { wsService } from './services/websocket-service';
import { SocketKey, THEME_DEFAULT } from './model/constant';
import PopupTimeout from './components/popup-timeout';
import { LocalStorageKey } from './model/constant';
import NotifyFirstLogin from './components/notify-firstlogin';
import jwt_decode from 'jwt-decode';


if (typeof window !== "undefined") {
  	require("bootstrap/dist/js/bootstrap.bundle.min");
}

const loading = (
	<div className="pt-3 text-center">
	  <div className="sk-spinner sk-spinner-pulse"></div>
	</div>
)

const Dashbroad = React.lazy(() => import('./pages/dashbroad'));
const AccountManagement = React.lazy(() => import('./pages/account-management'));
const OrderStatus = React.lazy(() => import('./pages/order-status'));
const Announcement = React.lazy(() => import('./pages/announcement'));

const App = () =>{
	const [isShowExpire, setIsShowExpire] = useState(false);
	const [isShowNotiFirstLogin,  setIsShowNotiFirstLogin] = useState(false);
	const [storedTheme, setStoredTheme] = useState("");
	useEffect(() => {
		// TODO: If using POST method, will use asynchronous to receive token message from POEM 2.0 and update to localStorage. 
		// So need to setTimeout to get token under localStorage to avoid missing data
		setTimeout(() => {
			const ackFlag = JSON.parse(localStorage.getItem(LocalStorageKey.ACK_FLAG));
			setIsShowNotiFirstLogin(!ackFlag);
			const temp = sessionStorage.getItem(LocalStorageKey.THEME) || THEME_DEFAULT;
			setStoredTheme(temp);
		}, 2000)

		const wsConnect = wsService.getSocketSubject().subscribe(resp => {
			if (resp === SocketKey.SOCKET_EXPIRE_TOKEN) {
				setIsShowExpire(true);
				return;
			}
			setIsShowExpire(false);
		 });
		 return () => wsConnect.unsubscribe();
	}, [])

	useEffect(() => {
		const element = window.document.getElementById('body-custom');
		if (isShowNotiFirstLogin) {
			element.classList.add('scroll-none');
		} else {
			element.classList.remove('scroll-none')
		}
	}, [isShowNotiFirstLogin])

	const getIsShowExpire = (isShowPopup) => {
		setIsShowExpire(isShowPopup);
	}
	const getIsCloseNotifirstLogin = (isCloseNoti) => {
		setIsShowNotiFirstLogin(isCloseNoti);
	}

	useEffect(() => {
		document.documentElement.setAttribute("data-theme", storedTheme)
	}, [storedTheme]);
	

	return(
		<>
			{isShowExpire && <PopupTimeout title={'Session Timeout'} setErrorFunc={getIsShowExpire}/>}
			{isShowNotiFirstLogin && <NotifyFirstLogin isCloseNotifirstLogin={getIsCloseNotifirstLogin}/>}
			<BrowserRouter basename={process.env.PUBLIC_URL}>
				<React.Suspense fallback={loading}>
					<Routes>
						<Route exact path="/" element={<Dashbroad/>}/>
						<Route exact path="/dashboard" element={<Dashbroad/>} />
						<Route exact path="/account-management" element={<AccountManagement/>} />
						<Route exact path="/order-status" element={<OrderStatus/>} />
						<Route exact path="/announcement" element={<Announcement/>} />
						{window.enableLoginScreenTest && <Route exact path="/login" element={<Login/>} />}
					</Routes>
				</React.Suspense>
			</BrowserRouter>
		</>
	)
}

export default App;
