import moment from "moment";
import * as FileSaver from 'file-saver';
import { utils, write } from 'xlsx';
import { ORDER_SIDE } from '../model/constant'
import { LocalStorageKey } from "./constant";
import { OrderState } from '../model/proto/trading_model_pb';
import * as stpn from '../model/proto/system_model_pb';
import { MESSAGE_ERROR, DIGIT_DEFAULT } from "../model/constant";
import Decimal from 'decimal.js';
import NumberFormat from "react-number-format";

const systemModel = stpn;
const numberFormat = new Intl.NumberFormat('en-US');
const numberFormatCustom = new Intl.NumberFormat('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2});

export const empty = (string) => {
    return (undefined === string) || (null === string) || (0 === string.trim().length);
}

export const isNull = (value) => {
    return (undefined === value) || (null === value);
}

export const formatISODate = (date) => {
    if (date !== null) {
        let mDateStr = moment(date).format("YYYY-MM-DD");
        return mDateStr;
    } return null;
}

export const formatDate = (datetime) => {
    if (datetime !== null && datetime !== "") {
        let mDateStr = moment(datetime).format("MMM DD YYYY HH:mm:ss");
        return mDateStr;
    } return null;
}

export const formatNumber = (number, digit) => {
    if (number !== null)
        return <NumberFormat value={number} displayType={'text'} thousandSeparator={true} decimalScale={digit} fixedDecimalScale={true} />
    else return null;

};

export const formatCurrencyDecimal = (value, digit = 2) => {
  if (value === 0) return '0';
  if (!value && value !== 0) return '-';

  const num = Number(value);
  if (isNaN(num)) return '-';

  const formatter = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: digit,
    useGrouping: false,
  });

  let formatted = formatter.format(num); 

  formatted = formatted.replace(/(\.\d*?[1-9])0+$/g, '$1'); 
  formatted = formatted.replace(/\.0+$/, '');

  return formatted;
};

export const calcChange = (lastPrice, prevClosePrice) => {
    if (prevClosePrice && lastPrice && lastPrice !== '-') {
        const _newLastPrice = lastPrice.replaceAll(',', '');
        const _newPrevClosePrice = prevClosePrice.replaceAll(',', '');
        const lastPriceValue = new Decimal(_newLastPrice);
        return lastPriceValue.minus(_newPrevClosePrice).toFixed(DIGIT_DEFAULT);
    }
    return '';
}

export const calcFloorPrice = (lastPrice, tickSize, rate) => {
    const floorPriceRaw = lastPrice - (lastPrice * rate)
    const floorPrice = Math.ceil(floorPriceRaw / tickSize) * tickSize
    return formatCurrencyDecimal(floorPrice, DIGIT_DEFAULT)
}

export const calcCeilingPrice = (lastPrice, tickSize, rate) => {
    const ceilingPriceRaw = lastPrice + (lastPrice * rate)
    const ceilingPrice = Math.floor(ceilingPriceRaw / tickSize) * tickSize
    return formatCurrencyDecimal(ceilingPrice, DIGIT_DEFAULT)
}

export const calcCeilFloor = (lastPrice, symbol, symbolMap) => {
    const price = {
        ceiling: 0,
        floor: 0
    };
    const ticker = symbolMap?.get(symbol?.symbolCode);
    if (ticker) {
        const rate = (convertNumber(ticker.limitRate) / 100);
        const tickSize = convertNumber(ticker.tickSize)
        price.ceiling = lastPrice === 0 ? convertNumber(symbol.ceiling) : convertNumber(calcCeilingPrice(lastPrice, tickSize, rate));
        price.floor = lastPrice === 0 ? convertNumber(symbol.floor) : convertNumber(calcFloorPrice(lastPrice, tickSize, rate));
        return price;
    }
    return price;
}

// lastPrice: string, prevClosePrice: string
export const calcPctChange = (lastPrice, prevClosePrice) => {
    if (calcChange(lastPrice, prevClosePrice)) {
        const change = new Decimal(calcChange(lastPrice, prevClosePrice));
        const _newPrevClosePrice = prevClosePrice?.replaceAll(',', '');
        if (convertNumber(_newPrevClosePrice) !== 0) {
            return change.div(_newPrevClosePrice).mul(100).toFixed(DIGIT_DEFAULT);
        }
    }
    return '';
}

export const toTimestamp = (strDate) => {
    const dt = Date.parse(strDate);
    return dt;
}

export const checkValue = (preValue, currentValue) => {
    if (currentValue !== preValue && currentValue.toString() !== '' && currentValue.toString() !== '-') {
        return currentValue;
    }
    return preValue;
}

export const getLastAsksElement = (symbol) => {
    const askList = symbol?.asksList;
    let index = 0;
    while (index < askList?.length) {
        if (convertNumber(askList[index]?.lpNumOrders) > 0) return askList[index];
        else index++;
    }

    return undefined;
}

export const getLastBidsElement = (symbol) => {
    const bidsList = symbol?.bidsList;
    let index = 0;
    while (index < bidsList?.length) {
        if (convertNumber(bidsList[index]?.lpNumOrders) > 0) return bidsList[index];
        else index++;
    }

    return undefined;
}

export const checkPriceListQuoteEvent = (prevList, currentList) => {
    if (currentList && currentList.length > 0) {
        currentList.forEach(item => {
            if (item) {
                const index = prevList.findIndex(o => o?.price === item?.price);
                if (index >= 0) {
                    if (item?.volume === '0' || item?.volume === '-') {
                        prevList.splice(index, 1);
                    } else {
                        prevList[index] = {
                            numOrders: item?.numOrders,
                            price: item?.price,
                            tradable: item?.tradable,
                            volume: item?.volume,
                        }
                    }
                } else {
                    prevList.push({
                        numOrders: item?.numOrders,
                        price: item?.price,
                        tradable: item?.tradable,
                        volume: item?.volume,
                    })
                }
            }
        })
    }
    return prevList;
}

export const defindConfigGet = (param) => {
    const data = {
        headers: { Authorization: `Bearer ${sessionStorage.getItem(LocalStorageKey.TOKEN)}` },
        params: param
    }
    return data;
}

export const defindConfigPost = () => {
    const data = {
        headers: { Authorization: `Bearer ${sessionStorage.getItem(LocalStorageKey.TOKEN)}` }
    }
    return data;
}
export function convertDatetoTimeStamp(value, time) {
    const newDate = `${value} ${time}`
    return Date.parse(newDate);
}

export const exportCSV = (csvData, fileName) => {
    const fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
    const fileExtension = '.xlsx';
    const ws = utils.json_to_sheet(csvData);
    const wb = { Sheets: { 'data': ws }, SheetNames: ['data'] };
    const excelBuffer = write(wb, { bookType: 'xlsx', type: 'array' });
    const data = new Blob([excelBuffer], { type: fileType });
    FileSaver.saveAs(data, fileName + fileExtension);
}

export const getPriceAfterSpread = (price, side, symbolDetail) => {
    const spread = symbolDetail?.spread;
    if (price === 0) {
        return '0'
    }
    if (price !== '-' && spread) {
        if (side === ORDER_SIDE.BUY) {
            return ((Number(price) - Number(spread)).toFixed(DIGIT_DEFAULT)).toString();
        } else {
            return ((Number(price) + Number(spread)).toFixed(DIGIT_DEFAULT)).toString();
        }
    }
    return '-'
}

export const convertNumber = (value) => {
    const tmpValue = value?.toString().replaceAll(',', '');
    if (!isNaN(Number(tmpValue))) {
        return Number(tmpValue);
    }
    return 0;
};

export const convertToLowerCase = (string) => {
    return string ? string.trim().toLowerCase() : '';
}

export const getHigherValue = (value1, value2) => {
    if (convertNumber(value1) > convertNumber(value2)) {
        return value1;
    }
    return value2;
}

export const getLowerValue = (value1, value2) => {
    if (convertNumber(value1) < convertNumber(value2)) {
        return value1;
    }
    return value2;
}

export const getClassState = (state) => {
    switch (state) {
        case OrderState.ORDER_STATE_REJECTED: {
            return 'text-danger';
        }
        case OrderState.ORDER_STATE_FILLED: {
            return 'done';
        }
        case OrderState.ORDER_STATE_PARTIAL: {
            return 'part-done';
        }
        case OrderState.ORDER_STATE_MATCHED: {
            return 'part-done';
        }
        case OrderState.ORDER_STATE_CANCELED: {
            return 'text-danger';
        }
        case OrderState.ORDER_STATE_MODIFIED: {
            return 'text-amend';
        }
        default: {
            return '';
        }
    }
}

export const _renderMessageErrorMinPriceValue = () => {
    return <div className="mb-2 text-center mt-3">
        <p className="mb-0">The order is less than USD</p>
        <p className='text-center mb-0'>{formatCurrency(localStorage.getItem(LocalStorageKey.MIN_ORDER_VALUE))}</p>
        <p className='text-center mb-0'>Kindly revise the number</p>
        <p className='text-center mb-0'>of shares.</p>
    </div>
}

export function formatCurrency(item) {
    if (item && convertNumber(item) !== 0) {
        return numberFormatCustom.format(Number(item));
    }
    return '0';
}

export const renderNumber = (item) => {
    if (item && convertNumber(item) !== 0) {
        return numberFormat.format(Number(item));
    }
    return '0';
}

export const checkMessageError = (msg, msgCode) => {
    if (msgCode === systemModel.MsgCode.MT_RET_ERR_NOT_ENOUGH_MONEY) {
        return msg;
    }
    const messageDisplay = MESSAGE_ERROR.get(msgCode);
    return messageDisplay || msg;
}

export const getKeySortChanged = (listConditionSort, keyItem, defaultData) => {
    return Object.keys(listConditionSort).reduce((result) => {
        return {
            ...result,
            [keyItem]: !listConditionSort[`${keyItem}`],
        }
    }, defaultData);
}

export const getKeyShowSortChanged = (listConditionSort, keyItem, defaultData) => {
    return Object.keys(listConditionSort).reduce((result, key) => {
        return {
            ...result,
            [key]: keyItem === key
        }
    }, defaultData);
}

export const sortAsc = (data, field) => {
    // lấy phần tử đầu tiên của mảng data để kiểm tra kiểu dữ liệu là string hay number
    if (data.length > 0) {
        if (isNaN(Number(data[0][field]))) {
            return data.sort((a, b) => (a[field]?.toString())?.localeCompare(b[field]?.toString()))
        }
    }
    return data.sort((a, b) => a[field] - (b[field]))
}

export const sortDesc = (data, field) => {
    if (data.length > 0) {
        if (isNaN(Number(data[0][field]))) {
            return data.sort((a, b) => (b[field]?.toString())?.localeCompare(a[field]?.toString()))
        }
    }
    return data.sort((a, b) => convertNumber(b[field]) - convertNumber(a[field]))
}

//NOTE: Check invalid LotSize. VolumePlace must be divisible by LotSize
// Eg: checkVolumeLotSize(100, 5) => true
//     checkVolumeLotSize(101, 5) => false
export const checkVolumeLotSize = (placeVol, lotSize) => {
    // NOTE: use convertNumber function to avoid lotSize is empty.
    // When lotSize is empty, new Decimal(lotSize) is exception
    if (lotSize && convertNumber(lotSize) !== 0) {
        const tempVol = new Decimal(placeVol);
        return tempVol.modulo(lotSize).toString() === '0';
    }
    return false;
}

// NOTE: check invalid TickSize. PricePlace must be divisible by TickSize
// Eg: checkPriceTickSize(182.31, 0.03) => true
//     checkPriceTickSize(182.32, 0.03) => false
export const checkPriceTickSize = (placePrice, tickSize) => {
    if (tickSize && convertNumber(tickSize) !== 0) {
        const tempPlacePrice = new Decimal(placePrice);
        return tempPlacePrice.modulo(tickSize).toString() === '0';
    }
    return false;
}

export const stripHtmlTagsFromString = (content) => {
    if (content) {
        return content.replace(/<\/?[^>]+(>|$)/g, "");
    }
    return "";
}