import { useMemo, useState } from 'react'
import { CloseButton } from "react-bootstrap";
import Button from 'react-bootstrap/Button';
import Modal from 'react-bootstrap/Modal';

import { _renderChangePrice, _renderLowerLiquidity, _renderLowerVolatility, _renderNewsAnnouncements, _renderNoteAndRiskDisctosure, _renderUnlinkedMarket, _renderWiderSpreads } from "../constants/notifyFirstLogin";


const RiskStatement = () => {
    const [showNoteRisk, setShowNoteRisk] = useState(false);

    const _renderModalRiskStatement = useMemo(() => {
        return (
            <Modal size="lg" show={showNoteRisk} onHide={() => setShowNoteRisk(false)} animation={false}>
                <Modal.Header onClick={() => setShowNoteRisk(false)} className="pb-0">
                    <CloseButton variant="white" />
                </Modal.Header>
                <Modal.Body className="p-0" >
                    <div >
                        <div className="m-3">
                            <h4>US (Asian Hrs Trading) </h4>
                            <div className="m-1 style-popup fs-6 lh-base mt-4">
                                <h5><b>Important Note and Risk Disclosure for US (Asian Hrs) Trading</b></h5>
                                {_renderNoteAndRiskDisctosure()}
                            </div>
                        </div>
                        <div className="m-3 border style-popup">
                            <div className="m-3 fs-6 lh-base">
                                <h5><b>Risks of Extended Hours Trading </b>(outside of regular US market trading hours)</h5>
                                <div>(i) Risk of Lower Liquidity </div>
                                {_renderLowerLiquidity()}
                                <div className="mt-2">(ii) Risk of Higher Volatility </div>
                                {_renderLowerVolatility()}
                                <div className="mt-2">(iii) Risk of Changing Prices </div>
                                {_renderChangePrice()}
                                <div className="mt-2">(iv) Risk of Unlinked Markets </div>
                                {_renderUnlinkedMarket()}
                                <div className="mt-2">(v) Risk of News Announcements </div>
                                {_renderNewsAnnouncements()}
                                <div className="mt-2">(vi) Risk of Wider Spreads </div>
                                {_renderWiderSpreads()}
                            </div>
                        </div>
                    </div>
                </Modal.Body>
                <Modal.Footer className="pt-0" >
                    <Button variant="secondary" onClick={() => setShowNoteRisk(false)}>
                        Close
                    </Button>
                </Modal.Footer>
            </Modal>
        )
    }, [showNoteRisk])

    return (
        <>
            <div className="mt-0 opacity-75 text-decoration-underline hyper-link" onClick={() => { setShowNoteRisk(true) }}>
                Risk Disclosure Statement
            </div>
            {_renderModalRiskStatement}
        </>
    );
};

export default RiskStatement;