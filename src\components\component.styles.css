.tableFixHead {
    overflow: auto;
}

.tableFixHead thead th {
    position: sticky;
    top: 0;
    z-index: 1;
}

.hidden-scroll {
  overflow-y: hidden !important;
}

.lazy-load .modal .modal-dialog .modal-content {
    background-color: transparent ;
    align-items: center;
}

.lazy-load .modal.show .modal-dialog {
    transform: translateY(-50%);
    top: 50%;
    left: 0;
}

.lds-ring {
    display: block;
    position: relative;
    width: 80px;
    height: 80px;
}
.lds-ring div {
  box-sizing: border-box;
  display: block;
  position: absolute;
  width: 64px;
  height: 64px;
  margin: 8px;
  border: 4px solid #fff;
  border-radius: 50%;
  animation: lds-ring 1.2s cubic-bezier(0.75, 0, 0.5, 1) infinite;
  border-color: #fff transparent transparent transparent;
}
.lds-ring div:nth-child(1) {
  animation-delay: -0.2s;
}
.lds-ring div:nth-child(2) {
  animation-delay: -0.15s;
}
.lds-ring div:nth-child(3) {
  animation-delay: -0.1s;
}
@keyframes lds-ring {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.bg_active {
  background-color: var(--cus-bg-active) !important;
}

.max-height-80 {
  max-height: 80vh;
  height: 80vh;
}

.row-even{
  background-color: var(--cus-bg-table);
}

.ReactVirtualized__Table__row,
.ReactVirtualized__Table__headerRow{
  display: flex !important;
  align-items: center;
}

.ReactVirtualized__Table__headerRow{
  background-color: var(--cus-bg-head-table);
}

.ReactVirtualized__Grid{
  scroll-behavior: smooth;
}

.overflow-unset {
  overflow: unset !important;
}


