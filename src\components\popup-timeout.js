import React from 'react';

const PopupTimeout = (props) => {
    const {title, setErrorFunc} = props;
    return <div className='popup_login'>
        <div className="fade modal-backdrop show"></div>
        <div role="dialog" aria-modal="true" className="fade modal show d-block" tabIndex="-1">
            <div className="modal-dialog">
                <div className="modal-content">
                    <div className="popup_container">
                        <div className="popup_title">
                            {title}
                        </div>
                        <button type="button" className="popup_btn" onClick={() => {setErrorFunc(false)}}>Ok</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

export default React.memo(PopupTimeout)